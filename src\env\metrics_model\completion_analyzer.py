"""
完成率分析接口模块
提供任务完成率相关指标的计算和分析
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np
import logging
from enum import Enum

from src.env.physics_layer.task_tracking import TaskTrackingRecord, TaskStatus, TaskType
from src.env.satellite_cloud.satellite import SatelliteTask, TaskStatus as SatTaskStatus


class Priority(Enum):
    """任务优先级枚举"""
    HIGH = 1
    MEDIUM = 2
    LOW = 3


@dataclass
class CompletionMetrics:
    """完成率指标数据类"""
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    timeout_tasks: int
    dropped_tasks: int
    overall_completion_rate: float
    timely_completion_rate: float
    timeout_rate: float
    failure_rate: float


@dataclass
class PriorityCompletionMetrics:
    """分优先级完成率指标"""
    high_priority: CompletionMetrics
    medium_priority: CompletionMetrics
    low_priority: CompletionMetrics
    weighted_completion_rate: float


class CompletionAnalyzer:
    """完成率分析器"""
    
    def __init__(self, config_path: str):
        """
        初始化完成率分析器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        
        # 优先级权重
        self.priority_weights = {
            Priority.HIGH: 0.5,
            Priority.MEDIUM: 0.3,
            Priority.LOW: 0.2
        }
    
    def analyze_task_records(self, task_records: List[TaskTrackingRecord],
                           time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        分析任务记录的完成率指标
        
        Args:
            task_records: 任务跟踪记录列表
            time_window: 时间窗口(开始时间, 结束时间)
            
        Returns:
            完成率分析结果字典
        """
        if not task_records:
            return self._empty_metrics()
        
        # 过滤时间窗口
        if time_window:
            start_time, end_time = time_window
            filtered_records = [
                r for r in task_records
                if start_time <= r.generation_time <= end_time
            ]
        else:
            filtered_records = task_records
        
        if not filtered_records:
            return self._empty_metrics()
        
        # 统计各状态任务数
        status_counts = self._count_task_status(filtered_records)
        
        # 计算总体完成率指标
        overall_metrics = self._calculate_overall_metrics(filtered_records, status_counts)
        
        # 计算分优先级完成率
        priority_metrics = self._calculate_priority_metrics(filtered_records)
        
        # 计算分任务类型完成率
        type_metrics = self._calculate_type_metrics(filtered_records)
        
        return {
            'overall_metrics': overall_metrics,
            'priority_metrics': priority_metrics,
            'type_metrics': type_metrics,
            'status_distribution': status_counts
        }
    
    def analyze_satellite_tasks(self, satellites_tasks: List[List[SatelliteTask]],
                              time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        分析卫星任务的完成率指标
        
        Args:
            satellites_tasks: 每个卫星的任务列表
            time_window: 时间窗口
            
        Returns:
            完成率分析结果
        """
        # 收集所有任务
        all_tasks = []
        for sat_tasks in satellites_tasks:
            all_tasks.extend(sat_tasks)
        
        if not all_tasks:
            return self._empty_metrics()
        
        # 过滤时间窗口
        if time_window:
            start_time, end_time = time_window
            filtered_tasks = [
                t for t in all_tasks
                if start_time <= t.arrival_time <= end_time
            ]
        else:
            filtered_tasks = all_tasks
        
        if not filtered_tasks:
            return self._empty_metrics()
        
        # 统计任务状态
        status_counts = {
            'completed': 0,
            'dropped': 0,
            'failed': 0,
            'pending': 0,
            'processing': 0
        }
        
        for task in filtered_tasks:
            if task.status == SatTaskStatus.COMPLETED:
                status_counts['completed'] += 1
            elif task.status == SatTaskStatus.DROPPED:
                status_counts['dropped'] += 1
            elif task.status == SatTaskStatus.FAILED:
                status_counts['failed'] += 1
            elif task.status == SatTaskStatus.PENDING:
                status_counts['pending'] += 1
            elif task.status == SatTaskStatus.PROCESSING:
                status_counts['processing'] += 1
        
        total_tasks = len(filtered_tasks)
        
        # 计算完成率指标
        overall_metrics = CompletionMetrics(
            total_tasks=total_tasks,
            completed_tasks=status_counts['completed'],
            failed_tasks=status_counts['failed'],
            timeout_tasks=0,  # 超时任务被计入dropped
            dropped_tasks=status_counts['dropped'],
            overall_completion_rate=status_counts['completed'] / total_tasks if total_tasks > 0 else 0,
            timely_completion_rate=self._calculate_timely_completion_rate(filtered_tasks),
            timeout_rate=status_counts['dropped'] / total_tasks if total_tasks > 0 else 0,
            failure_rate=status_counts['failed'] / total_tasks if total_tasks > 0 else 0
        )
        
        # 分优先级统计
        priority_metrics = self._calculate_satellite_priority_metrics(filtered_tasks)
        
        return {
            'overall_metrics': overall_metrics,
            'priority_metrics': priority_metrics,
            'status_distribution': status_counts,
            'pending_tasks': status_counts['pending'],
            'processing_tasks': status_counts['processing']
        }
    
    def _count_task_status(self, records: List[TaskTrackingRecord]) -> Dict[str, int]:
        """统计任务状态分布"""
        status_counts = {}
        for record in records:
            status_name = record.status.value
            status_counts[status_name] = status_counts.get(status_name, 0) + 1
        return status_counts
    
    def _calculate_overall_metrics(self, records: List[TaskTrackingRecord],
                                  status_counts: Dict[str, int]) -> CompletionMetrics:
        """计算总体完成率指标"""
        total = len(records)
        completed = status_counts.get(TaskStatus.DELIVERED.value, 0)
        failed = status_counts.get(TaskStatus.FAILED.value, 0)
        timeout = status_counts.get(TaskStatus.TIMEOUT.value, 0)
        
        # 计算及时完成率
        timely_completed = sum(
            1 for r in records
            if r.status == TaskStatus.DELIVERED and
            r.delivery_time and r.deadline_timestamp and
            r.delivery_time <= r.deadline_timestamp
        )
        
        return CompletionMetrics(
            total_tasks=total,
            completed_tasks=completed,
            failed_tasks=failed,
            timeout_tasks=timeout,
            dropped_tasks=failed + timeout,
            overall_completion_rate=completed / total if total > 0 else 0,
            timely_completion_rate=timely_completed / total if total > 0 else 0,
            timeout_rate=timeout / total if total > 0 else 0,
            failure_rate=failed / total if total > 0 else 0
        )
    
    def _calculate_priority_metrics(self, records: List[TaskTrackingRecord]) -> PriorityCompletionMetrics:
        """计算分优先级完成率"""
        priority_groups = {
            Priority.HIGH: [],
            Priority.MEDIUM: [],
            Priority.LOW: []
        }
        
        # 分组
        for record in records:
            if record.priority == 1:
                priority_groups[Priority.HIGH].append(record)
            elif record.priority == 2:
                priority_groups[Priority.MEDIUM].append(record)
            else:
                priority_groups[Priority.LOW].append(record)
        
        # 计算各优先级完成率
        priority_metrics = {}
        weighted_sum = 0
        total_weight = 0
        
        for priority, group_records in priority_groups.items():
            if group_records:
                completed = sum(1 for r in group_records if r.status == TaskStatus.DELIVERED)
                total = len(group_records)
                completion_rate = completed / total if total > 0 else 0
                
                priority_metrics[priority] = CompletionMetrics(
                    total_tasks=total,
                    completed_tasks=completed,
                    failed_tasks=sum(1 for r in group_records if r.status == TaskStatus.FAILED),
                    timeout_tasks=sum(1 for r in group_records if r.status == TaskStatus.TIMEOUT),
                    dropped_tasks=sum(1 for r in group_records if r.status in [TaskStatus.FAILED, TaskStatus.TIMEOUT]),
                    overall_completion_rate=completion_rate,
                    timely_completion_rate=self._calculate_timely_rate(group_records),
                    timeout_rate=sum(1 for r in group_records if r.status == TaskStatus.TIMEOUT) / total if total > 0 else 0,
                    failure_rate=sum(1 for r in group_records if r.status == TaskStatus.FAILED) / total if total > 0 else 0
                )
                
                # 加权完成率
                weight = self.priority_weights[priority]
                weighted_sum += completion_rate * weight * total
                total_weight += weight * total
            else:
                priority_metrics[priority] = self._empty_completion_metrics()
        
        weighted_completion = weighted_sum / total_weight if total_weight > 0 else 0
        
        return PriorityCompletionMetrics(
            high_priority=priority_metrics[Priority.HIGH],
            medium_priority=priority_metrics[Priority.MEDIUM],
            low_priority=priority_metrics[Priority.LOW],
            weighted_completion_rate=weighted_completion
        )
    
    def _calculate_type_metrics(self, records: List[TaskTrackingRecord]) -> Dict[str, CompletionMetrics]:
        """计算分任务类型完成率"""
        type_groups = {
            TaskType.REALTIME: [],
            TaskType.NORMAL: [],
            TaskType.COMPUTE_INTENSIVE: []
        }
        
        # 分组
        for record in records:
            task_type = TaskType(record.type_id)
            type_groups[task_type].append(record)
        
        # 计算各类型完成率
        type_metrics = {}
        for task_type, group_records in type_groups.items():
            if group_records:
                completed = sum(1 for r in group_records if r.status == TaskStatus.DELIVERED)
                total = len(group_records)
                
                type_metrics[task_type.name] = CompletionMetrics(
                    total_tasks=total,
                    completed_tasks=completed,
                    failed_tasks=sum(1 for r in group_records if r.status == TaskStatus.FAILED),
                    timeout_tasks=sum(1 for r in group_records if r.status == TaskStatus.TIMEOUT),
                    dropped_tasks=sum(1 for r in group_records if r.status in [TaskStatus.FAILED, TaskStatus.TIMEOUT]),
                    overall_completion_rate=completed / total if total > 0 else 0,
                    timely_completion_rate=self._calculate_timely_rate(group_records),
                    timeout_rate=sum(1 for r in group_records if r.status == TaskStatus.TIMEOUT) / total if total > 0 else 0,
                    failure_rate=sum(1 for r in group_records if r.status == TaskStatus.FAILED) / total if total > 0 else 0
                )
            else:
                type_metrics[task_type.name] = self._empty_completion_metrics()
        
        return type_metrics
    
    def _calculate_timely_rate(self, records: List[TaskTrackingRecord]) -> float:
        """计算及时完成率"""
        if not records:
            return 0
        
        timely = sum(
            1 for r in records
            if r.status == TaskStatus.DELIVERED and
            r.delivery_time and r.deadline_timestamp and
            r.delivery_time <= r.deadline_timestamp
        )
        return timely / len(records)
    
    def _calculate_timely_completion_rate(self, tasks: List[SatelliteTask]) -> float:
        """计算卫星任务的及时完成率"""
        if not tasks:
            return 0
        
        timely = sum(
            1 for t in tasks
            if t.status == SatTaskStatus.COMPLETED and
            t.completion_time and t.deadline and
            t.completion_time <= t.deadline
        )
        return timely / len(tasks)
    
    def _calculate_satellite_priority_metrics(self, tasks: List[SatelliteTask]) -> PriorityCompletionMetrics:
        """计算卫星任务的分优先级完成率"""
        priority_groups = {
            Priority.HIGH: [],
            Priority.MEDIUM: [],
            Priority.LOW: []
        }
        
        # 分组
        for task in tasks:
            if task.priority <= 2:  # 高优先级
                priority_groups[Priority.HIGH].append(task)
            elif task.priority <= 3:  # 中优先级
                priority_groups[Priority.MEDIUM].append(task)
            else:  # 低优先级
                priority_groups[Priority.LOW].append(task)
        
        # 计算各优先级完成率
        priority_metrics = {}
        weighted_sum = 0
        total_weight = 0
        
        for priority, group_tasks in priority_groups.items():
            if group_tasks:
                completed = sum(1 for t in group_tasks if t.status == SatTaskStatus.COMPLETED)
                total = len(group_tasks)
                completion_rate = completed / total if total > 0 else 0
                
                priority_metrics[priority] = CompletionMetrics(
                    total_tasks=total,
                    completed_tasks=completed,
                    failed_tasks=sum(1 for t in group_tasks if t.status == SatTaskStatus.FAILED),
                    timeout_tasks=0,
                    dropped_tasks=sum(1 for t in group_tasks if t.status == SatTaskStatus.DROPPED),
                    overall_completion_rate=completion_rate,
                    timely_completion_rate=self._calculate_timely_completion_rate(group_tasks),
                    timeout_rate=0,
                    failure_rate=sum(1 for t in group_tasks if t.status == SatTaskStatus.FAILED) / total if total > 0 else 0
                )
                
                # 加权完成率
                weight = self.priority_weights[priority]
                weighted_sum += completion_rate * weight * total
                total_weight += weight * total
            else:
                priority_metrics[priority] = self._empty_completion_metrics()
        
        weighted_completion = weighted_sum / total_weight if total_weight > 0 else 0
        
        return PriorityCompletionMetrics(
            high_priority=priority_metrics[Priority.HIGH],
            medium_priority=priority_metrics[Priority.MEDIUM],
            low_priority=priority_metrics[Priority.LOW],
            weighted_completion_rate=weighted_completion
        )
    
    def _empty_completion_metrics(self) -> CompletionMetrics:
        """返回空的完成率指标"""
        return CompletionMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0)
    
    def _empty_metrics(self) -> Dict:
        """返回空指标"""
        return {
            'overall_metrics': self._empty_completion_metrics(),
            'priority_metrics': PriorityCompletionMetrics(
                self._empty_completion_metrics(),
                self._empty_completion_metrics(),
                self._empty_completion_metrics(),
                0
            ),
            'type_metrics': {},
            'status_distribution': {}
        }