# SPACE2 PettingZoo环境设计规范


## 1. 项目背景

### 1.1 研究背景
低轨道(LEO)卫星星座在全球通信、地球观测和科学研究中发挥着越来越重要的作用。随着星座规模的扩大，如何高效地进行任务调度和资源分配成为关键挑战。传统的集中式调度方法面临通信延迟、计算复杂度和单点故障等问题，而分布式多智能体强化学习为解决这些挑战提供了新的思路。

### 1.2 问题定义
SPACE2项目旨在构建一个基于分布式决策的LEO卫星任务调度系统，主要解决以下问题：
- **任务卸载决策**: 卫星如何选择将计算任务在本地处理、卸载到邻居卫星或地面站
- **资源分配优化**: 如何在能量、计算和通信资源约束下最大化任务完成效率
- **协作机制设计**: 多个卫星如何协作以实现全局性能优化

### 1.3 技术路线
采用分布式部分可观测马尔可夫决策过程(Dec-POMDP)理论框架，结合PettingZoo多智能体强化学习环境，构建真实的卫星星座仿真环境。

---

## 2. 理论基础

### 2.1 Dec-POMDP模型映射
将卫星任务调度问题建模为Dec-POMDP，并映射到PettingZoo Parallel框架：

| Dec-POMDP组件 | PettingZoo组件 | SPACE2实例 |
|---------------|----------------|------------|
| 状态空间 $\mathcal{S}$ | 环境内部状态 | 全局卫星状态、轨道位置、任务队列 |
| 智能体集合 $\mathcal{L}$ | self.agents | 72个LEO卫星 |
| 观测空间 $\Omega_j$ | observation_spaces | 局部状态+邻居信息+任务信息 |
| 动作空间 $\mathcal{A}_j$ | action_spaces | 卸载决策+资源分配 |
| 奖励函数 $\mathcal{R}_j$ | rewards | 任务完成+能效+协作奖励 |
| 状态转移 $\mathcal{P}$ | step()函数 | 轨道动力学+任务处理+资源更新 |

### 2.2 系统参数
- **时间离散化**: 2000个时隙
- **智能体数量**: 72个LEO卫星
- **任务优先级**: 3个等级
- **卸载目标**: 本地处理、地面站、邻居卫星、云端

---

## 3. 环境架构设计

### 3.1 核心类定义

```python
class SPACE2Environment(ParallelEnv):
    """
    基于Dec-POMDP的LEO卫星任务调度环境
    
    核心特性:
    - 72个LEO卫星智能体并行决策
    - 2000时隙完整轨道周期仿真
    - 真实物理约束建模
    - 分层奖励机制
    """
    
    def __init__(self, config: EnvironmentConfig):
        # 时间管理
        self.current_timeslot = 0
        self.max_timeslots = 1441
        
        # 智能体定义
        self.agents = [f"satellite_{i}" for i in range(72)]
        self.possible_agents = self.agents[:]
        
        # 全局状态维护
        self.global_state = {
            'satellite_positions': {},    # 卫星位置 p_j(t)
            'energy_levels': {},          # 能量状态 e_j(t)
            'cpu_utilization': {},        # CPU利用率 ρ_j^cpu(t)
            'task_queues': {},            # 任务队列 Q_j(t)
            'link_quality_matrix': None   # 链路质量 L(t)
        }
```

### 3.2 配置管理

```python
@dataclass
class EnvironmentConfig:
    """环境配置参数"""
    num_satellites: int = 72
    max_timeslots: int = 1441
    max_queue_size: int = 10
    max_neighbors: int = 8
    max_tasks: int = 20
    
    # 物理参数
    energy_capacity: float = 100.0
    cpu_capacity: float = 1.0
    communication_range: float = 1000.0  # km
    
    # 奖励参数
    alpha: float = 0.1    # 能效权重
    beta: float = 0.2     # 能耗惩罚
    gamma: float = 0.15   # 负载均衡权重
    delta: float = 0.1    # 协作奖励权重
    zeta: float = 0.05    # 延迟惩罚
```

---

## 4. 空间设计规范

### 4.1 观测空间 (统一规范版本)

```python
def observation_space(self, agent):
    """
    观测空间定义: o_j(t) = {s_j^local(t), N_j(t), T_j^user(t), mask_j(t)}
    
    设计原则:
    - 局部可观测性: 只能观测自身状态和邻居信息
    - 包含action_mask: 指示有效动作，符合RL标准范式
    - 时间上下文: 增强时序决策能力
    """
    return spaces.Dict({
        # 自身状态 s_j^local(t)
        'local_state': spaces.Dict({
            'position': spaces.Box(
                low=-180, high=180, shape=(2,), dtype=np.float32
            ),  # 经纬度坐标 [longitude, latitude]
            'energy': spaces.Box(
                low=0, high=100, shape=(1,), dtype=np.float32
            ),  # 能量百分比
            'cpu_utilization': spaces.Box(
                low=0, high=1, shape=(1,), dtype=np.float32
            ),  # CPU利用率
            'queue_length': spaces.Discrete(self.max_queue_size),
            'time_context': spaces.Box(
                low=0, high=1, shape=(2,), dtype=np.float32
            )  # [当前时隙/总时隙, 轨道相位]
        }),
        
        # 邻居状态 N_j(t)
        'neighbor_states': spaces.Box(
            low=0, high=1,
            shape=(self.max_neighbors, self.neighbor_features),
            dtype=np.float32
        ),  # 可见卫星的状态信息
        
        # 用户任务 T_j^user(t)
        'user_tasks': spaces.Box(
            low=0, high=1,
            shape=(self.max_tasks, self.task_features),
            dtype=np.float32
        ),  # 覆盖区域的任务需求
        
        # 动作掩码 (关键修正)
        'action_mask': spaces.MultiBinary(self.max_queue_size)
        # 指示哪些任务槽位有效，由环境提供给智能体
    })
```

### 4.2 动作空间 (修正版本)

```python
def action_space(self, agent):
    """
    动作空间定义: a_j(t) = <D_j(t), P_j(t)>
    
    设计原则:
    - 结构化复合动作: 离散卸载决策 + 连续资源分配
    - 移除action_mask: 符合RL标准信息流 (环境→智能体)
    - 约束处理: 在环境中验证和修正无效动作
    """
    return spaces.Dict({
        # 卸载策略向量 D_j(t)
        'offloading_decisions': spaces.MultiDiscrete(
            [self.num_offload_targets] * self.max_queue_size,
            dtype=np.int32
        ),  # 0:本地, 1:地面站, 2:邻居卫星, 3:云端
        
        # 资源分配向量 P_j(t)
        'resource_allocation': spaces.Box(
            low=0, high=1,
            shape=(self.max_queue_size,),
            dtype=np.float32
        )   # 每个任务分配的CPU资源比例，总和应≤1
    })
```

---

## 5. 核心算法流程

### 5.1 状态转移机制 (修正因果关系)

```python
def step(self, actions):
    """
    并行环境核心状态转移函数
    
    关键修正:
    - 确保状态更新的因果一致性
    - 明确区分t时刻决策执行和t→t+1状态转移
    - 完善信息字典，支持调试和分析
    """
    # 阶段1: 动作验证 (基于t时刻状态)
    validated_actions = self._validate_actions(actions)
    
    # 阶段2: 决策执行 (基于t时刻状态和链路)
    offloading_decisions = {
        agent: validated_actions[agent]['offloading_decisions']
        for agent in self.agents
    }
    resource_allocations = {
        agent: validated_actions[agent]['resource_allocation']
        for agent in self.agents
    }
    
    # 基于t时刻状态执行决策
    self._execute_offloading(offloading_decisions)
    self._allocate_resources(resource_allocations)
    completed_tasks = self._process_tasks()
    
    # 阶段3: 物理状态转移 t → t+1
    self._update_orbital_dynamics()  # 轨道演进
    self._update_link_quality()      # 基于新位置更新链路
    self._update_energy_states()     # 能量消耗和充电
    self._generate_new_tasks()       # 生成新任务
    
    # 阶段4: 奖励计算和观测生成
    rewards = self._calculate_rewards(completed_tasks, self.global_state)
    observations = self._generate_observations()
    
    # 阶段5: 时间步进和终止检查
    self.current_timeslot += 1
    terminations = {
        agent: self.current_timeslot >= self.max_timeslots
        for agent in self.agents
    }
    truncations = {agent: False for agent in self.agents}
    
    # 阶段6: 信息字典 (调试和分析)
    infos = {
        agent: {
            'completed_tasks': completed_tasks.get(agent, 0),
            'energy_level': self.global_state['energy_levels'][agent],
            'queue_utilization': len(self.global_state['task_queues'][agent]) / self.max_queue_size,
            'valid_neighbors': len(self._get_valid_offload_targets(agent)) - 1
        } for agent in self.agents
    }
    
    return observations, rewards, terminations, truncations, infos
```

### 5.2 奖励计算机制 (修正数学一致性)

```python
def _calculate_rewards(self, completed_tasks, global_state):
    """
    分层奖励函数: r_j(s,a) = r_j^local(s_j,a_j) + r_j^regional(s,a)

    数学一致性保证:
    - local_reward: 依赖智能体j的局部状态s_j和动作a_j
    - regional_reward: 依赖全局状态s和所有智能体动作a
    """
    rewards = {}
    current_state = global_state

    for agent in self.agents:
        # 个体奖励 r_j^local(s_j,a_j)
        local_reward = 0
        priority_weights = {1: 10.0, 2: 6.0, 3: 3.0}

        # 任务完成奖励
        for priority in [1, 2, 3]:
            completed_p = self._count_completed_tasks(agent, priority)
            local_reward += priority_weights[priority] * completed_p

        # 资源效率奖励
        energy_efficiency = self._get_energy_efficiency(agent, current_state)
        local_reward += self.alpha * energy_efficiency

        # 个体惩罚项
        local_reward -= self.beta * self._get_energy_consumption(agent)
        local_reward -= self.zeta * self._get_average_delay(agent)

        # 区域协作奖励 r_j^regional(s,a)
        regional_reward = 0

        # 负载均衡奖励
        load_balance_score = self._get_regional_load_balance(agent, current_state)
        regional_reward += self.gamma * load_balance_score

        # 协作效率奖励
        cooperation_bonus = self._get_cooperation_bonus(agent, completed_tasks)
        regional_reward += self.delta * cooperation_bonus

        # 总奖励
        rewards[agent] = local_reward + regional_reward

    return rewards
```

### 5.3 动作验证机制 (增强版本)

```python
def _validate_actions(self, actions):
    """
    动作有效性验证，确保满足所有约束条件

    验证层次:
    1. 基于action_mask的槽位有效性
    2. 资源分配约束 (总和≤1)
    3. 卸载目标可达性
    """
    validated_actions = {}

    for agent, action in actions.items():
        validated_action = action.copy()

        # 获取有效动作掩码
        action_mask = self._get_action_mask(agent)

        # 1. 基于mask过滤无效槽位
        for i in range(self.max_queue_size):
            if not action_mask[i]:
                validated_action['offloading_decisions'][i] = 0  # 本地处理
                validated_action['resource_allocation'][i] = 0   # 无资源分配

        # 2. 资源分配约束
        resource_sum = np.sum(validated_action['resource_allocation'])
        if resource_sum > 1:
            validated_action['resource_allocation'] *= (1.0 / resource_sum)

        # 3. 卸载目标可达性验证
        valid_targets = self._get_valid_offload_targets(agent)
        for i, target in enumerate(validated_action['offloading_decisions']):
            if action_mask[i] and target not in valid_targets:
                validated_action['offloading_decisions'][i] = 0

        validated_actions[agent] = validated_action

    return validated_actions

def _get_action_mask(self, agent):
    """生成动作掩码，指示有效任务槽位"""
    queue = self.global_state['task_queues'][agent]
    mask = np.zeros(self.max_queue_size, dtype=bool)
    mask[:len(queue)] = True
    return mask
```

---

## 6. 环境初始化

### 6.1 重置流程

```python
def reset(self, seed=None, options=None):
    """
    环境重置到初始状态

    初始化顺序:
    1. 随机种子设置
    2. 时间状态重置
    3. 物理状态初始化
    4. 任务系统初始化
    5. 观测生成
    """
    # 1. 设置随机种子
    self.np_random, seed = seeding.np_random(seed)

    # 2. 时间重置
    self.current_timeslot = 0

    # 3. 物理状态初始化
    self._load_initial_satellite_positions()  # 基于TLE数据
    self._initialize_energy_states()          # 满电状态
    self._compute_initial_link_quality()      # 基于初始位置

    # 4. 任务系统初始化
    self._clear_task_queues()                 # 清空队列
    self._generate_initial_tasks()            # 生成初始任务负载

    # 5. 生成初始观测
    observations = self._generate_observations()

    return observations, {}
```

---

## 7. 关键设计考虑

### 7.1 部分可观测性处理
- **观测范围限制**: 基于卫星间可见性矩阵限制观测范围
- **信息延迟**: 考虑星间通信延迟对观测的影响
- **观测噪声**: 添加现实的传感器噪声模型

### 7.2 时空一致性保证
- **轨道周期性**: 利用轨道周期性优化状态预测
- **时隙同步**: 确保所有智能体在同一时隙决策
- **因果关系**: 保证动作效果的时序因果性

### 7.3 性能优化策略
- **向量化操作**: 使用NumPy广播减少循环计算
- **批处理更新**: 并行处理多智能体状态更新
- **内存管理**: 优化大规模状态空间的内存使用

---

## 8. 实现挑战与解决方案

| 挑战 | 解决方案 | 状态 |
|------|----------|------|
| 动作空间维度动态变化 | 使用最大队列长度+action_mask机制 | ✅ 已修正 |
| 大规模智能体并行计算 | 向量化操作，批处理状态更新 | 🔄 优化中 |
| 长时序依赖 | 在观测中增加time_context | ✅ 已修正 |
| 奖励稀疏性 | 分层奖励机制，包含中间奖励 | ✅ 已修正 |
| 逻辑一致性维护 | 统一设计规范，严格变更管理 | ✅ 已修正 |
| 因果关系正确性 | 明确区分决策执行和状态转移 | ✅ 已修正 |

---

## 9. 验证与测试

### 9.1 单元测试
- **观测空间验证**: 确保观测维度和数据类型正确
- **动作空间验证**: 验证动作约束和有效性检查
- **状态转移测试**: 验证物理模型的正确性

### 9.2 集成测试
- **多智能体交互**: 验证智能体间的协作机制
- **长期仿真**: 测试完整轨道周期的稳定性
- **性能基准**: 与简化模型对比验证复杂度合理性

### 9.3 现实性验证
- **轨道数据对比**: 与真实TLE数据对比验证轨道模型
- **通信模型验证**: 与卫星通信理论模型对比
- **能量模型校准**: 基于真实卫星能耗数据校准

---

## 10. 总结

### 10.1 设计优势
1. **理论基础扎实**: 基于Dec-POMDP理论，数学建模严谨
2. **工程实现规范**: 符合PettingZoo标准，易于集成和扩展
3. **现实约束完整**: 考虑轨道动力学、能量、通信等多重约束
4. **协作机制完善**: 通过分层奖励促进多智能体协作

### 10.2 创新点
- **action_mask正确使用**: 修正了常见的RL范式错误
- **因果关系明确**: 确保状态转移的时序逻辑正确
- **分层奖励设计**: 平衡个体效率和全局协作

### 10.3 后续工作
1. **实现验证**: 基于本规范完成代码实现
2. **算法开发**: 设计适配此环境的MARL算法
3. **性能优化**: 针对大规模仿真进行性能调优
4. **实际部署**: 向真实卫星系统迁移验证

---

## 附录

### A.1 参数配置示例
```python
# 标准配置
standard_config = EnvironmentConfig(
    num_satellites=72,
    max_timeslots=1441,
    max_queue_size=10,
    energy_capacity=100.0,
    alpha=0.1, beta=0.2, gamma=0.15, delta=0.1, zeta=0.05
)

# 小规模测试配置
test_config = EnvironmentConfig(
    num_satellites=8,
    max_timeslots=100,
    max_queue_size=5,
    energy_capacity=50.0
)
```

### A.2 关键常量定义
```python
# 卫星轨道参数
EARTH_RADIUS = 6371.0  # km
LEO_ALTITUDE = 550.0   # km
ORBITAL_PERIOD = 5760  # seconds

# 通信参数
MAX_COMMUNICATION_RANGE = 1000.0  # km
GROUND_STATION_COVERAGE = 500.0   # km

# 任务参数
TASK_PRIORITIES = [1, 2, 3]  # 紧急、重要、普通
OFFLOAD_TARGETS = [0, 1, 2, 3]  # 本地、地面站、邻居、云端
```

---

**文档状态**: 最终版本 | **维护者**: SPACE2项目组 | **最后更新**: 2025-01-19
