"""
调试任务分配问题 - 检查为什么所有任务都分配到南纬
"""

import sys
from pathlib import Path
import numpy as np
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.orbital import OrbitalUpdater

# 初始化
generator = TaskGenerator()
csv_path = Path(__file__).parent.parent.parent / 'src/env/env_data/global_ground_stations.csv'
generator.load_locations_from_csv(str(csv_path))

config_path = Path(__file__).parent.parent.parent / 'src/env/physics_layer/config.yaml'
orbital = OrbitalUpdater(None, str(config_path))

# 测试一个时隙
timeslot = 100
current_time = timeslot * 5

# 获取卫星和可见性矩阵
satellites_dict = orbital.get_satellites_at_time(timeslot)
sg_visibility, sg_distances = orbital.build_satellite_ground_visibility_matrix(satellites_dict, timeslot)

print(f"时隙 {timeslot} 调试信息:")
print(f"卫星数量: {len(satellites_dict)}")
print(f"可见性矩阵形状: {sg_visibility.shape}")
print(f"有可见卫星的地面站数: {np.sum(np.any(sg_visibility, axis=0))}")

# 生成任务并分配
satellite_list = list(satellites_dict.values())
ground_station_list = list(orbital.ground_stations.values())

# 统计每个纬度带的任务分配
lat_band_assignments = {}

# 测试南纬和北纬的陆地站点
south_locations = [loc for loc in generator.locations if loc.geography == 'Land' and loc.latitude < 0][:5]
north_locations = [loc for loc in generator.locations if loc.geography == 'Land' and loc.latitude > 0][:5]
test_locations = south_locations + north_locations

print("\n测试站点:")
print(f"南纬站点: {[loc.location_id for loc in south_locations]}")
print(f"北纬站点: {[loc.location_id for loc in north_locations]}")

for location in test_locations:
    tasks = generator.generate_tasks_for_location(location, current_time)
    
    if len(tasks) > 0:
        ground_id = location.location_id - 1  # 使用location的ID
        
        # 获取该地面站可见的卫星
        visible_satellites = np.where(sg_visibility[:, ground_id])[0]
        
        print(f"\n位置 ID={location.location_id}, 坐标=({location.latitude}, {location.longitude})")
        print(f"  地理类型: {location.geography}, 规模: {location.scale}")
        print(f"  生成任务数: {len(tasks)}")
        print(f"  对应矩阵列索引: {ground_id}")
        
        if ground_id < len(ground_station_list):
            gs = ground_station_list[ground_id]
            print(f"  矩阵中地面站: ID={gs.station_id}, 坐标=({gs.latitude}, {gs.longitude})")
            
        print(f"  可见卫星数: {len(visible_satellites)}")
        
        if len(visible_satellites) > 0:
            # 找最近的卫星
            distances = []
            for sat_idx in visible_satellites:
                sat = satellite_list[sat_idx]
                sat_lat = sat.latitude
                sat_lon = sat.longitude
                ground_lat = location.latitude
                ground_lon = location.longitude
                
                # 计算地理距离
                lat_diff = sat_lat - ground_lat
                lon_diff = sat_lon - ground_lon
                if lon_diff > 180:
                    lon_diff -= 360
                elif lon_diff < -180:
                    lon_diff += 360
                distance = np.sqrt(lat_diff**2 + lon_diff**2)
                distances.append((sat_idx, distance, sat))
            
            # 选择最近的
            best = min(distances, key=lambda x: x[1])
            best_sat_idx, best_dist, best_sat = best
            
            print(f"  最近卫星: 索引={best_sat_idx}, ID={best_sat.satellite_id}")
            print(f"  卫星位置: ({best_sat.latitude:.1f}, {best_sat.longitude:.1f})")
            print(f"  地理距离: {best_dist:.1f}")
            
            # 记录到纬度带
            sat_lat_band = int(best_sat.latitude // 10) * 10
            if sat_lat_band not in lat_band_assignments:
                lat_band_assignments[sat_lat_band] = 0
            lat_band_assignments[sat_lat_band] += len(tasks)

print("\n" + "="*60)
print("纬度带任务分配统计:")
for lat_band in sorted(lat_band_assignments.keys()):
    print(f"  纬度带 [{lat_band}, {lat_band+10}): {lat_band_assignments[lat_band]} 个任务")