# SPACE3环境模块架构文档

> **文档版本**: v3.0.0
> **最后更新**: 2025-08-17
> **项目路径**: D:\paper\space\SPACE3
> **文档类型**: 系统架构与开发指南

---

## 第一部分：系统概览

> **重要说明**: 本文档基于实际代码结构编写，反映SPACE3项目的真实架构。主要特点包括：
> - 三层模块化架构设计
> - 完整的卫星边缘计算仿真系统
> - 基于真实数据的高保真仿真
> - 支持多智能体强化学习算法研究

### 一、项目概述

#### 1.1 项目背景与目标

**SPACE3** 是一个基于LEO卫星星座的边缘-云协同计算仿真平台，专为多智能体强化学习算法研究而设计。该平台提供高保真度的卫星网络仿真环境，支持复杂的任务调度和资源分配算法验证。

**核心目标**：
- 🛰️ **高保真仿真**：模拟72颗LEO卫星的真实轨道动力学和通信特性
- 🌍 **全球覆盖**：支持420个全球分布的地面用户终端和5个云计算中心
- ⏱️ **实时仿真**：2000个时隙（约100分钟）的连续仿真，时隙粒度3秒
- 🤖 **算法研究**：为强化学习、启发式算法等提供标准化测试环境
- 📊 **性能评估**：统一的算法性能指标提取和对比分析框架
- 🔄 **混合仿真**：支持步内离散迭代和多跳任务卸载

**应用场景**：
- 卫星边缘计算任务调度算法研究
- 多智能体协同优化算法验证
- 卫星网络资源分配策略评估
- 边缘-云协同计算性能分析
- 任务分割和并行处理算法验证

#### 1.2 系统架构总览

SPACE3采用三层模块化架构，确保系统的可扩展性、可维护性和高性能：

```
src/env/
├── Foundation_Layer/              # 基础设施层
│   ├── time_manager.py           # 时间管理模块
│   ├── error_handling.py         # 错误处理模块
│   ├── logging_config.py         # 日志配置模块
│   └── __init__.py
├── physics_layer/                # 物理仿真层
│   ├── orbital.py                # 轨道动力学模块
│   ├── communication_refactored.py # 通信链路管理模块
│   ├── task_generator.py         # 任务生成器模块
│   ├── task_distributor.py       # 任务分发器模块
│   ├── task_models.py            # 任务数据模型
│   ├── config.yaml               # 系统配置文件
│   └── __init__.py
├── satellite_cloud/              # 卫星云计算层
│   ├── satellite_compute.py      # 卫星计算资源管理
│   ├── cloud_compute.py          # 云计算资源管理
│   ├── compute_manager.py        # 计算管理器
│   ├── compute_models.py         # 计算数据模型
│   └── __init__.py
├── metrics_model/                # 性能指标分析层
│   ├── algorithm_metrics.py      # 算法指标统一接口
│   ├── latency_analyzer.py       # 延迟分析器
│   ├── energy_analyzer.py        # 能耗分析器
│   ├── completion_analyzer.py    # 完成率分析器
│   ├── load_balancing.py         # 负载均衡分析
│   └── __init__.py
├── visualization/                # 可视化模块
│   ├── api_server.py             # Flask API服务器
│   ├── data_adapter.py           # 数据适配器
│   ├── run_visualization.py      # 启动脚本
│   ├── static/                   # 前端静态文件
│   └── README.md
└── env_data/                     # 环境数据
    ├── satellite_data_72_0.csv   # 72颗LEO卫星×2000时隙 = 144,000条记录
    ├── global_ground_stations.csv # 420个全球地面用户终端
    ├── cloud_station.csv         # 5个云计算中心
    └── regions.json              # 区域配置数据
```

**架构层次关系**：
```
┌─────────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                   │
├─────────────────────────────────────────────────────────────────┤
│  卫星云计算系统  │  性能指标分析  │  可视化系统  │  智能体接口    │
│  satellite_cloud/│ metrics_model/ │visualization/│   (规划中)    │
└─────────────────────────────────────────────────────────────────┘
                                    ↕
┌─────────────────────────────────────────────────────────────────┐
│                     物理仿真层 (Physics Layer)                   │
├─────────────────────────────────────────────────────────────────┤
│  轨道动力学     │  通信链路管理   │  任务生成器    │  任务分发器   │
│  orbital.py     │ communication_  │ task_generator │ task_distributor│
│                 │ refactored.py   │ .py            │ .py           │
└─────────────────────────────────────────────────────────────────┘
                                    ↕
┌─────────────────────────────────────────────────────────────────┐
│                    基础设施层 (Foundation Layer)                 │
├─────────────────────────────────────────────────────────────────┤
│  时间管理       │  错误处理       │  日志配置      │  环境数据     │
│  time_manager   │ error_handling  │ logging_config │ env_data/     │
│  .py            │ .py             │ .py            │               │
└─────────────────────────────────────────────────────────────────┘
```

**架构特点**：
- **三层解耦**：基础设施层 → 物理仿真层 → 应用层
- **模块化设计**：每个模块独立，遵循CLAUDE.md规范
- **数据驱动**：基于真实卫星轨道数据（satellite_data_72_0.csv）
- **性能优化**：使用NumPy矩阵运算和向量化操作
- **混合仿真**：支持步内离散迭代和多跳任务处理

#### 1.3 技术栈与依赖

**核心技术栈**：
```yaml
编程语言:
  - Python 3.10+         # 主要开发语言，遵循PEP8规范
  - YAML                  # 配置文件格式
  - JavaScript/HTML/CSS   # 可视化前端

核心框架:
  - NumPy                # 矩阵运算和向量化计算
  - Pandas               # 卫星轨道数据处理
  - Flask                # Web API服务器
  - Math & Datetime      # 地理坐标系和时间管理

自研仿真引擎:
  - 轨道动力学引擎       # ECEF坐标系，支持三种可见性矩阵
  - 通信模型引擎         # 5种链路类型，真实物理参数
  - DPSQ调度引擎         # 动态优先级评分调度算法
  - 混合仿真引擎         # 支持步内离散迭代和多跳处理

数据存储:
  - CSV                 # 卫星轨道数据 (144,000条记录)
  - CSV                 # 地面站数据 (420个全球分布点)
  - CSV                 # 云计算中心数据 (5个节点)
  - JSON                # 任务数据和结果输出

开发工具:
  - 自建测试框架         # 按时隙验证的测试方法
  - Python Logging      # 统一的日志管理系统
  - Type Hints          # 数据类和类型注解
  - 3D可视化系统         # 基于Web的实时仿真可视化
```

**数据依赖和规模**：
```
src/env/env_data/
├── satellite_data_72_0.csv       # 72颗LEO卫星×2000时隙 = 144,000条记录
├── global_ground_stations.csv    # 420个全球地面用户终端
├── cloud_station.csv             # 5个云计算中心
└── regions.json                  # 区域配置数据

仿真规模:
- 总时隙数: 2000 (约 6000 秒 / 100分钟仿真)
- 时隙粒度: 3秒/时隙
- 可见性矩阵: 卫星-卫星(72×72), 卫星-地面(72×420), 卫星-云(72×5)
- 距离阈值:
  - 卫星间可见性: 5500km
  - 卫星-地面可见性: 2500km
  - 卫星-云可见性: 3300km
- 混合仿真: 支持最大3跳任务卸载，3轮微观逻辑迭代
```

### 二、模块完成状态

#### 2.1 完成状态总览

| 分层 | 模块名称 | 文件路径 | 完成状态 | 版本 | 测试覆盖 |
|------|---------|---------|---------|------|---------|
| **基础设施层** | | | | | |
| | 时间管理 | `src/env/Foundation_Layer/time_manager.py` | ✅ 完成 | v1.0 | ✅ |
| | 错误处理 | `src/env/Foundation_Layer/error_handling.py` | ✅ 完成 | v1.0 | ✅ |
| | 日志配置 | `src/env/Foundation_Layer/logging_config.py` | ✅ 完成 | v1.0 | ✅ |
| | 环境数据 | `src/env/env_data/` | ✅ 完成 | v1.0 | ✅ |
| **物理仿真层** | | | | | |
| | 轨道动力学 | `src/env/physics_layer/orbital.py` | ✅ 完成 | v2.0 | ✅ |
| | 通信链路管理 | `src/env/physics_layer/communication_refactored.py` | ✅ 完成 | v2.0 | ✅ |
| | 任务生成器 | `src/env/physics_layer/task_generator.py` | ✅ 完成 | v1.0 | ✅ |
| | 任务分发器 | `src/env/physics_layer/task_distributor.py` | ✅ 完成 | v1.0 | ✅ |
| | 任务数据模型 | `src/env/physics_layer/task_models.py` | ✅ 完成 | v1.0 | ✅ |
| | 配置管理 | `src/env/physics_layer/config.yaml` | ✅ 完成 | v2.0 | ✅ |
| **卫星云计算层** | | | | | |
| | 卫星计算管理 | `src/env/satellite_cloud/satellite_compute.py` | ✅ 完成 | v1.0 | ✅ |
| | 云计算管理 | `src/env/satellite_cloud/cloud_compute.py` | ✅ 完成 | v1.0 | ✅ |
| | 计算管理器 | `src/env/satellite_cloud/compute_manager.py` | ✅ 完成 | v1.0 | ✅ |
| | 计算数据模型 | `src/env/satellite_cloud/compute_models.py` | ✅ 完成 | v1.0 | ✅ |
| **性能指标分析层** | | | | | |
| | 算法指标接口 | `src/env/metrics_model/algorithm_metrics.py` | ✅ 完成 | v1.0 | ✅ |
| | 延迟分析器 | `src/env/metrics_model/latency_analyzer.py` | ✅ 完成 | v1.0 | ✅ |
| | 能耗分析器 | `src/env/metrics_model/energy_analyzer.py` | ✅ 完成 | v1.0 | ✅ |
| | 完成率分析器 | `src/env/metrics_model/completion_analyzer.py` | ✅ 完成 | v1.0 | ✅ |
| | 负载均衡分析 | `src/env/metrics_model/load_balancing.py` | ✅ 完成 | v1.0 | ✅ |
| **可视化系统** | | | | | |
| | API服务器 | `src/env/visualization/api_server.py` | ✅ 完成 | v1.0 | ✅ |
| | 数据适配器 | `src/env/visualization/data_adapter.py` | ✅ 完成 | v1.0 | ✅ |
| | 启动脚本 | `src/env/visualization/run_visualization.py` | ✅ 完成 | v1.0 | ✅ |
| | 前端界面 | `src/env/visualization/static/` | ✅ 完成 | v1.0 | ✅ |
| **智能体接口** | | | | | |
| | PettingZoo环境接口 | `src/env/space3_env.py` | 📋 规划中 | - | - |

**图例说明**：
- ✅ 完成：功能完整，测试通过
- 🔄 开发中：正在开发，部分功能可用
- 📋 规划中：已规划，尚未开始开发
- ⚠️ 需要完善：基本功能完成，需要补充测试或文档

#### 2.2 版本管理

**版本命名规范**：
- **主版本号**：重大架构变更或不兼容更新
- **次版本号**：新功能添加或重要改进
- **修订版本号**：Bug修复或小幅优化

**当前版本状态**：
```yaml
系统整体版本: v3.0.0
├── 基础设施层: v1.0 (稳定) - 时间管理、错误处理、日志配置完成
├── 物理仿真层: v2.0 (稳定) - 轨道动力学、通信、任务生成和分发完成
├── 卫星云计算层: v1.0 (完成) - DPSQ调度、云计算、计算管理器完成
├── 性能指标分析层: v1.0 (完成) - 多维度性能分析系统完成
└── 可视化系统: v1.0 (完成) - Web可视化界面和API服务器完成

```

#### 2.3 开发路线图


---

## 第二部分：模块详细设计

### 一、基础设施层 (Foundation Layer)

基础设施层提供核心基础服务，为上层模块提供统一的时间、错误处理和日志服务。

#### 1.1 时间管理模块 (time_manager.py)

**功能描述**：
- 统一仿真时间和物理时间的转换
- 提供标准化的时间查询接口
- 解决项目中时间处理不一致的问题

**核心类**：
- `TimeManager`: 主要时间管理类
- `TimeContext`: 时间上下文数据类
- `create_time_manager_from_config()`: 从配置创建管理器

**设计特点**：
```python
# 统一的时间上下文
@dataclass
class TimeContext:
    simulation_step: int      # 仿真步数 (0-based)
    simulation_time: float    # 仿真时间 (秒)
    physical_time: datetime   # 对应的物理时间
    timeslot_duration: float  # 时隙持续时间 (秒)
```

#### 1.2 错误处理模块 (error_handling.py)

**功能描述**：
- 统一的异常处理机制
- 结构化的错误管理框架
- 支持分级错误处理策略

#### 1.3 日志配置模块 (logging_config.py)

**功能描述**：
- 全局统一的日志配置
- 支持debug/info/error多级别输出
- 统一的日志格式和输出方式

#### 1.4 环境数据 (env_data/)

**数据结构**：
```
env_data/
├── satellite_data_72_0.csv       # 格式: 卫星ID,时隙,时间,纬度,经度,光照,状态
├── global_ground_stations.csv    # 全球420个地面用户终端位置
├── cloud_station.csv             # 5个云计算中心位置和配置
└── regions.json                  # 区域配置数据
```

### 二、物理仿真层 (Physics Layer)

物理仿真层实现核心的物理建模组件，包括轨道动力学、通信链路、任务生成和分发等。

#### 2.1 轨道动力学模块 (orbital.py) - v2.0

**功能描述**：
- 基于ECEF坐标系的三维距离计算
- 支持三种可见性矩阵：卫星-卫星、卫星-地面、卫星-云
- 向量化NumPy操作，支持大规模矩阵计算

**核心类**：
- `OrbitalUpdater`: 轨道更新器主类
- `Satellite`: 卫星数据类
- `GroundStation`: 地面站数据类

**技术特点**：
```python
# 支持三种可见性计算
- 卫星间可见性: 5500 km阈值，72×72 矩阵
- 卫星-地面可见性: 2500 km阈值，72×420 矩阵
- 卫星-云可见性: 3300 km阈值，72×5 矩阵
```

#### 2.2 通信链路管理 (communication_refactored.py) - v2.0

**功能描述**：
- 支持5种链路类型的通信性能计算
- 真实物理参数建模（路径损耗、多普勒效应等）
- 与轨道模块集成，提供动态通信能力
- 支持混合仿真模式的通信建模

**核心类**：
- `CommunicationManager`: 通信管理器主类

#### 2.3 任务生成器 (task_generator.py)

**功能描述**：
- 基于区域任务生成模型
- 为420个地理坐标点生成任务数据
- 支持多种任务类型和优先级配置
- 集成地理位置和功能类型的任务分布

**核心类**：
- `TaskGenerator`: 任务生成器主类
- `Location`: 地理位置节点类
- `TaskGenerationConfig`: 任务生成配置类

#### 2.4 任务分发器 (task_distributor.py)

**功能描述**：
- 基于可见性的任务分配算法
- 支持重试机制和失败处理
- 距离优化的卫星选择策略
- 任务分配状态跟踪

**核心类**：
- `TaskDistributor`: 任务分发器主类
- `TaskAssignment`: 任务分配记录类
- `DistributionMetrics`: 分发性能指标类

#### 2.5 任务数据模型 (task_models.py)

**功能描述**：
- 统一的任务数据结构定义
- 支持多种任务类型和状态
- 地理位置和功能类型枚举
- 任务生成配置管理

**核心类**：
- `Task`: 任务数据类
- `TaskType`, `GeographyType`, `ScaleType`: 任务分类枚举
- `TaskGenerationConfig`: 任务生成配置类

### 三、卫星云计算层 (Satellite Cloud Layer)

卫星云计算层实现计算资源管理和任务调度算法。

#### 3.1 卫星计算管理 (satellite_compute.py)

**功能描述**：
- 实现DPSQ（动态优先级评分调度）算法
- 支持多任务并行处理和CPU分配
- 能量约束下的任务调度
- 跨时隙任务状态管理

**核心类**：
- `SatelliteCompute`: 卫星计算资源管理器
- `DPSQScheduler`: 动态优先级评分调度器

**技术特点**：
```python
# DPSQ算法公式
Score(T_i, t_now) = w_p * f_p(P_i) + w_d * f_d(D_i, t_now) - w_c * f_c(S_i, C_i)

# 支持并行处理
- CPU分配档位: 10%-100%，步长10%
- 最大并行任务数: 200
- 能量约束管理: 电池容量3.6MJ，太阳能板5kW
```

#### 3.2 云计算管理 (cloud_compute.py)

**功能描述**：
- 高性能云计算资源仿真
- 无能量约束的大规模并行处理
- 与卫星计算的协同调度
- 任务卸载和结果回传管理

**核心类**：
- `CloudCompute`: 云计算资源管理器

#### 3.3 计算管理器 (compute_manager.py)

**功能描述**：
- 统一的计算资源调度接口
- 卫星-云协同计算决策
- 任务分割和并行处理支持
- 性能监控和资源优化

**核心类**：
- `ComputeManager`: 计算管理器主类

#### 3.4 计算数据模型 (compute_models.py)

**功能描述**：
- 计算任务和节点的数据结构
- 任务状态和处理结果管理
- 资源指标和性能数据
- 卫星和云状态建模

**核心类**：
- `ComputeTask`: 计算任务数据类
- `ProcessingNode`: 处理节点信息类
- `SatelliteState`, `CloudState`: 节点状态类

### 四、性能指标分析层 (Metrics Model Layer)

性能指标分析层提供算法性能评估的统一接口和多维度分析能力。

#### 4.1 算法指标接口 (algorithm_metrics.py)

**功能描述**：
- 为算法对比研究提供标准化的性能评估接口
- 支持多维度指标分析（延迟、能耗、完成率等）
- 统一的数据输出和可视化接口
- 归一化指标设计消除负载差异

**核心类**：
- `AlgorithmMetrics`: 算法指标统一接口
- `AlgorithmResult`: 算法运行结果数据类

#### 4.2 延迟分析器 (latency_analyzer.py)

**功能描述**：
- 端到端延迟分析
- 排队延迟、处理延迟、传输延迟分解
- 延迟分布统计和异常检测

**核心类**：
- `LatencyAnalyzer`: 延迟分析器主类

#### 4.3 能耗分析器 (energy_analyzer.py)

**功能描述**：
- 单位任务能耗分析
- 能耗密度和能效比计算
- 能量利用率优化建议

**核心类**：
- `EnergyAnalyzer`: 能耗分析器主类

#### 4.4 完成率分析器 (completion_analyzer.py)

**功能描述**：
- 按优先级、任务类型分组的完成率统计
- 超时率和失败率分析
- 时间窗口完成率趋势

**核心类**：
- `CompletionAnalyzer`: 完成率分析器主类

#### 4.5 负载均衡分析 (load_balancing.py)

**功能描述**：
- 卫星间负载分布分析
- 负载均衡度量和优化建议
- 资源利用率统计

**核心类**：
- `LoadBalancingAnalyzer`: 负载均衡分析器主类

### 五、可视化系统 (Visualization System)

可视化系统提供Web界面的实时仿真监控和数据展示。

#### 5.1 API服务器 (api_server.py)

**功能描述**：
- Flask Web API服务器
- 实时数据接口和WebSocket支持
- 仿真控制和状态查询接口

**核心类**：
- Flask应用和路由定义

#### 5.2 数据适配器 (data_adapter.py)

**功能描述**：
- 仿真数据到可视化格式的转换
- 实时数据缓存和更新机制
- 多种数据源的统一接口

**核心类**：
- `VisualizationDataAdapter`: 数据适配器主类

#### 5.3 前端界面 (static/)

**功能描述**：
- 3D地球视图和卫星轨道显示
- 实时任务流和性能监控
- 交互式参数调整和仿真控制

**技术栈**：
- HTML5/CSS3/JavaScript
- 3D图形库和数据可视化组件

### 六、智能体接口 (Agent Interface) - 规划中

#### 6.1 PettingZoo环境接口 (space3_env.py) - 规划中

**规划功能**：
- 实现PettingZoo标准接口
- 支持多智能体强化学习算法
- 提供观察空间和动作空间定义
- 集成奖励函数和环境重置机制

### 七、模块间交互关系

#### 7.1 数据流向

```
基础设施层 (Foundation Layer)
    │ TimeManager → 提供时间上下文
    │ Logger → 提供日志服务
    │ ErrorHandler → 提供异常处理
    │ env_data/ → 提供基础数据
    │
    ↓ (依赖关系)
物理仿真层 (Physics Layer)
    │ OrbitalUpdater → 计算卫星位置和可见性矩阵
    │ CommunicationManager → 计算通信性能矩阵
    │ TaskGenerator → 生成任务数据
    │ TaskDistributor → 分发任务到卫星
    │
    ↓ (依赖关系)
卫星云计算层 (Satellite Cloud Layer)
    │ SatelliteCompute → DPSQ调度和任务处理
    │ CloudCompute → 云计算资源管理
    │ ComputeManager → 统一计算资源调度
    │
    ↓ (依赖关系)
性能指标分析层 (Metrics Model Layer)
    │ AlgorithmMetrics → 统一性能分析接口
    │ LatencyAnalyzer → 延迟分析
    │ EnergyAnalyzer → 能耗分析
    │ CompletionAnalyzer → 完成率分析
    │
    ↓ (依赖关系)
可视化系统 (Visualization System)
    │ API Server → Web接口服务
    │ DataAdapter → 数据格式转换
    └ Frontend → 用户界面展示
```

#### 7.2 关键接口

**时间管理接口**：
- `TimeManager.get_context(step)`: 获取指定步数的时间上下文
- `TimeContext`: 所有模块都使用统一的时间上下文

**轨道-通信接口**：
- `OrbitalUpdater.get_visibility_matrices()`: 提供可见性矩阵
- `CommunicationManager.calculate_communication_matrix()`: 提供通信性能矩阵

**任务处理接口**：
- `TaskGenerator.generate_tasks_for_timeslot()`: 生成时隙任务
- `TaskDistributor.distribute_tasks()`: 分发任务到卫星
- `SatelliteCompute.add_task()`: 添加任务到卫星队列
- `ComputeManager.process_timeslot()`: 统一时隙处理

**性能分析接口**：
- `AlgorithmMetrics.analyze()`: 统一的性能分析入口
- 支持多种数据源的性能指标提取和分析

**可视化接口**：
- `VisualizationDataAdapter.get_real_time_data()`: 获取实时仿真数据
- Flask API路由: `/api/satellites`, `/api/tasks`, `/api/metrics`

---

## 第三部分：集成与协作

### 一、数据流图与模块交互

#### 1.1 系统数据流图

```mermaid
graph TD
    A[env_data/*.csv] --> B[Foundation Layer]
    B --> C[Physics Layer]
    C --> D[Application Layer]
    
    subgraph "Foundation Layer"
        B1[TimeManager]
        B2[ErrorHandler]
        B3[LoggingManager]
        B4[ConfigManager]
    end
    
    subgraph "Physics Layer"
        C1[OrbitalUpdater]
        C2[CommunicationManager]
        C3[TaskGenerator]
        C4[TaskTracking]
    end
    
    subgraph "Application Layer"
        D1[Satellite DPSQ]
        D2[MetricsModel]
        D3[PettingZoo Interface]
    end
    
    B1 --> C1
    B1 --> C2
    C1 --> C2
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    D1 --> D2
```

#### 1.2 核心数据结构流转

**时间上下文流转**：
```python
# 1. TimeManager创建时间上下文
time_context = TimeContext(
    simulation_step=100,
    simulation_time=500.0,
    physical_time=datetime(2025, 6, 8, 4, 8, 20),
    timeslot_duration=5.0
)

# 2. OrbitalUpdater使用时间上下文
orbital_updater.update_positions(time_context)

# 3. Satellite调度器使用相同时间上下文
satellite.process_timeslot(time_context)
```

**可见性矩阵流转**：
```python
# 1. OrbitalUpdater计算可见性矩阵
visibility_matrices = orbital_updater.get_visibility_matrices(timeslot)
# 返回：{
#   'satellite_to_satellite': (72, 72),
#   'satellite_to_ground': (72, 420), 
#   'satellite_to_cloud': (72, 5)
# }

# 2. CommunicationManager使用可见性矩阵计算通信性能
comm_matrix = communication_manager.calculate_communication_matrix(
    visibility_matrices, time_context
)

# 3. Satellite使用通信矩阵更新带宽
satellite.update_bandwidth_from_communication(comm_matrix)
```

### 二、使用场景与集成示例

#### 2.1 完整仿真运行流程

```python
# 典型的SPACE2仿真运行示例
import yaml
from src.env.Foundation_Layer.time_manager import create_time_manager_from_config
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.satellite_cloud.satellite import Satellite
from src.env.metrics_model.algorithm_metrics import AlgorithmMetrics

def run_space2_simulation():
    # 1. 初始化基础设施层
    initialize_logging_and_config("src/env/physics_layer/config.yaml")
    
    # 2. 加载配置
    with open("src/env/physics_layer/config.yaml", 'r') as f:
        config = yaml.safe_load(f)
    
    # 3. 创建时间管理器
    time_manager = create_time_manager_from_config(config)
    
    # 4. 初始化物理仿真层
    orbital_updater = OrbitalUpdater()
    communication_manager = CommunicationManager(config)
    
    # 5. 初始化卫星集群
    satellites = []
    for sat_id in range(config['system']['num_leo_satellites']):
        satellite = Satellite(
            satellite_id=f"sat_{sat_id:02d}",
            config=config,
            orbital_updater=orbital_updater,
            communication_manager=communication_manager
        )
        satellites.append(satellite)
    
    # 6. 仿真主循环
    results = []
    for step in range(config['system']['total_timeslots']):
        # 获取时间上下文
        time_context = time_manager.get_context(step)
        
        # 更新轨道状态
        orbital_updater.update_positions(time_context)
        
        # 计算通信矩阵
        visibility_matrices = orbital_updater.get_visibility_matrices(step)
        comm_matrix = communication_manager.calculate_communication_matrix(
            visibility_matrices, time_context
        )
        
        # 执行卫星任务处理
        for satellite in satellites:
            satellite.update_bandwidth_from_communication(comm_matrix)
            result = satellite.process_timeslot(time_context)
            results.append(result)
    
    # 7. 性能分析
    metrics = AlgorithmMetrics()
    analysis_result = metrics.analyze(results, "DPSQ_Algorithm")
    
    return analysis_result

# 运行仿真
if __name__ == "__main__":
    result = run_space2_simulation()
    print(f"仿真完成，处理任务: {result.tasks_completed}")
```

#### 2.2 单模块测试示例

**轨道模块测试**：
```python
def test_orbital_module():
    from src.env.physics_layer.orbital import OrbitalUpdater
    from src.env.Foundation_Layer.time_manager import TimeContext
    from datetime import datetime
    
    # 创建轨道更新器
    updater = OrbitalUpdater()
    
    # 测试时间上下文
    context = TimeContext(
        simulation_step=100,
        simulation_time=500.0,
        physical_time=datetime(2025, 6, 8, 4, 8, 20),
        timeslot_duration=5.0
    )
    
    # 更新位置
    updater.update_positions(context)
    
    # 获取可见性矩阵
    matrices = updater.get_visibility_matrices(100)
    
    # 验证矩阵尺寸
    assert matrices['satellite_to_satellite'].shape == (72, 72)
    assert matrices['satellite_to_ground'].shape == (72, 420)
    assert matrices['satellite_to_cloud'].shape == (72, 5)
    
    print("轨道模块测试通过")
```

#### 2.3 错误处理集成示例

```python
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError

@handle_errors(module="simulation", function="main_loop")
def simulation_with_error_handling():
    try:
        # 仿真主逻辑
        run_space2_simulation()
    except Exception as e:
        # 错误会被自动处理和记录
        raise SpaceSimulationError(
            message=f"仿真执行失败: {str(e)}",
            error_code="SIMULATION_FAILURE"
        )
```

### 三、扩展开发指南


---

## 附录A：API参考手册

### A.1 基础设施层API

#### TimeManager类
```python
class TimeManager:
    def __init__(self, config: Dict[str, Any])
    def get_context(self, step: int) -> TimeContext
    def convert_to_physical_time(self, simulation_time: float) -> datetime
    def is_valid_step(self, step: int) -> bool
    
    # 静态方法
    @staticmethod
    def create_from_config(config: Dict) -> 'TimeManager'
```

#### ErrorHandler类
```python
class ErrorHandler:
    def handle_error(self, error: SpaceSimulationError, context: ErrorContext) -> bool
    def get_error_stats(self) -> Dict[str, Any]
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]
    
    # 装饰器
    @handle_errors(module: str, function: str)
```

#### LoggingManager类
```python
class LoggingManager:
    def __init__(self, config: Dict[str, Any])
    def get_logger(self, name: str) -> logging.Logger
    def set_module_log_level(self, module_name: str, level: str)
    def create_performance_logger(self) -> logging.Logger
    def cleanup_old_logs(self, days: int = 7)
```

### A.2 物理仿真层API

#### OrbitalUpdater类
```python
class OrbitalUpdater:
    def __init__(self, data_file: str = "src/env/env_data/satellite_data72_1.csv")
    def update_positions(self, time_context: TimeContext)
    def get_visibility_matrices(self, timeslot: int) -> Dict[str, np.ndarray]
    def get_satellite_position(self, satellite_id: int, timeslot: int) -> Tuple[float, float, float]
    def calculate_distance_matrix(self, positions1: np.ndarray, positions2: np.ndarray) -> np.ndarray
```

#### CommunicationManager类
```python
class CommunicationManager:
    def __init__(self, config: Dict[str, Any])
    def calculate_communication_matrix(self, visibility_matrices: Dict, time_context: TimeContext) -> np.ndarray
    def get_link_quality(self, sat1_id: int, sat2_id: int, timeslot: int) -> float
    def calculate_data_rate(self, distance: float, link_type: str) -> float
```

#### TaskGenerator类
```python
class TaskGenerator:
    def __init__(self, config: Dict[str, Any])
    def generate_tasks_for_timeslot(self, timeslot: int) -> List[Task]
    def generate_task_for_location(self, location: Location, timeslot: int) -> Task
    def get_task_statistics(self) -> Dict[str, Any]
```

### A.3 应用层API

#### Satellite类
```python
class Satellite:
    def __init__(self, satellite_id: str, config: Dict, orbital_updater: OrbitalUpdater, communication_manager: CommunicationManager)
    def process_timeslot(self, time_context: TimeContext) -> Dict[str, Any]
    def add_task(self, task: SatelliteTask)
    def update_bandwidth_from_communication(self, comm_matrix: np.ndarray)
    def get_status(self) -> Dict[str, Any]
    def handle_timeslot_boundary(self, time_context: TimeContext)
```

#### AlgorithmMetrics类
```python
class AlgorithmMetrics:
    def __init__(self)
    def analyze(self, results: List[Dict], algorithm_name: str) -> AlgorithmResult
    def add_custom_analyzer(self, name: str, analyzer: BaseAnalyzer)
    def get_available_metrics(self) -> List[str]
```

---

---

## 附录C：数据格式规范

### C.1 卫星轨道数据格式 (satellite_data_72_0.csv)

**文件描述**：包含72颗LEO卫星在2000个时隙的位置数据

**格式规范**：
```csv
卫星ID,时隙,时间,纬度,经度,光照,状态
111,1,04:00:00,0.0,-24.807,FALSE,TRUE
112,1,04:00:00,37.918,1.758,TRUE,TRUE
113,1,04:00:00,60.14,65.193,TRUE,TRUE
...
```

**字段说明**：
| 字段名 | 类型 | 说明 | 范围/值 |
|--------|------|------|---------|
| `卫星ID` | int | 卫星ID | 111-188 (对应72颗卫星) |
| `时隙` | int | 时隙编号 | 1-2000 |
| `时间` | string | 时间戳(HH:MM:SS格式) | 04:00:00开始 |
| `纬度` | float | 纬度 (度) | -90.0 ~ 90.0 |
| `经度` | float | 经度 (度) | -180.0 ~ 180.0 |
| `光照` | boolean | 光照状态 | TRUE=光照, FALSE=阴影 |
| `状态` | boolean | 卫星状态 | TRUE=活跃, FALSE=维护 |

### C.2 地面用户终端数据格式 (global_ground_stations.csv)

**文件描述**：全球420个地面用户终端的地理位置和属性信息

**格式规范**：
```csv
ID,Latitude,Longitude,RegionType,Size,PurposeType
1,-65,-180,Ocean,Small,Normal
2,-65,-168,Ocean,Small,Normal
3,-65,-156,Ocean,Small,Normal
...
```

**字段说明**：
| 字段名 | 类型 | 说明 | 可能值 |
|--------|------|------|--------|
| `ID` | int | 地面站ID | 1-420 |
| `Latitude` | float | 纬度 (度) | -90.0 ~ 90.0 |
| `Longitude` | float | 经度 (度) | -180.0 ~ 180.0 |
| `RegionType` | string | 区域类型 | Ocean, Land |
| `Size` | string | 规模大小 | Small, Medium, Large |
| `PurposeType` | string | 功能类型 | Normal, Industrial, DelaySensitive |

### C.3 云计算中心数据格式 (cloud_station.csv)

**文件描述**：5个云计算中心的位置信息

**格式规范**：
```csv
ID,Latitude,Longitude
1,35,-108
2,55,12
3,35,84
4,35,132
5,-25,120
```

**字段说明**：
| 字段名 | 类型 | 说明 | 范围 |
|--------|------|------|------|
| `ID` | int | 云中心ID | 1-5 |
| `Latitude` | float | 纬度 (度) | -90.0 ~ 90.0 |
| `Longitude` | float | 经度 (度) | -180.0 ~ 180.0 |

### C.4 任务数据格式

#### C.4.1 任务生成器任务格式 (Task类)
```python
@dataclass
class Task:
    task_id: int                           # 任务ID
    type_id: TaskType                      # 任务类型ID (REALTIME/NORMAL/COMPUTE_INTENSIVE)
    data_size_mb: float                    # 数据大小 (MB)
    complexity_cycles_per_bit: int         # 每比特计算复杂度 (cycles/bit)
    deadline_timestamp: float              # 截止时间戳
    priority: int                          # 优先级
    location_id: int                       # 位置ID
    coordinates: Tuple[float, float]       # 地理坐标 (纬度, 经度)
    generation_time: float                 # 生成时间
    geography: Optional[GeographyType]     # 地理类型 (LAND/OCEAN)
    scale: Optional[ScaleType]             # 规模类型 (SMALL/MEDIUM/LARGE)
    functional_type: Optional[FunctionalType] # 功能类型
```

#### C.4.2 计算任务格式 (ComputeTask类)
```python
@dataclass
class ComputeTask:
    task_id: int                           # 任务ID
    priority: float                        # 静态优先级 P_i
    deadline: float                        # 绝对截止时间 D_i (秒)
    data_size_mb: float                    # 数据大小 S_i (MB)
    complexity: float                      # 计算复杂度 C_i (CPU cycles)
    drop_penalty: float                    # 丢弃惩罚 W_i
    arrival_time: float                    # 到达时间
    start_time: Optional[float]            # 开始处理时间
    completion_time: Optional[float]       # 完成时间
    status: TaskStatus                     # 任务状态
    allocated_cpu: float                   # 分配的CPU资源比例
    energy_consumed: float                 # 消耗的能量
    processing_delay: float                # 处理延迟
    communication_delay: float             # 通信延迟
    total_delay: float                     # 总延迟

    # 跨时隙处理支持字段
    processing_progress: float             # 处理进度(0-1)
    remaining_complexity: float            # 剩余计算复杂度
    accumulated_processing_time: float     # 累计处理时间
    accumulated_energy: float              # 累计能量消耗
    last_update_time: Optional[float]      # 上次更新时间
    is_partial_processing: bool            # 是否部分处理
    segment_ratio: float                   # 任务分片比例
```

#### C.4.3 任务状态枚举
```python
class TaskStatus(Enum):
    PENDING = "pending"          # 等待中
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    DROPPED = "dropped"          # 已丢弃
    FAILED = "failed"           # 失败
```

### C.5 仿真结果数据格式

#### C.5.1 算法结果格式 (AlgorithmResult类)
```python
@dataclass
class AlgorithmResult:
    algorithm_name: str                      # 算法名称
    task_records: Optional[List[TaskTrackingRecord]]  # 任务跟踪记录列表
    satellites: Optional[List[Satellite]]    # 卫星对象列表
    time_window: Optional[Tuple[float, float]]  # 时间窗口
    additional_info: Dict                    # 额外信息
```

#### C.5.2 任务跟踪记录格式 (TaskTrackingRecord类)
```python
@dataclass
class TaskTrackingRecord:
    task_id: str                             # 任务ID
    total_data_size_mb: float                # 总数据大小 (MB)
    total_complexity_cycles: float           # 总计算复杂度 (cycles)
    priority: int                            # 优先级
    deadline_timestamp: float                # 截止时间戳
    arrival_timestamp: float                 # 到达时间戳
    start_timestamp: Optional[float]         # 开始处理时间
    completion_timestamp: Optional[float]    # 完成时间
    
    # 处理节点信息
    processing_nodes: List[ProcessingNode]   # 处理节点列表
    
    # 状态和指标
    status: TaskStatus                       # 任务状态
    total_processing_time: float             # 总处理时间
    total_communication_time: float          # 总通信时间
    total_queuing_time: float                # 总排队时间
    total_energy_consumption: float          # 总能耗
    processing_efficiency: float             # 处理效率
    end_to_end_delay: float                  # 端到端延迟
    drop_penalty: float                      # 丢弃惩罚
```

#### C.5.3 处理节点信息格式
```python
@dataclass
class ProcessingNode:
    node_id: str                             # 节点ID
    node_type: NodeType                      # 节点类型 (SATELLITE/CLOUD)
    data_size_mb: float                      # 处理数据大小
    complexity_cycles: float                 # 处理复杂度
    processing_time: float                   # 处理时间
    energy_consumption: float                # 能耗
    start_timestamp: float                   # 开始时间
    end_timestamp: float                     # 结束时间
    cpu_allocation: float                    # CPU分配比例
    
class NodeType(Enum):
    SATELLITE = "satellite"                  # 卫星节点
    CLOUD = "cloud"                          # 云节点
```

---

## 附录E：常见问题解答 (FAQ)

### E.1 系统配置问题

**Q1: 如何修改仿真参数？**

A1: 修改 `src/env/physics_layer/config.yaml` 文件：
```yaml
system:
  num_leo_satellites: 72        # 卫星数量
  num_users: 420               # 地面用户数量
  total_timeslots: 2000        # 总时隙数
  timeslot_duration_s: 3       # 时隙持续时间

computation:
  f_leo_hz: 500000000000       # 卫星CPU频率 (500GHz)
  max_parallel_tasks: 200      # 最大并行任务数

hybrid_simulation:
  enabled: true                # 启用混合仿真模式
  micro_iterations: 3          # 微观逻辑轮次数
  max_offloading_hops: 3       # 最大卸载跳数
```

**Q2: 如何启用可视化系统？**

A2: 运行可视化服务器：
```bash
cd src/env/visualization
python run_visualization.py
# 访问 http://localhost:5000 查看可视化界面
```

### E.2 性能优化问题

**Q3: 仿真运行缓慢，如何优化性能？**

A3: 性能优化建议：
```python
# 1. 使用向量化操作
import numpy as np
# 好的做法
distances = np.linalg.norm(positions1[:, np.newaxis] - positions2, axis=2)
# 避免的做法
# for i in range(len(positions1)):
#     for j in range(len(positions2)):
#         distance = calculate_distance(positions1[i], positions2[j])

# 2. 缓存计算结果
from functools import lru_cache
@lru_cache(maxsize=1000)
def expensive_calculation(param):
    return result

# 3. 减少日志输出
# 生产环境设置日志级别为INFO或WARNING
```


### E.3 模块开发问题

**Q4: 如何添加自定义的调度算法？**

A4: 按照以下模板开发：
```python
# src/env/satellite_cloud/my_scheduler.py
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus
from typing import List, Optional

class MyCustomScheduler:
    def __init__(self, config):
        self.config = config
        self.algorithm_params = config.get('my_algorithm', {})

    def calculate_priority_score(self, task: ComputeTask, current_time: float) -> float:
        # 实现你的优先级计算逻辑
        urgency = max(0, (task.deadline - current_time))
        score = (task.priority * self.algorithm_params.get('priority_weight', 1.0) +
                1.0 / (urgency + 1e-6) * self.algorithm_params.get('urgency_weight', 2.0))
        return score

    def select_next_task(self, task_queue: List[ComputeTask], current_time: float) -> Optional[ComputeTask]:
        if not task_queue:
            return None
        # 选择最高分任务
        return max(task_queue, key=lambda t: self.calculate_priority_score(t, current_time))

# 在配置文件中添加参数
# config.yaml
my_algorithm:
  priority_weight: 1.5
  urgency_weight: 2.0
```

**Q5: 如何集成新的性能指标分析器？**

A5: 创建自定义分析器：
```python
# src/env/metrics_model/my_analyzer.py
from typing import Dict, List, Any
from src.env.metrics_model.algorithm_metrics import AlgorithmMetrics

class MyCustomAnalyzer:
    def __init__(self):
        self.name = "my_custom_analyzer"

    def analyze(self, data: List[Dict[str, Any]]) -> Dict[str, float]:
        # 实现自定义指标计算
        custom_metric = sum(item.get('custom_value', 0) for item in data)
        return {
            'custom_metric': custom_metric,
            'custom_average': custom_metric / len(data) if data else 0
        }

# 注册到主分析器
metrics = AlgorithmMetrics()
metrics.add_custom_analyzer('my_analyzer', MyCustomAnalyzer())
```


### E.4 测试和验证问题

**Q6: 如何验证仿真结果的正确性？**

A6: 多层次验证方法：
```python
# 1. 单元测试 - 验证单个模块
def test_orbital_module():
    from src.env.physics_layer.orbital import OrbitalUpdater
    from src.env.Foundation_Layer.time_manager import TimeContext

    updater = OrbitalUpdater()
    context = TimeContext(simulation_step=0, simulation_time=0.0,
                         physical_time=datetime.now(), timeslot_duration=3.0)
    updater.update_positions(context)
    matrices = updater.get_visibility_matrices(0)

    # 验证矩阵维度
    assert matrices['satellite_to_satellite'].shape == (72, 72)
    # 验证对称性 (卫星间可见性应该对称)
    assert np.allclose(matrices['satellite_to_satellite'],
                      matrices['satellite_to_satellite'].T)

# 2. 集成测试 - 验证模块间协作
def test_compute_integration():
    from src.env.satellite_cloud.satellite_compute import SatelliteCompute
    from src.env.satellite_cloud.compute_models import ComputeTask

    satellite = SatelliteCompute(satellite_id="test_sat", config={})
    task = ComputeTask(task_id=1, priority=1.0, deadline=100.0,
                      data_size_mb=10.0, complexity=1000.0,
                      drop_penalty=50.0, arrival_time=0.0)

    success = satellite.add_task(task)
    assert success, "任务添加应该成功"

# 3. 结果合理性检查
def validate_simulation_results(results):
    # 检查任务完成率
    total_tasks = sum(r.get('tasks_total', 0) for r in results)
    completed_tasks = sum(r.get('tasks_completed', 0) for r in results)
    completion_rate = completed_tasks / total_tasks if total_tasks > 0 else 0
    assert 0 <= completion_rate <= 1, "完成率应在0-1之间"

    # 检查能耗合理性
    total_energy = sum(r.get('energy_consumption', 0) for r in results)
    assert total_energy >= 0, "总能耗应大于等于0"
```

**Q7: 如何运行完整的系统测试？**

A7: 使用测试套件：
```bash
# 运行所有测试
cd test/
python -m pytest env/ -v

# 运行特定模块测试
python -m pytest env/satellite_cloud/ -v

# 运行性能测试
python test_phase1_basic.py
```
