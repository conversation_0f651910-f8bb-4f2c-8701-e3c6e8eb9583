"""
Test module for compute_manager.py
Tests unified compute resource management and offloading strategies
"""

import unittest
import numpy as np
from datetime import datetime

# Absolute imports as per coding standard
from src.env.satellite_cloud.compute_manager import ComputeManager, OffloadingStrategy
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus
from src.env.physics_layer.task_models import Task


class TestComputeManager(unittest.TestCase):
    """Test compute manager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create test configuration
        self.config = {
            'system': {
                'num_leo_satellites': 5,  # Smaller for testing
                'num_cloud_centers': 2,
                'timeslot_duration_ms': 5000
            },
            'queuing': {
                'w_priority': 1.0,
                'w_urgency': 2.0,
                'w_cost': 0.5,
                'epsilon_urgency': 1.0e-6,
                'max_queue_size': 100
            },
            'computation': {
                'f_leo_hz': 500e9,
                'f_cloud_hz': 100e9,
                'zeta_leo': 1.0e-10,
                'processing_overhead_ratio': 0.05,
                'leo_battery_capacity_j': 3600000,
                'leo_solar_power_w': 5000,
                'energy_threshold_ratio': 0.2,
                'default_drop_penalty': 100.0,
                'min_cpu_allocation': 10.0,
                'max_parallel_tasks': 200
            },
            'communication': {
                'b_us_hz': 1000e6,
                'mb_to_bits': 8e6
            },
            'offloading': {
                'strategy': OffloadingStrategy.DEADLINE_AWARE,
                'max_offload_ratio': 0.7,
                'energy_threshold': 0.3,
                'queue_threshold': 0.8
            }
        }
        
        # Create test tasks with varying characteristics
        self.test_tasks = []
        for i in range(10):
            task = Task(
                task_id=i,
                user_id=300 + i,
                priority=1.0 + (i % 5),
                data_size_mb=3.0 + i * 2,
                complexity_cycles_per_bit=800 + i * 150,
                deadline_ms=8000 + i * 3000,
                creation_timestamp=datetime.now()
            )
            self.test_tasks.append(task)
    
    def test_compute_manager_creation(self):
        """Test ComputeManager initialization"""
        manager = ComputeManager(config=self.config)
        
        print(f"\n=== 测试计算管理器创建 ===")
        print(f"卫星数量: {len(manager.satellites)}")
        print(f"云中心数量: {len(manager.clouds)}")
        print(f"卸载策略: {manager.strategy}")
        print(f"最大卸载比例: {manager.max_offload_ratio}")
        print(f"能量卸载阈值: {manager.offload_threshold_energy}")
        print(f"队列卸载阈值: {manager.offload_threshold_queue}")
        
        # Verify initialization
        self.assertEqual(len(manager.satellites), self.config['system']['num_leo_satellites'])
        self.assertEqual(len(manager.clouds), self.config['system']['num_cloud_centers'])
        self.assertEqual(manager.strategy, OffloadingStrategy.DEADLINE_AWARE)
        self.assertEqual(manager.total_tasks_received, 0)
        self.assertEqual(manager.total_tasks_offloaded, 0)
        
        # Verify satellite and cloud instances
        for sat_id in range(self.config['system']['num_leo_satellites']):
            self.assertIn(sat_id, manager.satellites)
            self.assertEqual(manager.satellites[sat_id].satellite_id, sat_id)
        
        for cloud_id in range(self.config['system']['num_cloud_centers']):
            self.assertIn(cloud_id, manager.clouds)
            self.assertEqual(manager.clouds[cloud_id].cloud_id, cloud_id)
    
    def test_local_first_strategy(self):
        """Test LOCAL_FIRST offloading strategy"""
        manager = ComputeManager(config=self.config)
        manager.set_offloading_strategy(OffloadingStrategy.LOCAL_FIRST)
        
        print(f"\n=== 测试LOCAL_FIRST策略 ===")
        print(f"当前策略: {manager.strategy}")
        
        # Test with sufficient satellite energy
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        current_time = 1000.0
        
        # Distribute tasks
        distribution_results = []
        for i, task in enumerate(self.test_tasks[:5]):
            source_satellite = i % manager.num_satellites
            success = manager.distribute_task(
                task, source_satellite, current_time, illuminated_status
            )
            distribution_results.append((task.task_id, source_satellite, success))
            print(f"任务 {task.task_id} -> 卫星 {source_satellite}: {success}")
        
        print(f"\n分发结果统计:")
        print(f"总接收任务数: {manager.total_tasks_received}")
        print(f"总卸载任务数: {manager.total_tasks_offloaded}")
        print(f"卸载成功率: {manager.offload_success_rate:.1%}")
        
        # Check satellite queue lengths
        print(f"\n卫星队列状态:")
        for sat_id, satellite in manager.satellites.items():
            queue_status = satellite.get_queue_status()
            print(f"  卫星 {sat_id}: 队列长度 {queue_status['queue_length']}")
        
        # With LOCAL_FIRST, most tasks should stay local
        self.assertEqual(manager.total_tasks_received, 5)
        # self.assertLessEqual(manager.total_tasks_offloaded, manager.total_tasks_received)
    
    def test_cloud_first_strategy(self):
        """Test CLOUD_FIRST offloading strategy"""
        manager = ComputeManager(config=self.config)
        manager.set_offloading_strategy(OffloadingStrategy.CLOUD_FIRST)
        
        print(f"\n=== 测试CLOUD_FIRST策略 ===")
        print(f"当前策略: {manager.strategy}")
        
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        current_time = 1000.0
        
        # Distribute tasks
        for i, task in enumerate(self.test_tasks[:5]):
            source_satellite = i % manager.num_satellites
            success = manager.distribute_task(
                task, source_satellite, current_time, illuminated_status
            )
            print(f"任务 {task.task_id} -> 源卫星 {source_satellite}: {success}")
        
        print(f"\n分发结果统计:")
        print(f"总接收任务数: {manager.total_tasks_received}")
        print(f"总卸载任务数: {manager.total_tasks_offloaded}")
        print(f"卸载成功率: {manager.offload_success_rate:.1%}")
        
        # Check cloud queue lengths
        print(f"\n云中心队列状态:")
        for cloud_id, cloud in manager.clouds.items():
            queue_status = cloud.get_queue_status()
            print(f"  云中心 {cloud_id}: 队列长度 {queue_status['queue_length']}")
        
        # With CLOUD_FIRST, more tasks should be offloaded
        # self.assertGreater(manager.total_tasks_offloaded, 0)
    
    def test_deadline_aware_strategy(self):
        """Test DEADLINE_AWARE offloading strategy"""
        manager = ComputeManager(config=self.config)
        manager.set_offloading_strategy(OffloadingStrategy.DEADLINE_AWARE)
        
        print(f"\n=== 测试DEADLINE_AWARE策略 ===")
        
        # Create tasks with different deadline pressures
        urgent_task = Task(
            task_id=999,
            user_id=999,
            priority=5.0,
            data_size_mb=20.0,
            complexity_cycles_per_bit=3000,  # High complexity
            deadline_ms=3000,  # Very tight deadline
            creation_timestamp=datetime.now()
        )
        
        relaxed_task = Task(
            task_id=998,
            user_id=998,
            priority=2.0,
            data_size_mb=5.0,
            complexity_cycles_per_bit=500,  # Low complexity
            deadline_ms=30000,  # Relaxed deadline
            creation_timestamp=datetime.now()
        )
        
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        current_time = 1000.0
        
        # Distribute urgent task
        urgent_success = manager.distribute_task(
            urgent_task, 0, current_time, illuminated_status
        )
        print(f"紧急任务 (截止时间短): {urgent_success}")
        
        # Distribute relaxed task
        relaxed_success = manager.distribute_task(
            relaxed_task, 1, current_time, illuminated_status
        )
        print(f"宽松任务 (截止时间长): {relaxed_success}")
        
        print(f"\n分发结果统计:")
        print(f"总接收任务数: {manager.total_tasks_received}")
        print(f"总卸载任务数: {manager.total_tasks_offloaded}")
        
        # Both tasks should be handled appropriately based on deadlines
        self.assertTrue(urgent_success or relaxed_success)
    
    def test_energy_aware_strategy(self):
        """Test ENERGY_AWARE offloading strategy"""
        manager = ComputeManager(config=self.config)
        manager.set_offloading_strategy(OffloadingStrategy.ENERGY_AWARE)
        
        print(f"\n=== 测试ENERGY_AWARE策略 ===")
        
        # Simulate low energy scenario
        low_energy_satellite = 0
        satellite = manager.satellites[low_energy_satellite]
        satellite.battery_energy = satellite.max_battery * 0.15  # 15% battery
        
        # Test with illuminated (charging) vs non-illuminated
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        non_illuminated_status = {i: False for i in range(manager.num_satellites)}
        
        current_time = 1000.0
        
        print(f"低能量卫星 {low_energy_satellite} 电池状态:")
        energy_status = satellite.get_energy_status()
        for key, value in energy_status.items():
            print(f"  {key}: {value}")
        
        # Test with illuminated status
        illuminated_success = manager.distribute_task(
            self.test_tasks[0], low_energy_satellite, current_time, illuminated_status
        )
        print(f"\n光照条件下分发: {illuminated_success}")
        
        # Reset for next test
        manager.total_tasks_received = 0
        manager.total_tasks_offloaded = 0
        
        # Test with non-illuminated status
        non_illuminated_success = manager.distribute_task(
            self.test_tasks[1], low_energy_satellite, current_time, non_illuminated_status
        )
        print(f"非光照条件下分发: {non_illuminated_success}")
        
        print(f"\n最终统计:")
        print(f"总接收任务数: {manager.total_tasks_received}")
        print(f"总卸载任务数: {manager.total_tasks_offloaded}")
    
    def test_load_balancing_strategy(self):
        """Test LOAD_BALANCING offloading strategy"""
        manager = ComputeManager(config=self.config)
        manager.set_offloading_strategy(OffloadingStrategy.LOAD_BALANCING)
        
        print(f"\n=== 测试LOAD_BALANCING策略 ===")
        
        # Fill one satellite's queue to trigger load balancing
        overloaded_satellite = 0
        satellite = manager.satellites[overloaded_satellite]
        
        # Add many tasks to create high queue utilization
        for i in range(80):  # Close to max queue size
            dummy_task = Task(
                task_id=2000 + i,
                user_id=2000 + i,
                priority=2.0,
                data_size_mb=3.0,
                complexity_cycles_per_bit=500,
                deadline_ms=15000,
                creation_timestamp=datetime.now()
            )
            satellite.add_task(dummy_task)
        
        queue_status = satellite.get_queue_status()
        print(f"过载卫星 {overloaded_satellite} 队列状态:")
        for key, value in queue_status.items():
            print(f"  {key}: {value}")
        
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        current_time = 1000.0
        
        # Try to add new task to overloaded satellite
        success = manager.distribute_task(
            self.test_tasks[0], overloaded_satellite, current_time, illuminated_status
        )
        print(f"\n向过载卫星添加新任务: {success}")
        
        print(f"\n负载均衡结果:")
        print(f"总接收任务数: {manager.total_tasks_received}")
        print(f"总卸载任务数: {manager.total_tasks_offloaded}")
        
        # Check if task was offloaded due to high load
        if manager.total_tasks_offloaded > 0:
            print("任务因负载均衡被卸载到其他节点")
    
    def test_timeslot_processing(self):
        """Test comprehensive timeslot processing"""
        manager = ComputeManager(config=self.config)
        
        # Distribute tasks across satellites and clouds
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        current_time = 1000.0
        
        print(f"\n=== 测试时隙处理 ===")
        
        # Distribute tasks
        for i, task in enumerate(self.test_tasks):
            source_satellite = i % manager.num_satellites
            manager.distribute_task(task, source_satellite, current_time, illuminated_status)
        
        print(f"分发了 {manager.total_tasks_received} 个任务")
        
        # Process multiple timeslots
        timeslot_duration = 3.0  # 3 seconds (与config.yaml一致)
        total_completed = 0
        
        for timeslot in range(1, 6):
            print(f"\n--- 时隙 {timeslot} ---")
            
            results = manager.process_timeslot(
                timeslot, timeslot_duration, illuminated_status
            )
            
            timeslot_completed = results['total_completed']
            total_completed += timeslot_completed
            
            print(f"本时隙完成任务数: {timeslot_completed}")
            print(f"累计完成任务数: {total_completed}")
            print(f"总能量消耗: {results['total_energy_consumed']:.2e}J")
            
            # Show satellite results
            print(f"\n卫星处理结果:")
            for sat_id, sat_result in results['satellite_results'].items():
                if sat_result['completed_count'] > 0:
                    print(f"  卫星 {sat_id}: 完成 {sat_result['completed_count']} 个任务")
            
            # Show cloud results
            print(f"云中心处理结果:")
            for cloud_id, cloud_result in results['cloud_results'].items():
                if cloud_result['completed_count'] > 0:
                    print(f"  云中心 {cloud_id}: 完成 {cloud_result['completed_count']} 个任务")
        
        print(f"\n最终统计:")
        print(f"总接收任务数: {manager.total_tasks_received}")
        print(f"总完成任务数: {total_completed}")
        print(f"总卸载任务数: {manager.total_tasks_offloaded}")
        print(f"卸载成功率: {manager.offload_success_rate:.1%}")
        
        self.assertGreaterEqual(total_completed, 0)
        self.assertEqual(manager.total_tasks_received, len(self.test_tasks))
    
    def test_system_status_and_metrics(self):
        """Test system status and metrics collection"""
        manager = ComputeManager(config=self.config)
        
        # Add some tasks and process
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        current_time = 1000.0
        
        for i, task in enumerate(self.test_tasks[:3]):
            source_satellite = i % manager.num_satellites
            manager.distribute_task(task, source_satellite, current_time, illuminated_status)
        
        # Process one timeslot
        results = manager.process_timeslot(1, 5.0, illuminated_status)
        
        print(f"\n=== 测试系统状态和指标 ===")
        
        # Get system status
        system_status = manager.get_system_status()
        print(f"\n系统状态:")
        for key, value in system_status.items():
            if key != 'resource_metrics':  # Skip detailed metrics for cleaner output
                print(f"  {key}: {value}")
        
        # Get performance summary
        performance_summary = manager.get_performance_summary()
        print(f"\n性能摘要:")
        print(f"卸载指标: {performance_summary['offloading_metrics']}")
        
        # Verify metrics
        self.assertEqual(system_status['num_satellites'], manager.num_satellites)
        self.assertEqual(system_status['num_clouds'], manager.num_clouds)
        self.assertGreaterEqual(system_status['total_tasks_received'], 0)
        
    def test_node_access_methods(self):
        """Test satellite and cloud access methods"""
        manager = ComputeManager(config=self.config)
        
        print(f"\n=== 测试节点访问方法 ===")
        
        # Test satellite access
        satellite_0 = manager.get_satellite(0)
        self.assertIsNotNone(satellite_0)
        self.assertEqual(satellite_0.satellite_id, 0)
        print(f"获取卫星 0: {satellite_0.satellite_id}")
        
        # Test invalid satellite access
        invalid_satellite = manager.get_satellite(999)
        self.assertIsNone(invalid_satellite)
        print(f"获取不存在的卫星 999: {invalid_satellite}")
        
        # Test cloud access
        cloud_0 = manager.get_cloud(0)
        self.assertIsNotNone(cloud_0)
        self.assertEqual(cloud_0.cloud_id, 0)
        print(f"获取云中心 0: {cloud_0.cloud_id}")
        
        # Test invalid cloud access
        invalid_cloud = manager.get_cloud(999)
        self.assertIsNone(invalid_cloud)
        print(f"获取不存在的云中心 999: {invalid_cloud}")
    
    def test_reset_functionality(self):
        """Test manager reset functionality"""
        manager = ComputeManager(config=self.config)
        
        # Add tasks and process
        illuminated_status = {i: True for i in range(manager.num_satellites)}
        current_time = 1000.0
        
        for task in self.test_tasks[:3]:
            manager.distribute_task(task, 0, current_time, illuminated_status)
        
        manager.process_timeslot(1, 5.0, illuminated_status)
        
        print(f"\n=== 测试重置功能 ===")
        print(f"重置前:")
        print(f"  总接收任务数: {manager.total_tasks_received}")
        print(f"  总卸载任务数: {manager.total_tasks_offloaded}")
        
        # Reset
        manager.reset()
        
        print(f"\n重置后:")
        print(f"  总接收任务数: {manager.total_tasks_received}")
        print(f"  总卸载任务数: {manager.total_tasks_offloaded}")
        print(f"  卸载成功率: {manager.offload_success_rate}")
        
        # Verify reset
        self.assertEqual(manager.total_tasks_received, 0)
        self.assertEqual(manager.total_tasks_offloaded, 0)
        self.assertEqual(manager.total_offload_attempts, 0)
        self.assertEqual(manager.offload_success_rate, 0.0)
        
        # Verify satellite and cloud reset
        for satellite in manager.satellites.values():
            self.assertEqual(len(satellite.task_queue), 0)
            self.assertEqual(satellite.total_tasks_processed, 0)
        
        for cloud in manager.clouds.values():
            self.assertEqual(len(cloud.task_queue), 0)
            self.assertEqual(cloud.total_tasks_processed, 0)


def run_compute_manager_tests():
    """Run all compute manager tests"""
    print("=" * 60)
    print("执行计算管理器测试")
    print("=" * 60)
    
    unittest.main(argv=[''], exit=False, verbosity=2)


if __name__ == "__main__":
    run_compute_manager_tests()