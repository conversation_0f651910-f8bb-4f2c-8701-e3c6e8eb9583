"""
简单测试任务生成的随机性
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.env.physics_layer.task_generator import TaskGenerator

def test_random():
    print("测试任务生成随机性\n")
    
    # 测试1：使用seed=None（随机）
    print("1. 不使用种子（随机）- 运行两次:")
    
    generator1 = TaskGenerator(seed=None)
    generator1.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    print("第一次运行:")
    for i in range(3):
        tasks = generator1.generate_tasks_for_timeslot(i)
        total = sum(len(t) for t in tasks.values())
        print(f"  时隙{i}: {total}个任务")
    
    print("\n第二次运行（新实例）:")
    generator2 = TaskGenerator(seed=None)
    generator2.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    for i in range(3):
        tasks = generator2.generate_tasks_for_timeslot(i)
        total = sum(len(t) for t in tasks.values())
        print(f"  时隙{i}: {total}个任务")
    
    print("\n如果上面两次运行的数字不同，说明随机性生效了！")
    
    # 测试2：使用固定种子作对比
    print("\n2. 使用固定种子（seed=123）- 运行两次:")
    
    generator3 = TaskGenerator(seed=123)
    generator3.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    print("第一次（seed=123）:")
    for i in range(3):
        tasks = generator3.generate_tasks_for_timeslot(i)
        total = sum(len(t) for t in tasks.values())
        print(f"  时隙{i}: {total}个任务")
    
    generator4 = TaskGenerator(seed=123)
    generator4.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    print("\n第二次（seed=123）:")
    for i in range(3):
        tasks = generator4.generate_tasks_for_timeslot(i)
        total = sum(len(t) for t in tasks.values())
        print(f"  时隙{i}: {total}个任务")
    
    print("\n固定种子的两次运行应该产生相同结果")

if __name__ == "__main__":
    test_random()