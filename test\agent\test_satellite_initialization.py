"""
测试卫星初始化和任务分配问题
"""

import sys
from pathlib import Path
import numpy as np
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.satellite_cloud.satellite import Satellite, SatelliteTask

# 初始化
config_path = Path(__file__).parent.parent.parent / 'src/env/physics_layer/config.yaml'
orbital = OrbitalUpdater(None, str(config_path))
comm_manager = None  # 简化测试，不使用通信管理器

# 获取卫星ID并创建卫星
actual_satellite_ids = sorted([int(sid) for sid in orbital.get_satellite_ids(1)])
print(f"卫星ID列表: {actual_satellite_ids}")
print(f"卫星数量: {len(actual_satellite_ids)}")

# 创建所有卫星
satellites = []
for idx, actual_sat_id in enumerate(actual_satellite_ids):
    satellite = Satellite(
        satellite_id=actual_sat_id,
        config_path=str(config_path),
        orbital_updater=orbital,
        comm_manager=comm_manager
    )
    satellites.append(satellite)
    print(f"创建卫星 {idx}: ID={actual_sat_id}, 队列大小限制={satellite.scheduler.max_queue_size}")

print(f"\n创建了 {len(satellites)} 个卫星")

# 测试向每个卫星添加任务
print("\n测试向每个卫星添加任务:")
task_counts = []

for idx, satellite in enumerate(satellites):
    # 创建测试任务
    test_task = SatelliteTask(
        task_id=f"test_{idx}",
        priority=1.0,
        deadline=100.0,
        data_size=10.0,
        complexity=1000000,
        drop_penalty=10.0,
        arrival_time=0.0
    )
    
    # 尝试添加任务
    success = satellite.add_task(test_task)
    current_queue_size = len(satellite.task_queue)
    task_counts.append(current_queue_size)
    
    if idx < 10 or idx >= len(satellites) - 10 or not success:
        print(f"  卫星 {idx} (ID={satellite.satellite_id}): 添加{'成功' if success else '失败'}, 队列长度={current_queue_size}")

print(f"\n任务分配统计:")
print(f"  成功接收任务的卫星数: {sum(1 for c in task_counts if c > 0)}")
print(f"  队列为空的卫星数: {sum(1 for c in task_counts if c == 0)}")

# 测试批量添加任务
print("\n测试批量添加任务到前3个卫星:")
for sat_idx in range(3):
    satellite = satellites[sat_idx]
    initial_queue = len(satellite.task_queue)
    
    # 尝试添加105个任务（超过队列限制）
    added = 0
    dropped = 0
    for i in range(105):
        task = SatelliteTask(
            task_id=f"batch_{sat_idx}_{i}",
            priority=1.0,
            deadline=100.0,
            data_size=10.0,
            complexity=1000000,
            drop_penalty=10.0,
            arrival_time=0.0
        )
        if satellite.add_task(task):
            added += 1
        else:
            dropped += 1
    
    final_queue = len(satellite.task_queue)
    print(f"  卫星 {sat_idx} (ID={satellite.satellite_id}):")
    print(f"    初始队列: {initial_queue}, 最终队列: {final_queue}")
    print(f"    成功添加: {added}, 丢弃: {dropped}")
    print(f"    队列是否满: {final_queue >= satellite.scheduler.max_queue_size}")

# 检查所有卫星的状态
print("\n所有卫星队列状态汇总:")
queue_distribution = {}
for idx, satellite in enumerate(satellites):
    queue_len = len(satellite.task_queue)
    if queue_len not in queue_distribution:
        queue_distribution[queue_len] = []
    queue_distribution[queue_len].append(idx)

for queue_len in sorted(queue_distribution.keys()):
    indices = queue_distribution[queue_len]
    print(f"  队列长度 {queue_len}: {len(indices)} 个卫星 (索引: {indices[:5]}{'...' if len(indices) > 5 else ''})")