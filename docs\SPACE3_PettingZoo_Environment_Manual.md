# SPACE3 PettingZoo Environment Manual
# SPACE3 PettingZoo 强化学习环境使用手册

## 目录 (Table of Contents)

1. [环境概述](#1-环境概述)
2. [快速开始](#2-快速开始)
3. [环境架构](#3-环境架构)
4. [观测空间](#4-观测空间)
5. [动作空间](#5-动作空间)
6. [奖励机制](#6-奖励机制)
7. [环境配置](#7-环境配置)
8. [API参考](#8-api参考)
9. [训练示例](#9-训练示例)
10. [性能优化](#10-性能优化)
11. [常见问题](#11-常见问题)

---

## 1. 环境概述

### 1.1 简介

SPACE3 PettingZoo环境是一个专为**Transformer-MAPPO算法**设计的多智能体强化学习环境，模拟了低地球轨道(LEO)卫星星座的边缘计算任务调度场景。

**核心特性：**
- 🛰️ **72个LEO卫星智能体**：每个卫星作为独立的决策智能体
- 🌍 **420个地面用户终端**：动态生成计算任务
- ☁️ **5个云计算中心**：提供高性能计算资源
- 📊 **序列动作空间**：支持批量任务分配决策
- 🎯 **多维奖励机制**：综合考虑完成率、延迟、能耗等指标

### 1.2 应用场景

- 卫星边缘计算资源优化
- 分布式任务调度策略学习
- 多智能体协作机制研究
- 序列决策算法验证

### 1.3 技术特点

- **PettingZoo ParallelEnv**：标准多智能体环境接口
- **Padding & Masking**：支持变长序列的Transformer输入
- **模块化架构**：观测、动作、奖励组件分离
- **高性能实现**：向量化运算，220+ FPS

---

## 2. 快速开始

### 2.1 环境安装

```bash
# 依赖项
pip install pettingzoo gymnasium numpy pandas pyyaml

# 克隆项目
git clone <repository_url>
cd SPACE3
```

### 2.2 基础使用

```python
from src.env.space_env import make_env

# 创建环境
env = make_env()

# 重置环境
observations, infos = env.reset(seed=42)

# 环境交互循环
for step in range(100):
    # 为每个智能体生成动作（这里使用随机策略）
    actions = {}
    for agent_id in env.agents:
        # 每个动作是长度为20的序列（对应最大任务队列长度）
        actions[agent_id] = env.action_spaces[agent_id].sample()
    
    # 执行动作
    observations, rewards, terminations, truncations, infos = env.step(actions)
    
    # 检查终止
    if any(terminations.values()):
        break

# 关闭环境
env.close()
```

### 2.3 智能动作示例

```python
def smart_action_policy(observation):
    """基于观测的简单策略"""
    task_mask = observation['task_mask']
    action_mask = observation['action_mask']
    
    # 创建动作序列
    action_sequence = np.zeros(20, dtype=int)
    
    for i in range(20):
        if task_mask[i] == 1:  # 真实任务
            # 获取可行动作
            valid_actions = np.where(action_mask[i] > 0)[0]
            if len(valid_actions) > 0:
                # 70%概率本地处理，30%概率其他
                if np.random.random() < 0.7 and 0 in valid_actions:
                    action_sequence[i] = 0  # 本地处理
                else:
                    action_sequence[i] = np.random.choice(valid_actions)
    
    return action_sequence
```

---

## 3. 环境架构

### 3.1 系统架构图

```
┌─────────────────────────────────────────────────┐
│          Space3PettingZooEnv (主环境)           │
│                  [协调者角色]                    │
└─────────────┬───────────────────────────────────┘
              │
      ┌───────┴────────┬──────────┬──────────┐
      ▼                ▼          ▼          ▼
┌─────────────┐ ┌──────────┐ ┌─────────┐ ┌────────┐
│Observation  │ │Sequence  │ │ Reward  │ │Physical│
│  Builder    │ │ Action   │ │Calculator│ │Modules │
│             │ │ Handler  │ │         │ │        │
└─────────────┘ └──────────┘ └─────────┘ └────────┘
   观测构建       动作处理     奖励计算    物理仿真
```

### 3.2 核心组件

#### 3.2.1 主环境类 (Space3PettingZooEnv)
- **职责**：环境协调、时间管理、状态维护
- **接口**：PettingZoo ParallelEnv标准接口

#### 3.2.2 观测构建器 (ObservationBuilder)
- **职责**：构建归一化观测、处理padding/masking
- **特点**：支持变长任务队列、邻居状态聚合

#### 3.2.3 序列动作处理器 (SequenceActionHandler)
- **职责**：验证动作合法性、处理动作序列
- **特点**：考虑可见性约束、资源限制

#### 3.2.4 奖励计算器 (RewardCalculator)
- **职责**：多维奖励计算、奖励塑形
- **特点**：7个奖励维度、协作奖励机制

### 3.3 数据流

```
1. 任务生成 → 2. 任务分配到卫星队列 → 3. 智能体观测
     ↓
6. 奖励计算 ← 5. 任务执行 ← 4. 动作决策
```

---

## 4. 观测空间

### 4.1 观测结构

每个智能体的观测是一个字典，包含7个组件：

```python
observation = {
    'own_state': np.ndarray,        # [12,] 自身状态向量
    'task_queue': np.ndarray,       # [20, 8] 任务队列特征
    'task_mask': np.ndarray,        # [20,] 任务掩码
    'action_mask': np.ndarray,      # [20, 16] 动作掩码
    'neighbor_states': np.ndarray,  # [10, 5] 邻居状态
    'comm_quality': np.ndarray,     # [10,] 通信质量
    'time_info': np.ndarray         # [3,] 时间信息
}
```

### 4.2 特征详解

#### 4.2.1 自身状态 (own_state)
| 索引 | 特征 | 范围 | 说明 |
|------|------|------|------|
| 0 | CPU使用率 | [0,1] | 当前CPU利用率 |
| 1 | 内存使用率 | [0,1] | 当前内存利用率 |
| 2 | 电池电量 | [0,1] | 归一化电池电量 |
| 3 | 队列长度 | [0,1] | 任务队列占用率 |
| 4 | 处理中任务 | [0,1] | 正在处理的任务数 |
| 5 | 完成任务数 | [0,1] | 累计完成任务（归一化） |
| 6 | 丢弃任务数 | [0,1] | 累计丢弃任务（归一化） |
| 7 | 能耗 | [0,1] | 累计能量消耗 |
| 8 | 光照状态 | {0,1} | 是否在光照下 |
| 9 | 时间进度 | [0,1] | 当前仿真进度 |
| 10 | 剩余时间 | [0,1] | 剩余仿真时间比例 |
| 11 | 负载指标 | [0,1] | 平均处理时间 |

#### 4.2.2 任务队列 (task_queue)
每个任务的8维特征向量：
| 索引 | 特征 | 说明 |
|------|------|------|
| 0 | 任务类型 | 实时/普通/计算密集 |
| 1 | 优先级 | 任务优先级 |
| 2 | 数据大小 | 归一化数据量(MB) |
| 3 | 计算复杂度 | 每比特CPU周期数 |
| 4 | 剩余时间 | 距离截止时间 |
| 5 | 纬度 | 任务源位置纬度 |
| 6 | 经度 | 任务源位置经度 |
| 7 | 紧急度 | 综合紧急程度 |

#### 4.2.3 掩码机制
- **task_mask**: 标识真实任务(1)和padding位置(0)
- **action_mask**: 标识每个任务的可行动作

### 4.3 Padding策略

```python
# 任务队列不足20个时的padding处理
if len(actual_tasks) < 20:
    # 真实任务
    task_queue[0:len(actual_tasks)] = real_task_features
    task_mask[0:len(actual_tasks)] = 1
    
    # Padding位置
    task_queue[len(actual_tasks):] = 0
    task_mask[len(actual_tasks):] = 0
```

---

## 5. 动作空间

### 5.1 动作定义

每个智能体的动作是一个**长度为20的整数序列**，对应任务队列中每个位置的分配决策。

```python
action_space = spaces.MultiDiscrete([16] * 20)
```

### 5.2 动作含义

对于序列中的每个位置i，action[i]的含义：

| 动作值 | 目标 | 说明 |
|--------|------|------|
| 0 | 本地处理 | 在当前卫星上处理任务 |
| 1-10 | 邻居卫星1-10 | 转发到可见的邻居卫星 |
| 11-15 | 云中心1-5 | 通过地面站转发到云中心 |

### 5.3 动作约束

动作必须满足以下约束：
1. **可见性约束**：只能转发到可见的邻居
2. **连接约束**：转发到云需要地面站可见
3. **资源约束**：目标节点需要有可用资源
4. **Padding约束**：对padding位置的动作会被忽略

### 5.4 动作验证流程

```python
# SequenceActionHandler中的验证逻辑
for i, action in enumerate(action_sequence):
    if task_mask[i] == 0:  # Padding位置
        continue
        
    if action == 0:  # 本地处理
        if satellite_has_capacity():
            execute_local()
    elif 1 <= action <= 10:  # 邻居转发
        neighbor_idx = action - 1
        if is_neighbor_visible(neighbor_idx):
            forward_to_neighbor()
    elif 11 <= action <= 15:  # 云转发
        cloud_idx = action - 11
        if has_ground_visibility():
            forward_to_cloud()
```

---

## 6. 奖励机制

### 6.1 奖励维度

环境实现了7个奖励维度的综合计算：

| 维度 | 权重 | 说明 |
|------|------|------|
| 任务完成 | +10.0 | 成功完成任务的正奖励 |
| 处理延迟 | -0.01 | 任务延迟的惩罚 |
| 能量消耗 | -0.001 | 能耗惩罚 |
| 任务丢弃 | -5.0 | 丢弃任务的严重惩罚 |
| 无效动作 | -1.0 | 违反约束的动作惩罚 |
| 协作奖励 | **** | 成功协作的额外奖励 |
| 负载均衡 | +0.5 | 维持负载均衡的奖励 |

### 6.2 奖励计算公式

```python
reward = (
    completion_reward * w_completion +
    delay_penalty * w_delay +
    energy_penalty * w_energy +
    drop_penalty * w_drop +
    invalid_penalty * w_invalid +
    cooperation_bonus * w_coop +
    balance_bonus * w_balance
)
```

### 6.3 协作奖励机制

当多个智能体协同处理任务时，给予额外奖励：

```python
if successful_neighbor_forward:
    sender_reward += cooperation_bonus * 0.5
    receiver_reward += cooperation_bonus * 0.5
```

### 6.4 奖励塑形

支持基于状态改善的奖励塑形（可选）：

```python
shaped_reward = reward_calculator.compute_shaped_reward(
    current_state=current_obs,
    previous_state=prev_obs,
    agent_id=agent_id
)
```

---

## 7. 环境配置

### 7.1 配置文件结构

环境配置通过`src/env/space_env/env_config.yaml`管理：

```yaml
# 环境参数
max_queue_size: 20          # 最大任务队列长度
task_feature_dim: 8         # 任务特征维度
state_feature_dim: 12       # 状态特征维度
neighbor_feature_dim: 5     # 邻居特征维度
max_visible_neighbors: 10   # 最大可见邻居数
num_cloud_targets: 5        # 云中心数量

# 奖励权重
reward_weights:
  completion: 10.0
  delay: -0.01
  energy: -0.001
  drop: -5.0
  invalid_action: -1.0
  cooperation: 2.0
  load_balance: 0.5

# 归一化参数
normalization:
  state:
    cpu_max: 100
    memory_max: 100
    battery_max: 100
    queue_max: 20
    tasks_max: 1000
    energy_max: 10000
  task:
    priority_max: 5
    data_size_max: 150
    complexity_max: 500
    deadline_max: 30
    lat_max: 90
    lon_max: 180
  neighbor:
    distance_max: 2000

# 训练参数
training:
  learning_rate: 3e-4
  batch_size: 256
  gamma: 0.99
  gae_lambda: 0.95
  clip_range: 0.2
  value_coef: 0.5
  entropy_coef: 0.01
```

### 7.2 自定义配置

```python
# 使用自定义配置文件
env = make_env(config_path="path/to/custom_config.yaml")

# 或者直接修改配置
env = Space3PettingZooEnv()
env.reward_weights['completion'] = 15.0  # 增加完成奖励权重
```

---

## 8. API参考

### 8.1 环境创建

```python
def make_env(config_path: str = None) -> Space3PettingZooEnv:
    """
    创建SPACE3环境实例
    
    Args:
        config_path: 配置文件路径，None使用默认配置
    
    Returns:
        环境实例
    """
```

### 8.2 核心方法

#### reset()
```python
def reset(self, seed: Optional[int] = None, 
         options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
    """
    重置环境
    
    Returns:
        observations: 所有智能体的初始观测
        infos: 额外信息
    """
```

#### step()
```python
def step(self, actions: Dict[str, np.ndarray]) -> Tuple[Dict, Dict, Dict, Dict, Dict]:
    """
    执行一步环境更新
    
    Args:
        actions: 每个智能体的动作序列
    
    Returns:
        observations: 新观测
        rewards: 奖励
        terminations: 终止标志
        truncations: 截断标志
        infos: 额外信息
    """
```

#### render()
```python
def render(self):
    """渲染环境状态（文本模式）"""
```

### 8.3 属性访问

```python
# 智能体列表
env.agents  # ['sat_111', 'sat_112', ..., 'sat_182']

# 观测空间
env.observation_spaces[agent_id]

# 动作空间
env.action_spaces[agent_id]

# 当前时隙
env.current_timeslot

# 任务统计
env.total_tasks_generated
env.total_tasks_completed
env.total_tasks_dropped
```

---

## 9. 训练示例

### 9.1 基础训练循环

```python
import torch
import torch.nn as nn
from src.env.space_env import make_env

class SimplePolicy(nn.Module):
    """简单的策略网络示例"""
    def __init__(self, obs_dim, act_dim, hidden_dim=256):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, act_dim)
        )
    
    def forward(self, obs):
        # 将观测字典展平为向量
        obs_vector = self.flatten_observation(obs)
        logits = self.net(obs_vector)
        return logits
    
    def flatten_observation(self, obs):
        """将观测字典展平为一维向量"""
        vectors = []
        vectors.append(obs['own_state'])
        vectors.append(obs['task_queue'].flatten())
        vectors.append(obs['task_mask'])
        vectors.append(obs['neighbor_states'].flatten())
        vectors.append(obs['comm_quality'])
        vectors.append(obs['time_info'])
        return torch.cat([torch.tensor(v) for v in vectors])

# 训练循环
env = make_env()
policy = SimplePolicy(obs_dim=500, act_dim=16)  # 简化的维度
optimizer = torch.optim.Adam(policy.parameters(), lr=3e-4)

for episode in range(1000):
    observations, _ = env.reset()
    episode_rewards = {agent: 0 for agent in env.agents}
    
    for step in range(100):
        # 为每个智能体生成动作
        actions = {}
        for agent_id in env.agents:
            obs = observations[agent_id]
            with torch.no_grad():
                logits = policy(obs)
                # 生成序列动作
                action_seq = []
                for i in range(20):
                    if obs['task_mask'][i] == 1:
                        valid_actions = obs['action_mask'][i]
                        # 使用masked softmax
                        probs = torch.softmax(logits * valid_actions, dim=-1)
                        action = torch.multinomial(probs, 1).item()
                    else:
                        action = 0
                    action_seq.append(action)
                actions[agent_id] = np.array(action_seq)
        
        # 环境步进
        observations, rewards, terminations, truncations, infos = env.step(actions)
        
        # 累积奖励
        for agent_id in env.agents:
            episode_rewards[agent_id] += rewards[agent_id]
        
        if any(terminations.values()):
            break
    
    # 输出episode统计
    avg_reward = np.mean(list(episode_rewards.values()))
    print(f"Episode {episode}: Average Reward = {avg_reward:.2f}")
```

### 9.2 使用Transformer-MAPPO

```python
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv

class TransformerMAPPOWrapper:
    """Transformer-MAPPO包装器示例"""
    
    def __init__(self, env):
        self.env = env
        self.transformer = self._build_transformer()
        self.ppo_agents = self._build_ppo_agents()
    
    def _build_transformer(self):
        """构建Transformer模型处理序列"""
        # 实现Transformer架构
        pass
    
    def _build_ppo_agents(self):
        """为每个智能体创建PPO实例"""
        agents = {}
        for agent_id in self.env.agents:
            # 创建独立的PPO智能体
            agents[agent_id] = PPO("MlpPolicy", ...)
        return agents
    
    def train(self, total_timesteps):
        """训练循环"""
        # 实现MAPPO训练逻辑
        pass
```

---

## 10. 性能优化

### 10.1 计算优化

1. **向量化操作**
   ```python
   # 使用NumPy向量化代替循环
   distances = np.linalg.norm(
       positions[:, np.newaxis] - positions[np.newaxis, :],
       axis=2
   )
   ```

2. **批处理**
   ```python
   # 批量处理所有智能体的观测
   all_observations = env.observation_builder.build_batch_observations(
       agent_ids=env.agents,
       satellites=env.satellites,
       task_queues=env.task_queues
   )
   ```

3. **缓存机制**
   ```python
   # 缓存不变的可见性矩阵
   if self.current_timeslot == self.cached_timeslot:
       return self.cached_visibility_matrices
   ```

### 10.2 内存优化

1. **使用适当的数据类型**
   ```python
   # 使用int8而不是int32/int64用于掩码
   task_mask = np.zeros(20, dtype=np.int8)
   
   # 使用float32而不是float64
   observations = np.zeros(shape, dtype=np.float32)
   ```

2. **稀疏矩阵表示**
   ```python
   from scipy.sparse import csr_matrix
   
   # 对于大型稀疏可见性矩阵
   visibility_sparse = csr_matrix(visibility_matrix)
   ```

### 10.3 并行化

1. **多进程环境**
   ```python
   from multiprocessing import Pool
   
   def create_env_fn():
       return make_env()
   
   # 创建多个环境实例
   num_envs = 4
   envs = [create_env_fn() for _ in range(num_envs)]
   ```

2. **异步执行**
   ```python
   import asyncio
   
   async def async_step(env, actions):
       return await asyncio.to_thread(env.step, actions)
   ```

### 10.4 性能监控

```python
import time
import cProfile

# 性能分析
profiler = cProfile.Profile()
profiler.enable()

# 运行环境
start_time = time.time()
for _ in range(1000):
    env.step(actions)
elapsed = time.time() - start_time

profiler.disable()
profiler.print_stats(sort='cumulative')

print(f"FPS: {1000 / elapsed:.1f}")
```

---

## 11. 常见问题

### Q1: 为什么任务队列总是空的？

**A**: 任务生成器需要加载地理位置数据。确保调用：
```python
env.task_generator.load_locations_from_csv(
    "src/env/env_data/global_ground_stations.csv"
)
```

### Q2: 如何处理变长任务队列？

**A**: 使用`task_mask`识别真实任务：
```python
for i in range(20):
    if observation['task_mask'][i] == 1:
        # 这是一个真实任务
        process_task(observation['task_queue'][i])
```

### Q3: 动作被标记为无效的原因？

**A**: 检查以下约束：
1. 目标卫星是否可见
2. 是否有地面站连接（云转发）
3. 目标节点是否有可用资源

### Q4: 如何调整奖励平衡？

**A**: 修改配置文件中的`reward_weights`：
```yaml
reward_weights:
  completion: 20.0  # 增加完成权重
  energy: -0.0001  # 减少能耗惩罚
```

### Q5: 环境运行缓慢怎么办？

**A**: 尝试以下优化：
1. 减少`max_timeslots`
2. 使用更少的智能体进行测试
3. 启用向量化操作
4. 使用多进程并行

### Q6: 如何保存和加载训练检查点？

**A**: 
```python
# 保存环境状态
state = {
    'timeslot': env.current_timeslot,
    'task_queues': env.task_queues,
    'statistics': env.get_statistics()
}
torch.save(state, 'checkpoint.pt')

# 加载状态
state = torch.load('checkpoint.pt')
env.current_timeslot = state['timeslot']
env.task_queues = state['task_queues']
```

### Q7: 如何自定义任务生成？

**A**: 继承并覆盖`TaskGenerator`类：
```python
class CustomTaskGenerator(TaskGenerator):
    def generate_tasks_for_timeslot(self, timeslot):
        # 自定义任务生成逻辑
        return custom_tasks
        
env.task_generator = CustomTaskGenerator()
```

### Q8: 如何可视化环境状态？

**A**: 使用内置的render方法或自定义可视化：
```python
# 文本渲染
env.render()

# 自定义可视化
import matplotlib.pyplot as plt

def visualize_satellite_states(env):
    states = []
    for agent_id in env.agents:
        stat = env.satellites[agent_id].get_statistics()
        states.append(stat['avg_utilization'])
    
    plt.bar(range(len(states)), states)
    plt.xlabel('Satellite ID')
    plt.ylabel('Utilization')
    plt.show()
```

---

## 附录A: 环境参数速查表

| 参数 | 默认值 | 说明 |
|------|--------|------|
| num_satellites | 72 | LEO卫星数量 |
| num_ground_stations | 420 | 地面站数量 |
| num_cloud_centers | 5 | 云中心数量 |
| max_timeslots | 1441 | 最大仿真时隙 |
| timeslot_duration | 5s | 每时隙持续时间 |
| max_queue_size | 20 | 最大任务队列长度 |
| max_visible_neighbors | 10 | 最大可见邻居数 |

## 附录B: 数据文件格式

### 卫星轨道数据 (satellite_data72_1.csv)
```csv
satellite_ID,time_slot,time,lat,lon,light,state
111,0,0,45.2,120.5,1,active
...
```

### 地面站数据 (global_ground_stations.csv)
```csv
ID,Latitude,Longitude,RegionType,Size,PurposeType
1,40.7,-74.0,Land,Large,Normal
...
```

## 附录C: 性能基准

| 指标 | 数值 | 测试条件 |
|------|------|----------|
| FPS | 221.9 | 72智能体，单CPU |
| 内存占用 | ~2GB | 完整环境 |
| 初始化时间 | ~1.5s | 包含数据加载 |
| 单步时间 | ~4.5ms | 包含所有计算 |

---

## 联系与支持

- 项目仓库：[GitHub Repository]
- 问题反馈：[Issue Tracker]
- 技术文档：`docs/`目录
- 示例代码：`test/env/space_env/`

---

*最后更新：2025年8月*