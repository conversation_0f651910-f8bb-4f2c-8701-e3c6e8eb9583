#!/usr/bin/env python3
"""
完整轨道性能测试 - 2000时隙可见性矩阵计算
测试GPU加速版orbital.py的性能和正确性
使用真实卫星数据进行完整的2000时隙计算
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
import logging
import psutil
import gc

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('orbital_performance_test.log'),
        logging.StreamHandler()
    ]
)

def test_orbital_performance():
    """完整的轨道性能测试"""
    print("=" * 80)
    print("轨道模块完整性能测试 - 2000时隙可见性矩阵计算")
    print("=" * 80)
    
    try:
        # 导入orbital模块
        from src.env.physics_layer.orbital import OrbitalUpdater
        print("[SUCCESS] 成功导入OrbitalUpdater")
        
        # 初始化轨道更新器
        start_init = time.time()
        orbital = OrbitalUpdater()
        init_time = time.time() - start_init
        
        print(f"[SUCCESS] 轨道更新器初始化完成 ({init_time:.3f}s)")
        print(f"[INFO] GPU支持: {getattr(orbital, 'use_gpu', False)}")
        print(f"[INFO] 设备: {getattr(orbital, 'device', 'CPU')}")
        print(f"[INFO] 总时隙数: {orbital.get_total_timeslots()}")
        print(f"[INFO] 地面站数量: {orbital.get_ground_station_count()}")
        print(f"[INFO] 云中心数量: {orbital.get_cloud_station_count()}")
        
        # 验证数据完整性
        print("\n" + "=" * 60)
        print("数据完整性验证")
        print("=" * 60)
        
        # 检查几个时隙的卫星数量
        sample_timeslots = [0, 100, 500, 1000, 1500, 1999]
        for ts in sample_timeslots:
            if ts < orbital.get_total_timeslots():
                satellites = orbital.get_satellites_at_time(ts)
                print(f"[INFO] 时隙 {ts}: {len(satellites)} 颗卫星")
            else:
                print(f"[WARNING] 时隙 {ts} 超出范围")
        
        # 性能测试配置
        test_timeslots = min(2000, orbital.get_total_timeslots())
        print(f"\n[INFO] 将测试 {test_timeslots} 个时隙")
        
        # 存储结果的数据结构
        results = {
            'timeslot': [],
            'satellite_count': [],
            'inter_sat_computation_time': [],
            'sat_ground_computation_time': [],
            'sat_cloud_computation_time': [],
            'total_computation_time': [],
            'inter_sat_visible_links': [],
            'sat_ground_visible_links': [],
            'sat_cloud_visible_links': [],
            'memory_usage_mb': [],
            'inter_sat_matrix_size': [],
            'sat_ground_matrix_size': [],
            'sat_cloud_matrix_size': []
        }
        
        print("\n" + "=" * 60)
        print("开始完整性能测试")
        print("=" * 60)
        
        total_start_time = time.time()
        
        # 批量处理，每100个时隙报告一次进度
        batch_size = 100
        for batch_start in range(0, test_timeslots, batch_size):
            batch_end = min(batch_start + batch_size, test_timeslots)
            batch_start_time = time.time()
            
            for timeslot in range(batch_start, batch_end):
                timeslot_start = time.time()
                
                # 获取当前时隙的卫星
                satellites = orbital.get_satellites_at_time(timeslot)
                satellite_count = len(satellites)
                
                if satellite_count == 0:
                    print(f"[WARNING] 时隙 {timeslot} 无卫星数据，跳过")
                    continue
                
                # 1. 星间可见性矩阵
                inter_sat_start = time.time()
                inter_sat_vis, _ = orbital.build_visibility_matrix(satellites)
                inter_sat_time = time.time() - inter_sat_start
                inter_sat_links = np.sum(inter_sat_vis)
                inter_sat_size = f"{inter_sat_vis.shape[0]}x{inter_sat_vis.shape[1]}"

                # 2. 卫星-地面站可见性矩阵
                sat_ground_start = time.time()
                sat_ground_vis, _ = orbital.build_satellite_ground_visibility_matrix(satellites, timeslot)
                sat_ground_time = time.time() - sat_ground_start
                sat_ground_links = np.sum(sat_ground_vis)
                sat_ground_size = f"{sat_ground_vis.shape[0]}x{sat_ground_vis.shape[1]}"

                # 3. 卫星-云中心可见性矩阵
                sat_cloud_start = time.time()
                sat_cloud_vis, _ = orbital.build_satellite_cloud_visibility_matrix(satellites, timeslot)
                sat_cloud_time = time.time() - sat_cloud_start
                sat_cloud_links = np.sum(sat_cloud_vis)
                sat_cloud_size = f"{sat_cloud_vis.shape[0]}x{sat_cloud_vis.shape[1]}"

                # 内存使用情况
                memory_usage = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                
                total_time = time.time() - timeslot_start
                
                # 记录结果
                results['timeslot'].append(timeslot)
                results['satellite_count'].append(satellite_count)
                results['inter_sat_computation_time'].append(inter_sat_time)
                results['sat_ground_computation_time'].append(sat_ground_time)
                results['sat_cloud_computation_time'].append(sat_cloud_time)
                results['total_computation_time'].append(total_time)
                results['inter_sat_visible_links'].append(inter_sat_links)
                results['sat_ground_visible_links'].append(sat_ground_links)
                results['sat_cloud_visible_links'].append(sat_cloud_links)
                results['memory_usage_mb'].append(memory_usage)
                results['inter_sat_matrix_size'].append(inter_sat_size)
                results['sat_ground_matrix_size'].append(sat_ground_size)
                results['sat_cloud_matrix_size'].append(sat_cloud_size)

                # 定期清理内存
                if timeslot % 100 == 0:
                    gc.collect()
            
            batch_time = time.time() - batch_start_time
            progress = (batch_end / test_timeslots) * 100
            avg_time_per_slot = batch_time / (batch_end - batch_start)
            
            print(f"[PROGRESS] 时隙 {batch_start}-{batch_end-1} 完成 "
                  f"({progress:.1f}%) - 批次耗时: {batch_time:.2f}s, "
                  f"平均每时隙: {avg_time_per_slot:.3f}s")
        
        total_test_time = time.time() - total_start_time
        
        print("\n" + "=" * 60)
        print("测试完成 - 性能统计")
        print("=" * 60)
        
        # 计算统计信息
        df_results = pd.DataFrame(results)
        
        print(f"[SUMMARY] 总测试时间: {total_test_time:.2f}s")
        print(f"[SUMMARY] 测试时隙数: {len(df_results)}")
        print(f"[SUMMARY] 平均每时隙计算时间: {df_results['total_computation_time'].mean():.4f}s")
        print(f"[SUMMARY] 最大每时隙计算时间: {df_results['total_computation_time'].max():.4f}s")
        print(f"[SUMMARY] 最小每时隙计算时间: {df_results['total_computation_time'].min():.4f}s")
        
        print(f"\n[MATRIX] 星间可见性矩阵:")
        print(f"  - 平均计算时间: {df_results['inter_sat_computation_time'].mean():.4f}s")
        print(f"  - 平均可见链路数: {df_results['inter_sat_visible_links'].mean():.1f}")
        
        print(f"\n[MATRIX] 卫星-地面站可见性矩阵:")
        print(f"  - 平均计算时间: {df_results['sat_ground_computation_time'].mean():.4f}s")
        print(f"  - 平均可见链路数: {df_results['sat_ground_visible_links'].mean():.1f}")
        
        print(f"\n[MATRIX] 卫星-云中心可见性矩阵:")
        print(f"  - 平均计算时间: {df_results['sat_cloud_computation_time'].mean():.4f}s")
        print(f"  - 平均可见链路数: {df_results['sat_cloud_visible_links'].mean():.1f}")
        
        # 保存详细结果
        results_file = 'orbital_performance_results.csv'
        df_results.to_csv(results_file, index=False)
        print(f"\n[SUCCESS] 详细结果已保存到: {results_file}")

        # 保存几个时隙的矩阵样本用于验证
        save_matrix_samples(orbital, [0, 500, 1000, 1500])

        # 内存使用分析
        print(f"\n[MEMORY] 内存使用分析:")
        print(f"  - 平均内存使用: {df_results['memory_usage_mb'].mean():.1f} MB")
        print(f"  - 最大内存使用: {df_results['memory_usage_mb'].max():.1f} MB")
        print(f"  - 最小内存使用: {df_results['memory_usage_mb'].min():.1f} MB")

        # GPU性能统计（如果可用）
        if hasattr(orbital, 'get_performance_stats'):
            gpu_stats = orbital.get_performance_stats()
            if gpu_stats:
                print(f"\n[GPU] GPU性能统计:")
                for key, value in gpu_stats.items():
                    print(f"  - {key}: {value}")

        # 生成性能报告
        generate_performance_report(df_results, total_test_time)

        print("\n" + "=" * 60)
        print("测试成功完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_matrix_samples(orbital, sample_timeslots):
    """保存矩阵样本用于验证"""
    print(f"\n[INFO] 保存矩阵样本...")

    for timeslot in sample_timeslots:
        try:
            satellites = orbital.get_satellites_at_time(timeslot)
            if len(satellites) == 0:
                continue

            # 计算矩阵
            inter_sat_vis, inter_sat_dist = orbital.build_visibility_matrix(satellites)
            sat_ground_vis, sat_ground_dist = orbital.build_satellite_ground_visibility_matrix(satellites, timeslot)
            sat_cloud_vis, sat_cloud_dist = orbital.build_satellite_cloud_visibility_matrix(satellites, timeslot)

            # 保存为numpy文件
            np.savez(f'matrices_timeslot_{timeslot}.npz',
                    inter_sat_vis=inter_sat_vis,
                    inter_sat_dist=inter_sat_dist,
                    sat_ground_vis=sat_ground_vis,
                    sat_ground_dist=sat_ground_dist,
                    sat_cloud_vis=sat_cloud_vis,
                    sat_cloud_dist=sat_cloud_dist,
                    satellite_count=len(satellites))

            print(f"  - 时隙 {timeslot}: 矩阵已保存 (卫星数: {len(satellites)})")

        except Exception as e:
            print(f"  - 时隙 {timeslot}: 保存失败 - {e}")

def generate_performance_report(df_results, total_time):
    """生成性能报告"""
    report_file = 'orbital_performance_report.txt'

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("轨道模块性能测试报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"测试时间: {datetime.now()}\n")
        f.write(f"总测试时长: {total_time:.2f}秒\n")
        f.write(f"测试时隙数: {len(df_results)}\n\n")

        f.write("性能统计:\n")
        f.write("-" * 30 + "\n")
        f.write(f"平均每时隙计算时间: {df_results['total_computation_time'].mean():.4f}s\n")
        f.write(f"最大每时隙计算时间: {df_results['total_computation_time'].max():.4f}s\n")
        f.write(f"最小每时隙计算时间: {df_results['total_computation_time'].min():.4f}s\n")
        f.write(f"标准差: {df_results['total_computation_time'].std():.4f}s\n\n")

        f.write("矩阵计算性能:\n")
        f.write("-" * 30 + "\n")
        f.write(f"星间可见性矩阵平均时间: {df_results['inter_sat_computation_time'].mean():.4f}s\n")
        f.write(f"卫星-地面站矩阵平均时间: {df_results['sat_ground_computation_time'].mean():.4f}s\n")
        f.write(f"卫星-云中心矩阵平均时间: {df_results['sat_cloud_computation_time'].mean():.4f}s\n\n")

        f.write("可见性统计:\n")
        f.write("-" * 30 + "\n")
        f.write(f"平均星间可见链路数: {df_results['inter_sat_visible_links'].mean():.1f}\n")
        f.write(f"平均卫星-地面站链路数: {df_results['sat_ground_visible_links'].mean():.1f}\n")
        f.write(f"平均卫星-云中心链路数: {df_results['sat_cloud_visible_links'].mean():.1f}\n\n")

        f.write("内存使用:\n")
        f.write("-" * 30 + "\n")
        f.write(f"平均内存使用: {df_results['memory_usage_mb'].mean():.1f} MB\n")
        f.write(f"最大内存使用: {df_results['memory_usage_mb'].max():.1f} MB\n")
        f.write(f"最小内存使用: {df_results['memory_usage_mb'].min():.1f} MB\n")

    print(f"[SUCCESS] 性能报告已保存到: {report_file}")

def validate_data_integrity():
    """验证数据完整性"""
    print("\n" + "=" * 60)
    print("数据完整性验证")
    print("=" * 60)
    
    try:
        from src.env.physics_layer.orbital import OrbitalUpdater
        orbital = OrbitalUpdater()
        
        # 检查数据文件
        print(f"[INFO] 卫星数据文件: {orbital.data_file}")
        print(f"[INFO] 配置文件: {orbital.config_file}")
        
        # 检查数据范围
        total_timeslots = orbital.get_total_timeslots()
        print(f"[INFO] 配置的总时隙数: {total_timeslots}")
        
        # 检查实际数据范围
        if hasattr(orbital, 'satellite_data') and not orbital.satellite_data.empty:
            actual_timeslots = len(orbital.satellite_data.index.unique())
            print(f"[INFO] 实际数据时隙数: {actual_timeslots}")
            
            # 检查数据质量
            sample_satellites = orbital.get_satellites_at_time(0)
            if sample_satellites:
                sample_sat = list(sample_satellites.values())[0]
                print(f"[INFO] 样本卫星: {sample_sat}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 数据验证失败: {e}")
        return False

if __name__ == "__main__":
    print("轨道模块完整性能测试")
    print(f"开始时间: {datetime.now()}")
    
    # 验证数据完整性
    if not validate_data_integrity():
        print("数据验证失败，退出测试")
        sys.exit(1)
    
    # 运行性能测试
    success = test_orbital_performance()
    
    print(f"\n结束时间: {datetime.now()}")
    
    if success:
        print("测试成功完成！")
        sys.exit(0)
    else:
        print("测试失败！")
        sys.exit(1)
