"""
验证卫星任务分配是否正确工作
"""

import sys
from pathlib import Path
import numpy as np
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.task_generator import TaskGenerator

# 初始化
config_path = Path(__file__).parent.parent.parent / 'src/env/physics_layer/config.yaml'
orbital = OrbitalUpdater(None, str(config_path))
generator = TaskGenerator()
csv_path = Path(__file__).parent.parent.parent / 'src/env/env_data/global_ground_stations.csv'
generator.load_locations_from_csv(str(csv_path))

# 获取卫星ID
actual_satellite_ids = sorted([int(sid) for sid in orbital.get_satellite_ids(1)])
print(f"卫星ID列表: {actual_satellite_ids}")
print(f"卫星数量: {len(actual_satellite_ids)}")

# 创建映射
satellite_id_mapping = {}  # idx -> actual_id
satellite_index_mapping = {}  # actual_id -> idx

for idx, actual_sat_id in enumerate(actual_satellite_ids):
    satellite_id_mapping[idx] = actual_sat_id
    satellite_index_mapping[actual_sat_id] = idx

print(f"\n映射示例:")
print(f"  索引0 -> 卫星ID {satellite_id_mapping[0]}")
print(f"  索引71 -> 卫星ID {satellite_id_mapping[71]}")
print(f"  卫星ID 111 -> 索引 {satellite_index_mapping[111]}")
print(f"  卫星ID 198 -> 索引 {satellite_index_mapping[198]}")

# 测试时隙100的任务分配
timeslot = 100
satellites_dict = orbital.get_satellites_at_time(timeslot)
sg_visibility, sg_distances = orbital.build_satellite_ground_visibility_matrix(satellites_dict, timeslot)

print(f"\n时隙{timeslot}的可见性矩阵形状: {sg_visibility.shape}")

# 模拟任务分配统计
satellite_task_counts = [0] * len(actual_satellite_ids)  # 72个卫星的任务计数

# 测试几个地面站的任务分配
test_stations = [
    (0, "南极站", -65, -180),  # ID=1
    (209, "赤道站", 5, -180),  # ID=210
    (389, "北极站", 65, -180),  # ID=390
]

for ground_id, name, lat, lon in test_stations:
    print(f"\n{name} (ID={ground_id+1}, 位置=({lat}, {lon})):")
    
    # 找可见卫星
    visible_sats = []
    for sat_idx in range(sg_visibility.shape[0]):
        if sg_visibility[sat_idx, ground_id]:
            actual_sat_id = satellite_id_mapping[sat_idx]
            sat_data = satellites_dict[actual_sat_id]
            visible_sats.append((sat_idx, actual_sat_id, sat_data.latitude, sat_data.longitude))
    
    print(f"  可见卫星数: {len(visible_sats)}")
    if visible_sats:
        # 找最近的（简化距离计算）
        min_dist = float('inf')
        best_idx = None
        for sat_idx, sat_id, sat_lat, sat_lon in visible_sats:
            dist = np.sqrt((sat_lat - lat)**2 + (sat_lon - lon)**2)
            if dist < min_dist:
                min_dist = dist
                best_idx = sat_idx
        
        if best_idx is not None:
            best_sat_id = satellite_id_mapping[best_idx]
            print(f"  最近卫星: 索引={best_idx}, ID={best_sat_id}")
            print(f"  该卫星位置: ({visible_sats[0][2]:.1f}, {visible_sats[0][3]:.1f})")
            
            # 模拟分配10个任务
            satellite_task_counts[best_idx] += 10

print("\n\n任务分配统计:")
print(f"有任务的卫星数: {sum(1 for c in satellite_task_counts if c > 0)}")
print(f"总任务数: {sum(satellite_task_counts)}")

# 统计每个纬度带的任务数
lat_band_tasks = {}
for sat_idx, task_count in enumerate(satellite_task_counts):
    if task_count > 0:
        actual_sat_id = satellite_id_mapping[sat_idx]
        sat_data = satellites_dict[actual_sat_id]
        lat_band = int(sat_data.latitude // 10) * 10
        if lat_band not in lat_band_tasks:
            lat_band_tasks[lat_band] = 0
        lat_band_tasks[lat_band] += task_count

print("\n纬度带任务分布:")
for lat_band in sorted(lat_band_tasks.keys()):
    print(f"  [{lat_band}, {lat_band+10}): {lat_band_tasks[lat_band]} 个任务")

# 检查数组索引是否正确
print("\n\n验证数组索引:")
print(f"satellites数组应该有 {len(actual_satellite_ids)} 个元素（索引0-{len(actual_satellite_ids)-1}）")
print(f"可见性矩阵第一维: {sg_visibility.shape[0]} （应该等于卫星数）")
print(f"可见性矩阵第二维: {sg_visibility.shape[1]} （应该等于地面站数420）")

if sg_visibility.shape[0] == len(actual_satellite_ids):
    print("数组维度匹配正确")
else:
    print("数组维度不匹配！")

# 测试多个时隙的可见性
print("\n\n测试多个时隙的可见性变化:")
test_timeslots = [1, 50, 100, 150, 200]
for ts in test_timeslots:
    satellites_dict_ts = orbital.get_satellites_at_time(ts)
    sg_vis_ts, _ = orbital.build_satellite_ground_visibility_matrix(satellites_dict_ts, ts)
    
    # 统计每个纬度带的地面站可见性
    vis_by_lat = {}
    ground_station_list = list(orbital.ground_stations.values())
    for gs_idx, gs in enumerate(ground_station_list):
        lat_band = int(gs.latitude // 10) * 10
        if lat_band not in vis_by_lat:
            vis_by_lat[lat_band] = {'total': 0, 'visible': 0}
        vis_by_lat[lat_band]['total'] += 1
        if np.any(sg_vis_ts[:, gs_idx]):  # 如果有任何卫星可见
            vis_by_lat[lat_band]['visible'] += 1
    
    print(f"\n时隙 {ts}:")
    for lat_band in sorted(vis_by_lat.keys()):
        stats = vis_by_lat[lat_band]
        ratio = stats['visible'] / stats['total'] * 100 if stats['total'] > 0 else 0
        print(f"  纬度带[{lat_band:>3}, {lat_band+10:>3}): {stats['visible']:>2}/{stats['total']:>2} 站点可见 ({ratio:>5.1f}%)")