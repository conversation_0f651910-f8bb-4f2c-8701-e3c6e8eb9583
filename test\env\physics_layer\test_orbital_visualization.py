"""
Orbital Module Visualization Test
轨道模块可视化测试程序

按照CLAUDE.md规范要求：
- 使用绝对导入
- 从config.yaml读取参数  
- 基于时隙输出详细信息
- 可视化输出卫星可见性图

测试时隙: 1-10, 50-60, 100-110
可视化内容:
1. 所有72颗卫星在地图上的位置
2. 所有420个地面站的位置
3. 卫星间可见性连接线
4. 指定卫星(111-118,121-122)与地面站的可见性连接线
"""

# 1. 标准库导入
import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings

# 2. 第三方库导入
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import to_rgba
import yaml

# 忽略matplotlib的警告
warnings.filterwarnings('ignore', category=UserWarning)

# 3. 项目内部导入 (绝对导入)
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.Foundation_Layer.time_manager import create_time_manager_from_config
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError


class OrbitalVisualizationTester:
    """轨道模块可视化测试器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化可视化测试器
        
        Args:
            config_file: 配置文件路径
        """
        # 设置路径
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.config_file = config_file or str(self.project_root / "src" / "env" / "physics_layer" / "config.yaml")
        self.output_dir = Path(__file__).parent / "visualization_output"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化模块
        self.orbital_updater = OrbitalUpdater(config_file=self.config_file)
        self.time_manager = create_time_manager_from_config(self.config)
        
        # 指定要可视化的卫星ID (111-118, 121-122)
        self.selected_satellite_ids = [111, 112, 113, 114, 115, 116, 117, 118, 121, 122]
        
        # 测试时隙
        self.test_timeslots = list(range(1, 11)) + list(range(50, 61)) + list(range(100, 111))
        
        print(f"=== Orbital可视化测试器初始化完成 ===")
        print(f"配置文件: {self.config_file}")
        print(f"输出目录: {self.output_dir}")
        print(f"总时隙数: {self.orbital_updater.get_total_timeslots()}")
        print(f"卫星数量: {len(self.orbital_updater.get_satellite_ids(0))}")
        print(f"地面站数量: {self.orbital_updater.get_ground_station_count()}")
        print(f"测试时隙: {len(self.test_timeslots)}个")
        print(f"指定卫星: {self.selected_satellite_ids}")
        
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            raise
    
    def setup_map_figure(self, timeslot: int) -> Tuple[plt.Figure, plt.Axes]:
        """
        设置地图图表
        
        Args:
            timeslot: 当前时隙
            
        Returns:
            Figure和Axes对象
        """
        # 创建图表
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        
        # 设置世界地图范围
        ax.set_xlim(-180, 180)
        ax.set_ylim(-90, 90)
        
        # 添加网格
        ax.grid(True, alpha=0.3, linestyle='--')
        ax.set_xlabel('经度 (度)', fontsize=12)
        ax.set_ylabel('纬度 (度)', fontsize=12)
        
        # 获取时间信息
        time_context = self.time_manager.get_time_context(timeslot)
        
        # 设置标题
        title = (f"SPACE2 卫星可见性可视化 - 时隙 {timeslot}\n"
                f"仿真时间: {time_context.simulation_time}s | "
                f"物理时间: {time_context.physical_time.strftime('%H:%M:%S')}")
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        # 绘制简单的陆地轮廓（矩形表示主要大陆）
        continents = [
            # 亚洲
            patches.Rectangle((60, 10), 80, 50, linewidth=1, edgecolor='brown', 
                            facecolor='lightgray', alpha=0.3),
            # 欧洲
            patches.Rectangle((-10, 35), 50, 35, linewidth=1, edgecolor='brown', 
                            facecolor='lightgray', alpha=0.3),
            # 非洲
            patches.Rectangle((10, -35), 40, 65, linewidth=1, edgecolor='brown', 
                            facecolor='lightgray', alpha=0.3),
            # 北美洲
            patches.Rectangle((-130, 25), 60, 45, linewidth=1, edgecolor='brown', 
                            facecolor='lightgray', alpha=0.3),
            # 南美洲
            patches.Rectangle((-80, -55), 40, 80, linewidth=1, edgecolor='brown', 
                            facecolor='lightgray', alpha=0.3),
            # 澳洲
            patches.Rectangle((110, -45), 40, 35, linewidth=1, edgecolor='brown', 
                            facecolor='lightgray', alpha=0.3),
        ]
        
        for continent in continents:
            ax.add_patch(continent)
        
        return fig, ax
    
    def plot_satellites(self, ax: plt.Axes, timeslot: int) -> Dict[str, Tuple[float, float]]:
        """
        绘制卫星位置
        
        Args:
            ax: matplotlib轴对象
            timeslot: 时隙
            
        Returns:
            卫星位置字典 {satellite_id: (longitude, latitude)}
        """
        satellites_dict = self.orbital_updater.get_satellites_at_time(timeslot)
        satellite_positions = {}
        
        # 将字典转换为列表进行处理
        satellites = list(satellites_dict.values())
        
        for satellite in satellites:
            sat_id = satellite.satellite_id
            lon, lat = satellite.longitude, satellite.latitude
            satellite_positions[sat_id] = (lon, lat)
            
            # 区分选定的卫星和其他卫星
            if int(sat_id) in self.selected_satellite_ids:
                # 选定卫星用红色大圆点
                ax.scatter(lon, lat, c='red', s=60, marker='o', 
                          edgecolors='darkred', linewidth=1, zorder=5,
                          label='选定卫星' if int(sat_id) == self.selected_satellite_ids[0] else "")
                # 添加卫星ID标签
                ax.annotate(sat_id, (lon, lat), xytext=(5, 5), 
                           textcoords='offset points', fontsize=8, color='red', fontweight='bold')
            else:
                # 其他卫星用橙色小圆点
                ax.scatter(lon, lat, c='orange', s=30, marker='o', 
                          edgecolors='darkorange', linewidth=0.5, zorder=4,
                          label='其他卫星' if len(satellite_positions) == 1 else "")
        
        print(f"  - 绘制卫星: {len(satellites)}颗")
        return satellite_positions
    
    def plot_ground_stations(self, ax: plt.Axes) -> Dict[str, Tuple[float, float]]:
        """
        绘制地面站位置
        
        Args:
            ax: matplotlib轴对象
            
        Returns:
            地面站位置字典 {station_id: (longitude, latitude)}
        """
        ground_stations = list(self.orbital_updater.ground_stations.values())
        ground_positions = {}
        
        for station in ground_stations:
            station_id = station.station_id
            lon, lat = station.longitude, station.latitude
            ground_positions[station_id] = (lon, lat)
            
            # 地面站用蓝色三角形
            ax.scatter(lon, lat, c='blue', s=15, marker='^', 
                      edgecolors='darkblue', linewidth=0.3, zorder=3,
                      label='地面站' if len(ground_positions) == 1 else "")
        
        print(f"  - 绘制地面站: {len(ground_stations)}个")
        return ground_positions
    
    @handle_errors(module="visualization", function="plot_satellite_visibility")
    def plot_satellite_visibility(self, ax: plt.Axes, timeslot: int, 
                                 satellite_positions: Dict[str, Tuple[float, float]]):
        """
        绘制卫星间可见性连接线
        
        Args:
            ax: matplotlib轴对象
            timeslot: 时隙
            satellite_positions: 卫星位置字典
        """
        try:
            # 获取可见性矩阵
            satellites_dict = self.orbital_updater.get_satellites_at_time(timeslot)
            if len(satellites_dict) == 0:
                print(f"  - 警告: 时隙{timeslot}没有可用卫星")
                return
            
            # 构建卫星间可见性矩阵
            visibility_matrix, distance_matrix = self.orbital_updater.build_visibility_matrix(satellites_dict)
            satellite_ids = list(satellites_dict.keys())
            
            connection_count = 0
            
            # 绘制可见性连接线
            for i, sat_id1 in enumerate(satellite_ids):
                for j, sat_id2 in enumerate(satellite_ids):
                    if i < j and visibility_matrix[i, j] > 0:  # 只绘制上三角，避免重复
                        pos1 = satellite_positions.get(sat_id1)
                        pos2 = satellite_positions.get(sat_id2)
                        
                        if pos1 and pos2:
                            # 处理跨越180度经线的情况
                            lon1, lat1 = pos1
                            lon2, lat2 = pos2
                            
                            if abs(lon2 - lon1) > 180:
                                # 跨越日期变更线，不绘制连接线（避免混乱）
                                continue
                            
                            # 绘制连接线
                            ax.plot([lon1, lon2], [lat1, lat2], 'g-', 
                                   alpha=0.3, linewidth=0.5, zorder=2,
                                   label='卫星间可见性' if connection_count == 0 else "")
                            connection_count += 1
            
            print(f"  - 绘制卫星间连接: {connection_count}条")
            
        except Exception as e:
            print(f"  - 错误: 绘制卫星可见性失败: {e}")
            raise SpaceSimulationError(
                message=f"绘制卫星可见性失败: {str(e)}",
                error_code="SATELLITE_VISIBILITY_PLOT_FAILURE"
            )
    
    @handle_errors(module="visualization", function="plot_selected_satellite_ground_visibility")
    def plot_selected_satellite_ground_visibility(self, ax: plt.Axes, timeslot: int,
                                                 satellite_positions: Dict[str, Tuple[float, float]],
                                                 ground_positions: Dict[str, Tuple[float, float]]):
        """
        绘制指定卫星与地面站的可见性连接线
        
        Args:
            ax: matplotlib轴对象
            timeslot: 时隙
            satellite_positions: 卫星位置字典
            ground_positions: 地面站位置字典
        """
        try:
            # 获取选定卫星的数据
            satellites_dict = self.orbital_updater.get_satellites_at_time(timeslot)
            
            # 使用orbital模块的正确可见性计算方法
            sat_ground_visibility_matrix, sat_ground_distance_matrix = \
                self.orbital_updater.build_satellite_ground_visibility_matrix(satellites_dict, timeslot)
            
            satellite_list = list(satellites_dict.values())
            ground_stations = list(self.orbital_updater.ground_stations.values())
            
            connection_count = 0
            
            # 只为选定的卫星绘制与地面站的连接
            for i, satellite in enumerate(satellite_list):
                if int(satellite.satellite_id) not in self.selected_satellite_ids:
                    continue
                    
                sat_pos = satellite_positions.get(satellite.satellite_id)
                if not sat_pos:
                    continue
                
                # 遍历所有地面站，检查可见性
                for j, gs in enumerate(ground_stations):
                    if sat_ground_visibility_matrix[i, j]:  # 使用orbital模块计算的可见性
                        gs_pos = ground_positions.get(gs.station_id)
                        if gs_pos:
                            lon1, lat1 = sat_pos
                            lon2, lat2 = gs_pos
                            
                            # 处理跨越180度经线的情况
                            if abs(lon2 - lon1) > 180:
                                continue
                            
                            # 绘制连接线
                            ax.plot([lon1, lon2], [lat1, lat2], 'purple', 
                                   alpha=0.4, linewidth=0.8, zorder=2,
                                   label='选定卫星-地面站可见性' if connection_count == 0 else "")
                            connection_count += 1
            
            print(f"  - 绘制选定卫星-地面站连接: {connection_count}条")
            
        except Exception as e:
            print(f"  - 错误: 绘制卫星-地面站可见性失败: {e}")
            raise SpaceSimulationError(
                message=f"绘制卫星-地面站可见性失败: {str(e)}",
                error_code="SATELLITE_GROUND_VISIBILITY_PLOT_FAILURE"
            )
    
    def generate_timeslot_visualization(self, timeslot: int) -> str:
        """
        生成单个时隙的可视化图表
        
        Args:
            timeslot: 时隙编号
            
        Returns:
            保存的文件路径
        """
        print(f"\n=== 生成时隙 {timeslot} 可视化图表 ===")
        
        # 设置图表
        fig, ax = self.setup_map_figure(timeslot)
        
        # 绘制各种元素
        satellite_positions = self.plot_satellites(ax, timeslot)
        ground_positions = self.plot_ground_stations(ax)
        
        # 绘制可见性连接
        self.plot_satellite_visibility(ax, timeslot, satellite_positions)
        self.plot_selected_satellite_ground_visibility(ax, timeslot, satellite_positions, ground_positions)
        
        # 添加图例
        ax.legend(loc='upper right', fontsize=10, framealpha=0.8)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        filename = f"orbital_visibility_timeslot_{timeslot:04d}.png"
        filepath = self.output_dir / filename
        
        plt.savefig(filepath, dpi=150, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close(fig)  # 释放内存
        
        print(f"  - 图片已保存: {filepath}")
        return str(filepath)
    
    def run_visualization_test(self):
        """运行完整的可视化测试"""
        print(f"\n{'='*60}")
        print(f"开始轨道模块可视化测试")
        print(f"测试时隙: {self.test_timeslots}")
        print(f"{'='*60}")
        
        generated_files = []
        
        try:
            for i, timeslot in enumerate(self.test_timeslots, 1):
                print(f"\n[{i}/{len(self.test_timeslots)}] 处理时隙 {timeslot}")
                
                # 生成可视化
                filepath = self.generate_timeslot_visualization(timeslot)
                generated_files.append(filepath)
                
                # 进度提示
                if i % 5 == 0:
                    print(f"  进度: {i}/{len(self.test_timeslots)} 完成")
            
            print(f"\n{'='*60}")
            print(f"可视化测试完成！")
            print(f"生成图片: {len(generated_files)}张")
            print(f"保存目录: {self.output_dir}")
            print(f"{'='*60}")
            
            return generated_files
            
        except Exception as e:
            print(f"\n错误: 可视化测试失败: {e}")
            raise


def main():
    """主函数"""
    try:
        # 创建测试器
        tester = OrbitalVisualizationTester()
        
        # 运行测试
        generated_files = tester.run_visualization_test()
        
        print(f"\n测试成功完成，生成了 {len(generated_files)} 个可视化图表。")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()