# algorithm_metrics.py 项目说明文档

## 项目概览

`algorithm_metrics.py`是SPACE2卫星边缘计算仿真系统的**算法性能指标统一提取模块**，专门为算法对比研究提供标准化的性能评估接口。该模块解决了不同算法在性能对比时指标计算不一致、重复实现、缺乏公平性的问题。

核心价值在于为算法研究提供统一、公平、可重复的性能评估标准。通过标准化的指标计算方法，确保不同算法在相同的评估基准下进行对比，提高研究结果的可信度和说服力。模块采用归一化和相对指标设计，消除了不同算法运行时任务负载差异对比较结果的影响，为算法优化和选择提供科学依据。

### 接口定位
- **非独立运行模块**: 作为算法训练和评估的标准化接口，不独立执行
- **服务提供者**: 为强化学习算法、启发式算法等提供统一的性能评估服务
- **数据后处理器**: 在算法训练完成后，对仿真结果进行标准化分析和对比

### 性能考虑
- **数据规模**: 支持处理1441时隙×72卫星×420地面站的大规模仿真数据
- **计算效率**: 采用增量计算和缓存机制，避免重复计算
- **内存优化**: 使用滑动窗口处理，控制内存占用

### 设计目标
- **公平性**: 通过归一化消除负载差异，确保不同算法在相同基准下对比
- **完整性**: 覆盖延迟、能耗、完成率、资源效率四大维度
- **可扩展性**: 支持自定义指标注册和灵活的分析维度
- **高效性**: 增量计算和缓存机制支持大规模数据处理（1441时隙×72卫星×420地面站）
- **科学性**: 提供统计显著性检验和置信区间分析

## 架构总结

### 模块划分
- **AlgorithmMetrics**: 负责核心指标计算和统一接口提供
- **LatencyAnalyzer**: 负责延迟相关指标的深度分析，包括端到端延迟、排队延迟、传输延迟
- **EnergyAnalyzer**: 负责能耗效率分析，包括单位任务能耗、能耗密度、能效比计算
- **CompletionAnalyzer**: 负责任务完成率统计，支持按优先级、任务类型、时间窗口分组分析

### 模块协作方式
数据流向：系统组件 → 原始数据提取 → 指标计算 → 标准化处理 → 对比分析 → 报告生成
- 从TaskTrackingSystem、SatelliteManager等组件提取原始性能数据
- LatencyAnalyzer、EnergyAnalyzer、CompletionAnalyzer并行计算各自领域指标
- MetricsAggregator统一数据格式并进行归一化处理
- ComparisonReporter生成标准化的算法对比报告

## 核心流程

1. **数据收集阶段**: 从各系统组件提取指定时间窗口内的原始性能数据
2. **指标计算阶段**: 并行计算延迟、能耗、完成率等核心指标
3. **归一化处理阶段**: 将绝对指标转换为相对指标和效率指标，消除负载差异影响
4. **统计分析阶段**: 计算均值、方差、分位数等统计特征
5. **对比准备阶段**: 生成标准化的指标报告，支持多算法横向对比
6. **结果输出阶段**: 提供JSON、CSV等多种格式的结果输出

关键业务逻辑：通过**归一化指标设计**解决不同算法运行时负载差异问题，如"平均每任务延迟"、"单位数据能耗"等相对指标。

## 外部集成

- **与TaskTrackingSystem的集成**: 获取任务完成状态、处理时间、重传次数等核心数据
- **与SatelliteManager的集成**: 提取卫星资源利用率、能耗统计、队列状态等信息
- **与CommunicationManager的集成**: 获取网络传输延迟、通信能耗、链路质量等数据
- **与TimeManager的集成**: 确保时间窗口的准确性和一致性
- **与算法模块的集成**: 为各种调度算法提供统一的性能评估接口

## 关键设计决策

- **决策1 - 归一化指标设计**: 使用"平均每任务延迟"、"平均每任务能耗"等相对指标，消除不同算法运行时任务数量的影响
- **决策2 - 多维度完成率统计**: 按优先级（高/中/低）分别统计完成率，揭示算法对不同重要性任务的处理能力差异
- **决策3 - 时间窗口分析**: 支持灵活的时间窗口设置，可分析算法在不同阶段（启动期/稳定期/高负载期）的性能表现
- **决策4 - 统计显著性验证**: 提供置信区间和显著性检验，确保算法对比结果的统计可靠性
- **决策5 - 可扩展指标框架**: 预留接口支持新增自定义指标，适应不同研究需求
- **决策6 - 增量计算优化**: 采用滑动窗口和缓存机制，避免重复计算历史数据
- **决策7 - 异常值处理**: 使用IQR方法识别和处理异常值，提高指标稳定性

## 技术栈

- **主要框架**: Python标准库 + NumPy + SciPy
- **统计分析**: SciPy.stats用于统计检验和置信区间计算
- **数据处理**: Pandas用于复杂数据聚合和分组分析
- **核心库**: 
  - `numpy`: 高效的数值计算和矩阵操作
  - `scipy.stats`: 统计分析和假设检验
  - `dataclasses`: 标准化的数据结构定义
  - `typing`: 完整的类型注解支持

## 重要配置

- **时间窗口设置**: 支持自定义分析时间范围，默认分析完整仿真周期
- **优先级映射**: 高优先级(1)、中优先级(2)、低优先级(3)的分组规则
- **统计置信度**: 默认95%置信区间，可调整统计显著性水平
- **归一化基准**: 可选择不同的归一化基准（任务数量、数据大小、处理时间等）

## 核心指标定义

### 1. 延迟指标族
#### 基础延迟指标
- **端到端延迟**: 任务完成时间 - 任务生成时间
  - 平均值(Mean)、中位数(Median)、P50、P90、P99分位数
  - 标准差(Std)和变异系数(CV = Std/Mean)
#### 延迟分解指标
- **排队延迟**: 处理开始时间 - 任务到达时间
- **传输延迟**: 上行传输时间 + 下行传输时间
  - 上行延迟 = 数据大小(MB) / 上行带宽(MB/s)
  - 下行延迟 = 结果大小(MB) / 下行带宽(MB/s)
- **处理延迟**: 任务处理结束时间 - 处理开始时间
- **重传延迟**: Σ(重传间隔) × 重传次数

#### 归一化延迟指标
- **平均每任务延迟**: 总延迟时间 ÷ 完成任务数
- **单位数据延迟**: 总延迟时间 ÷ 处理数据量(MB)

### 2. 能耗指标族
#### 基础能耗指标
- **总能耗**: 计算能耗 + 通信能耗 + 待机能耗
  - 计算能耗 = Σ(CPU功率 × 处理时间)
  - 通信能耗 = Σ(发射功率 × 传输时间)

#### 能效指标
- **单位任务能耗**: 总能耗 ÷ 完成任务数 (J/task)
- **单位数据能耗**: 总能耗 ÷ 处理数据量 (J/MB)

#### 能耗分布指标
- **能耗均衡度**: 1 - (各卫星能耗标准差 ÷ 平均能耗)
- **能耗基尼系数**: 衡量能耗分布不均衡程度
- **峰值能耗比**: 最大能耗 ÷ 平均能耗

### 3. 完成率指标族
#### 基础完成率
- **总体完成率**: 完成任务数 ÷ 总任务数 × 100%
- **及时完成率**: 截止期前完成数 ÷ 总任务数 × 100%
- **超时率**: 超时任务数 ÷ 总任务数 × 100%

#### 分优先级完成率
- **高优先级(P1)完成率**: P1完成数 ÷ P1总数 × 100%
- **中优先级(P2)完成率**: P2完成数 ÷ P2总数 × 100%
- **低优先级(P3)完成率**: P3完成数 ÷ P3总数 × 100%
- **加权完成率**: Σ(优先级权重 × 各级完成率)


### 5. 综合性能指标
 暂不完成


## 性能优化策略

### 增量计算设计
- **滑动窗口更新**: 避免重复计算历史数据
- **缓存机制**: 分层缓存原始数据和聚合指标
- **并行处理**: 不同维度指标独立计算

### 缓存机制
- **L1缓存**: 最近5个时隙的原始数据
- **L2缓存**: 最近20个时隙的聚合指标
- **持久化缓存**: 关键统计结果定期保存

### 并行处理
- **指标并行**: 不同维度指标独立计算
- **卫星并行**: 各卫星数据并行处理
- **时隙批处理**: 批量处理连续时隙

## 错误处理机制

### 数据验证
- **完整性检查**: 验证必需字段存在
- **一致性检查**: 时间戳连续性、ID唯一性
- **范围检查**: 数值在合理范围内

### 异常处理
- **缺失值处理**: 插值或使用默认值
- **计算错误**: 捕获除零、溢出等异常
- **降级策略**: 部分指标失败时返回可用结果

## 接口使用示例

### 基础调用接口
```python
# 初始化指标提取器
metrics = AlgorithmMetrics(config)

# 从训练结果中提取指标
results = metrics.extract_metrics_from_simulation(
    simulation_results=training_output,
    time_window=(start_time, end_time)
)

# 算法对比分析
comparison = metrics.compare_algorithms([
    {'name': 'DQN', 'results': dqn_results},
    {'name': 'PPO', 'results': ppo_results},
    {'name': 'DPSQ', 'results': dpsq_results}
])
```

### 批量评估接口
```python
# 批量算法评估
batch_results = metrics.batch_evaluate([
    'algorithm_a_output.json',
    'algorithm_b_output.json', 
    'algorithm_c_output.json'
])

# 生成对比报告
report = metrics.generate_comparison_report(
    batch_results,
    output_format=['json', 'csv', 'latex']
)
```



## 学习要点

1. **指标标准化**: 通过归一化设计解决不同算法运行条件差异，提高对比公平性
2. **统计方法**: 使用置信区间和显著性检验确保结果可靠性，避免偶然性结论
3. **多维度分析**: 按优先级分组分析揭示算法特性，比单一指标更有说服力
4. **相对指标设计**: "平均每任务"、"单位数据"等相对指标比绝对数值更具可比性
5. **模块化设计**: 各分析器独立实现，便于维护和扩展新指标
6. **数据驱动**: 基于真实仿真数据计算，避免理论假设与实际运行的偏差
7. **可重复性**: 标准化的计算方法确保不同研究者能得到一致的评估结果
8. **性能意识**: 大规模数据处理需要增量计算和缓存优化
9. **鲁棒性设计**: 完善的错误处理确保部分失败不影响整体
10. **接口导向**: 作为服务接口为算法训练提供标准化评估，而非独立运行程序
