SPACE2系统测试文档编写提示词
测试目标
请基于SPACE-DMPO1卫星边缘计算仿真环境的技术文档，编写全面的系统测试代码，测试指定时隙（1-5, 100-105, 1000-1005）的所有核心模块功能，并输出详细的测试结果。

测试范围和要求
测试时隙
早期时隙: 1, 2, 3, 4, 5 (系统启动阶段)
中期时隙: 100, 101, 102, 103, 104, 105 (系统稳定运行阶段)
后期时隙: 1000, 1001, 1002, 1003, 1004, 1005 (系统长期运行阶段)
核心模块测试要求
1. 时间管理系统 (time_manager.py)
输出要求：

# 每个时隙输出：
- 仿真步数 (simulation_step)
- 仿真时间 (simulation_time) 
- 物理时间 (physical_time)
- 时隙时长 (timeslot_duration)
- 时间转换验证结果
2. 轨道更新系统 (orbital.py)
输出要求：

# 每个时隙输出：
- 72颗卫星的位置信息 (纬度、经度、光照状态)
- 卫星间可见性矩阵 (72x72) - 显示连通性统计
- 卫星-地面可见性矩阵 (72x420) - 显示覆盖统计
- 卫星-云可见性矩阵 (72x5) - 显示云连接统计
- 距离矩阵关键统计 (最小/最大/平均距离)
- 可见性变化分析 (与上一时隙对比)
3. 通信链路管理 (communication_refactored.py)
输出要求：

# 每个时隙输出：
- 所有活跃链路的物理参数：
  * 链路数量统计 (卫星间/卫星-地面/卫星-云)
  * 数据传输速率 (最小/最大/平均 Mbps)
  * 传输延迟 (最小/最大/平均 ms)
  * 信号强度 (最小/最大/平均 dBm)
  * 信噪比 (最小/最大/平均 dB)
  * 传输能耗 (总能耗/平均能耗 J)
- 链路质量评估和异常检测
注意，是输出要求时隙的所有链路的链路状态。

4. 卫星节点系统 (satellite.py)
输出要求：
# 每个时隙输出每颗卫星的状态：
- 能量管理器状态：
  * 电池电量百分比
  * 充电状态 (是否光照)
  * 能量消耗速率
  * 能量预测信息
- 资源管理器状态：
  * CPU利用率 (核心使用情况)
- 任务调度器状态：
  * 任务队列长度
  * 正在执行的任务数
  * 已完成任务统计
  * 任务优先级分布
- 通信管理器状态：
  * 邻居卫星数量
  * 可见地面站数量
  * 通信链路质量

5. 任务管理系统 (task.py)
输出要求：
# 每个时隙输出：
- 新生成任务统计：
  * 任务数量 (按类型分组)
  * 任务大小分布 (数据大小MB)
  * 任务复杂度分布 (CPU周期)
  * 任务优先级分布
  * 任务截止时间分布
- 任务状态转换统计：
  * GENERATED → QUEUED 数量
  * QUEUED → PROCESSING 数量
  * PROCESSING → COMPLETED 数量
  * FAILED/TIMEOUT 数量
- 动态优先级计算结果
- 任务分配决策分析
6. 云服务器管理 (cloud_server.py)
输出要求：

# 每个时隙输出：
- 5个云计算中心状态：
  * 任务队列长度 (每个云中心4个队列)
  * 正在处理的任务数 (最多5个并行)
  * 处理完成的任务数
  * 平均处理时间
  * 队列等待时间
- 云任务分配策略效果
- 云-卫星通信状态
7. 环境组件系统 (environment_components.py)
输出要求：

# 每个时隙输出：
- StateManager状态：
  * 系统状态一致性检查结果
  * 状态同步耗时
  * 状态快照大小
- TaskManager状态：
  * 任务分配策略执行结果
  * 任务性能指标
  * 负载均衡效果
- RewardCalculator状态：
  * 多维度奖励计算结果
  * 奖励分布统计
  * 性能指标评估
- ObservationManager状态：
  * 观测空间数据质量
  * 动作掩码生成结果
  * 观测归一化效果
8. 性能优化系统 (performance_optimizations.py)
输出要求：

# 每个时隙输出：
- 缓存系统状态：
  * 各级缓存命中率
  * 缓存内存使用情况
  * 缓存清理统计
- 内存优化状态：
  * 系统内存使用率
  * 垃圾回收统计
  * 内存泄漏检测
- 计算优化状态：
  * 向量化计算效果
  * 并行计算性能
  * 计算瓶颈识别
测试代码结构要求
1. 主测试框架
class SPACE2SystemTester:
    """SPACE2系统全面测试器"""
    
    def __init__(self):
        # 初始化所有测试组件
        pass
    
    def run_comprehensive_test(self, test_timeslots: List[int]):
        """运行指定时隙的全面测试"""
        for timeslot in test_timeslots:
            print(f"\n{'='*80}")
            print(f"时隙 {timeslot} 系统状态全面测试")
            print(f"{'='*80}")
            
            self.test_time_management(timeslot)
            self.test_orbital_system(timeslot)
            self.test_communication_system(timeslot)
            self.test_satellite_nodes(timeslot)
            self.test_task_management(timeslot)
            self.test_cloud_servers(timeslot)
            self.test_environment_components(timeslot)
            self.test_performance_systems(timeslot)
            
            self.generate_timeslot_summary(timeslot)
2. 详细输出格式要求
def format_test_output(self, module_name: str, timeslot: int, data: Dict):
    """标准化测试输出格式"""
    print(f"\n--- {module_name} 测试结果 (时隙 {timeslot}) ---")
    
    # 核心数据表格化输出
    if 'matrix_data' in data:
        self.print_matrix_summary(data['matrix_data'])
    
    if 'statistics' in data:
        self.print_statistics_table(data['statistics'])
    
    if 'status_list' in data:
        self.print_status_table(data['status_list'])
    
    # 异常和警告
    if 'warnings' in data:
        self.print_warnings(data['warnings'])
    
    # 性能指标
    if 'performance' in data:
        self.print_performance_metrics(data['performance'])
3. 数据验证要求
def validate_test_results(self, module_name: str, results: Dict) -> bool:
    """验证测试结果的正确性"""
    validation_rules = {
        'orbital': {
            'satellite_count': 72,
            'ground_station_count': 420,
            'cloud_center_count': 5,
            'coordinate_range': {'lat': (-90, 90), 'lon': (-180, 180)}
        },
        'communication': {
            'max_inter_sat_distance': 2000,  # km
            'max_ground_distance': 1000,     # km
            'max_cloud_distance': 1200,      # km
            'data_rate_range': (0, 1000)     # Mbps
        },
        'energy': {
            'battery_range': (0, 100),       # percentage
            'solar_power_max': 200,          # W
            'base_power_min': 50             # W
        }
    }
    
    # 执行验证逻辑
    return self._perform_validation(module_name, results, validation_rules)
4. 比较分析要求
def analyze_timeslot_progression(self, all_results: Dict):
    """分析时隙间的变化趋势"""
    print(f"\n{'='*80}")
    print("时隙进展分析报告")
    print(f"{'='*80}")
    
    # 轨道变化分析
    self.analyze_orbital_changes(all_results)
    
    # 任务负载变化分析
    self.analyze_task_load_changes(all_results)
    
    # 系统性能变化分析
    self.analyze_performance_changes(all_results)
    
    # 资源利用变化分析
    self.analyze_resource_utilization_changes(all_results)
输出报告格式要求
1. 时隙级别报告
================================================================================
时隙 X 系统状态全面测试报告
================================================================================
测试时间: 2025-01-21 10:30:45
仿真时间: XXX秒
物理时间: 2025-06-08 04:XX:XX

--- 轨道系统状态 ---
卫星总数: 72
光照卫星数: XX (XX.X%)
可见性连接总数: XXXX
平均邻居数: X.XX
覆盖地面站数: XXX/420 (XX.X%)
云连接总数: XXX

--- 通信系统状态 ---
活跃链路总数: XXXX
  - 卫星间链路: XXX
  - 卫星-地面链路: XXX  
  - 卫星-云链路: XXX
平均数据速率: XX.X Mbps
平均传输延迟: XX.X ms
通信能耗总计: XXX.X J

--- 任务系统状态 ---
新生成任务: XXX个
任务队列总长: XXX
正在处理任务: XXX
已完成任务: XXX
任务成功率: XX.X%

--- 资源系统状态 ---
平均CPU利用率: XX.X%
平均内存使用率: XX.X%
平均电池电量: XX.X%
能量消耗速率: XX.X W

--- 性能系统状态 ---
缓存命中率: XX.X%
内存使用: XXX MB
计算耗时: XX.X ms
系统负载: XX.X%

--- 异常和警告 ---
[列出所有检测到的异常和警告]

--- 关键指标对比 ---
[与前一时隙的关键指标对比]
2. 全局分析报告
================================================================================
SPACE2系统全面测试分析报告
================================================================================
测试时隙范围: 1-5, 100-105, 1000-1005
测试执行时间: XXX分钟
数据点总数: XXXXX

--- 系统稳定性分析 ---
- 轨道计算稳定性: ✅/⚠️/❌
- 通信链路稳定性: ✅/⚠️/❌
- 任务处理稳定性: ✅/⚠️/❌
- 资源管理稳定性: ✅/⚠️/❌

--- 性能趋势分析 ---
- 系统负载趋势: 上升/稳定/下降
- 资源利用效率: XX.X% (平均)
- 任务处理效率: XX.X% (平均)
- 通信效率: XX.X% (平均)

--- 发现的问题 ---
[按优先级列出发现的所有问题]

--- 优化建议 ---
[基于测试结果提出的优化建议]
执行指令
请基于以上要求，编写完整的SPACE2系统测试代码，确保：

完整性: 覆盖所有核心模块的测试
准确性: 输出真实的系统状态数据
可读性: 测试结果格式清晰，易于理解
可验证性: 包含数据验证和异常检测
可比较性: 支持时隙间的对比分析
测试代码应该能够独立运行，并生成详细的测试报告，帮助验证SPACE2系统各模块的正确性和性能表现。