"""
测试任务生成的随机性
验证取消固定种子后每次运行产生不同的任务数
"""

import numpy as np
import yaml
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.env.physics_layer.task_generator import TaskGenerator

def test_random_task_generation():
    """测试任务生成的随机性"""
    
    print("=" * 80)
    print("测试任务生成随机性（取消固定种子）")
    print("=" * 80)
    
    # 加载配置文件查看当前参数
    config_file = "src/env/physics_layer/config.yaml"
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("\n当前λ参数配置:")
    print(f"  ocean_lambda: {config['task_generation']['ocean_lambda']}")
    print(f"  land_large_lambda: {config['task_generation']['land_large_lambda']}")
    print(f"  land_medium_lambda: {config['task_generation']['land_medium_lambda']}")
    print(f"  land_small_lambda: {config['task_generation']['land_small_lambda']}")
    
    # 测试1：不指定种子（完全随机）
    print("\n\n测试1: 不指定种子（每个时隙都随机）")
    print("-" * 50)
    
    # 创建生成器，不指定种子
    generator1 = TaskGenerator(seed=None)
    generator1.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    print("第一次运行（5个时隙）:")
    run1_results = []
    for timeslot in range(5):
        tasks = generator1.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        run1_results.append(total)
        print(f"  时隙{timeslot}: 生成了 {total} 个任务")
    
    # 测试2：创建新的生成器实例，再次运行
    print("\n第二次运行（新实例，5个时隙）:")
    generator2 = TaskGenerator(seed=None)
    generator2.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    run2_results = []
    for timeslot in range(5):
        tasks = generator2.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        run2_results.append(total)
        print(f"  时隙{timeslot}: 生成了 {total} 个任务")
    
    # 测试3：使用同一个生成器继续生成更多时隙
    print("\n继续使用第二个生成器（时隙5-9）:")
    for timeslot in range(5, 10):
        tasks = generator2.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        print(f"  时隙{timeslot}: 生成了 {total} 个任务")
    
    # 比较结果
    print("\n\n结果分析:")
    print("-" * 50)
    
    # 检查两次运行是否产生了不同的结果
    is_different = any(r1 != r2 for r1, r2 in zip(run1_results, run2_results))
    
    if is_different:
        print("✓ 成功：两次运行产生了不同的任务数，说明随机性生效")
        print(f"  第一次运行前5个时隙: {run1_results}")
        print(f"  第二次运行前5个时隙: {run2_results}")
    else:
        print("✗ 警告：两次运行产生了相同的任务数")
        print("  可能原因：")
        print("  1. 仍有某处使用了固定种子")
        print("  2. 极小概率的巧合")
    
    # 统计分析
    print("\n统计信息:")
    print(f"  第一次运行平均任务数: {np.mean(run1_results):.2f}")
    print(f"  第二次运行平均任务数: {np.mean(run2_results):.2f}")
    print(f"  第一次运行标准差: {np.std(run1_results):.2f}")
    print(f"  第二次运行标准差: {np.std(run2_results):.2f}")
    
    # 测试4：对比固定种子的情况
    print("\n\n测试2: 对比使用固定种子的情况")
    print("-" * 50)
    
    print("使用固定种子seed=999（两次运行）:")
    
    generator3 = TaskGenerator(seed=999)
    generator3.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    fixed_run1 = []
    print("  第一次（seed=999）:")
    for timeslot in range(3):
        tasks = generator3.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        fixed_run1.append(total)
        print(f"    时隙{timeslot}: {total} 个任务")
    
    generator4 = TaskGenerator(seed=999)
    generator4.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    fixed_run2 = []
    print("  第二次（seed=999）:")
    for timeslot in range(3):
        tasks = generator4.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        fixed_run2.append(total)
        print(f"    时隙{timeslot}: {total} 个任务")
    
    if fixed_run1 == fixed_run2:
        print("  ✓ 固定种子产生相同结果（预期行为）")
    else:
        print("  ✗ 固定种子产生了不同结果（异常）")
    
    # 总结
    print("\n" + "=" * 80)
    print("总结:")
    print("=" * 80)
    print("\n如何使用:")
    print("1. 完全随机（推荐）:")
    print("   generator = TaskGenerator(seed=None)")
    print("   或")
    print("   generator = TaskGenerator()  # 不传seed参数")
    print("\n2. 可重现的结果（用于调试）:")
    print("   generator = TaskGenerator(seed=42)  # 使用固定种子")
    print("\n现在每个时隙的任务生成都是真正随机的！")

if __name__ == "__main__":
    test_random_task_generation()