

### **科研仿真平台功能开发规划模板 (通用版 for SPACE2)**

请在每次开发新功能或大模块时，复制并填写此模板。

-----

#### **模块/功能编号:** [例如：F01, M03]

#### **模块/功能名称:** [例如：星间链路通信模型, 动态任务生成器]

### **1. 目标与范围定义 (Objective & Scope Definition)**

  * **核心目标 / 待解决的科研问题 (Core Objective / Research Problem to Solve):**

    > *（清晰描述此功能要实现什么，或它解决了哪个具体的科研挑战。）*

  * **功能概述 (Feature Overview):**

    > *（用通俗的语言描述这个功能是什么，以及它在整个仿真中扮演的角色。）*

  * **关键输入 (Key Inputs):**

    > *（列出此功能需要从仿真环境主循环或其他模块获取的数据。）*

  * **关键输出 (Key Outputs):**

    > *（列出此功能将产生什么结果，或修改了哪些全局状态）*

  * **边界与约束 (Boundaries & Constraints):**

    > *（明确此功能的范围，什么做，什么不做，以及基于哪些假设。）*

### **2. 技术与交互设计 (Technical & Interaction Design)**

  * **设计思路 / 算法原理 (Design Rationale / Algorithm Principle):**

    > *（描述实现该功能的核心技术、公式或伪代码。这是设计的灵魂

  * **模块交互设计 (Module Interaction Design):**

    > *（描述此功能如何与`SPACE2`环境中的其他部分交互，这是保证系统集成性的关键。）*

      * **影响的现有模块 (Affected Existing Modules):** [例如：`CommunicationModel`, `SimulationKernel`]
      * **新增的模块/类 (New Modules/Classes):** [例如：`AtmosphericModel.py`, `RainAttenuationCalculator`类]
      * **数据交互接口 (Data Interaction Interface):**
        > *（描述具体的函数调用关系。例如：“`SimulationKernel`的主循环将在每个时隙开始时调用`AtmosphericModel.update(t)`。原有的`CommunicationModel`在计算传输速率时，将从`AtmosphericModel`获取指定链路的衰减值。”）*

  * **数据结构与状态变量 (Data Structures & State Variables):**

    > *（详细定义为实现此功能需要新增或修改的数据结构。例如：“在`SatelliteLink`类中增加一个浮点型属性 `current_attenuation_db`。” 或 “新增一个全局字典 `weather_map` 用于存储地理位置到降雨率的映射。”）*

### **3. 验证与评估方案 (Validation & Evaluation Plan)**

  * **正确性验证方案 (Correctness Validation Plan):**

    > *（这是科研代码的生命线，必须严格。取代传统软件测试。）*

      * **单元验证 (Unit Validation):**
        > *（对新增的核心函数/类进行孤立测试。例如：“创建一个测试脚本，输入固定的卫星/用户坐标和降雨率，验证`calculate_itu_attenuation`的输出是否与手动计算或官方示例一致。”）*
      * **集成验证 (Integration Validation):**
        > *（验证功能在整个仿真流程中是否按预期工作。例如：“运行一个10个时隙的短时仿真，打印出某一条特定链路的带宽变化。观察其是否随着卫星飞过高降雨区域而按预期下降。”）*

  * **效果评估方案 (Impact Evaluation Plan):**

    > *（评估该功能对最终科研目标的影响。）*

      * **对比实验设计 (Comparative Experiment Design):**
        > *（描述如何衡量该功能带来的变化。例如：“设置两组实验：A组（无天气模型）和B组（有天气模型）。使用相同的基线强化学习卸载算法，在两种环境下分别运行，比较算法的平均任务完成率、时延和能耗。预期B组的性能指标会有所下降，但更贴近真实情况。”）*
      * **期望结果与分析 (Expected Outcome & Analysis):**
        > *（预测实验结果，并说明这能证明什么。例如：“预期结果将表明，不考虑动态链路变化的强化学习策略在真实环境下性能会严重下降，从而凸显研究动态自适应策略的必要性。”）*

-----


目的：创建详尽的技术规范

核心思维应用：

 *  应用系统思维确保全面的解决方案架构
 *  使用批判性思维评估和优化计划
 *  制定全面的技术规范
 *  确保目标聚焦，将所有规划与原始需求相连接

允许：

 *  带有精确文件路径的详细计划
 *  精确的函数名称和签名
 *  具体的更改规范
 *  完整的架构概述

禁止：

 *  任何实施或代码编写
 *  甚至可能被实施的"示例代码"
 *  跳过或缩略规范