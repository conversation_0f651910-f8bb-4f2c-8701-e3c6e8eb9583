# SPACE3 环境接口指南

## 一、粒子群算法(PSO)接口文档

### 1. 系统架构概览

```
src/
├── env/                      # 环境层（物理仿真）
│   ├── Foundation_Layer/     # 基础设施
│   ├── physics_layer/        # 物理仿真
│   └── satellite_cloud/      # 卫星计算资源
└── agent/                    # 算法层（决策逻辑）
    ├── local/               # 本地计算基线
    └── pso/                 # PSO算法（待实现）
```

### 2. PSO算法需要调用的核心接口

#### 2.1 环境初始化接口

```python
from src.env.Foundation_Layer.time_manager import TimeManager, create_time_manager_from_config
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_distributor import TaskDistributor
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.satellite_cloud.satellite_compute import SatelliteCompute

class PSOOptimizer:
    def __init__(self, config_path: str):
        # 1. 加载配置
        self.config = yaml.safe_load(open(config_path))
        
        # 2. 初始化时间管理器
        self.time_manager = create_time_manager_from_config(self.config)
        
        # 3. 初始化轨道模块
        self.orbital_updater = OrbitalUpdater(config_file=config_path)
        
        # 4. 初始化通信管理器
        self.comm_manager = CommunicationManager(
            orbital_updater=self.orbital_updater,
            config=self.config
        )
        
        # 5. 初始化任务生成器
        self.task_generator = TaskGenerator(config_file=config_path)
        self.task_generator.load_locations_from_csv('src/env/env_data/global_ground_stations.csv')
        
        # 6. 初始化任务分发器
        self.task_distributor = TaskDistributor(
            orbital_updater=self.orbital_updater,
            config_file=config_path,
            time_manager=self.time_manager
        )
        
        # 7. 初始化卫星计算节点
        self.satellites = {}
        satellites_at_t0 = self.orbital_updater.get_satellites_at_time(0)
        for sat_id_str in satellites_at_t0.keys():
            sat_id = int(sat_id_str)
            self.satellites[sat_id] = SatelliteCompute(
                satellite_id=sat_id,
                config=self.config,
                orbital_updater=self.orbital_updater,
                comm_manager=self.comm_manager
            )
```

#### 2.2 时隙处理接口

```python
def process_timeslot(self, timeslot: int, pso_params: Dict):
    """PSO算法处理单个时隙"""
    
    # 1. 获取时间上下文
    time_context = self.time_manager.get_time_context(timeslot)
    current_time = time_context.simulation_time
    
    # 2. 生成任务
    tasks_by_location = self.task_generator.generate_tasks_for_timeslot(timeslot)
    
    # 3. 分发任务到可见卫星
    all_tasks = [task for tasks in tasks_by_location.values() for task in tasks]
    assignments = self.task_distributor.distribute_tasks(all_tasks, timeslot)
    
    # 4. PSO优化：决定任务卸载策略
    offload_decisions = self.pso_optimize_offloading(
        assignments, 
        timeslot,
        pso_params
    )
    
    # 5. 执行卸载决策
    for decision in offload_decisions:
        if decision['action'] == 'local':
            # 本地处理
            satellite = self.satellites[decision['satellite_id']]
            satellite.set_cpu_allocation_strategy('custom', decision['cpu_allocation'])
            satellite.add_task(decision['task'])
        elif decision['action'] == 'offload':
            # 卸载到其他卫星
            self.execute_offloading(decision)
    
    # 6. 处理任务
    for sat_id, satellite in self.satellites.items():
        # 获取卫星光照状态
        illuminated = self.check_satellite_illumination(sat_id, timeslot)
        
        # 更新时间
        satellite.current_time = current_time
        
        # 处理时隙
        result = satellite.process_timeslot(
            duration=self.config['system']['timeslot_duration_s'],
            illuminated=illuminated
        )
```

#### 2.3 PSO核心优化接口

```python
def pso_optimize_offloading(self, assignments: List, timeslot: int, pso_params: Dict):
    """PSO优化任务卸载决策
    
    Args:
        assignments: 任务分配列表
        timeslot: 当前时隙
        pso_params: PSO参数（粒子数、迭代次数、惯性权重等）
    
    Returns:
        卸载决策列表
    """
    # 1. 获取系统状态
    system_state = self.get_system_state(timeslot)
    
    # 2. 初始化粒子群
    particles = self.initialize_particles(
        num_particles=pso_params['num_particles'],
        assignments=assignments,
        system_state=system_state
    )
    
    # 3. PSO迭代优化
    for iteration in range(pso_params['max_iterations']):
        for particle in particles:
            # 评估适应度（考虑延迟、能量、完成率）
            fitness = self.evaluate_fitness(particle, system_state)
            
            # 更新个体最优和全局最优
            particle.update_best(fitness)
            self.update_global_best(particle)
            
            # 更新粒子速度和位置
            particle.update_velocity(
                w=pso_params['inertia_weight'],
                c1=pso_params['cognitive_factor'],
                c2=pso_params['social_factor']
            )
            particle.update_position()
    
    # 4. 返回最优决策
    return self.decode_solution(self.global_best_position)
```

#### 2.4 获取系统状态接口

```python
def get_system_state(self, timeslot: int) -> Dict:
    """获取系统完整状态"""
    
    state = {
        'satellite_states': {},
        'communication_state': {},
        'task_distribution': {}
    }
    
    # 1. 卫星状态
    for sat_id, satellite in self.satellites.items():
        state['satellite_states'][sat_id] = {
            'queue_status': satellite.get_queue_status(),
            'energy_status': satellite.get_energy_status(),
            'statistics': satellite.get_statistics()
        }
    
    # 2. 通信状态
    state['communication_state'] = {
        'inter_satellite': self.comm_manager.get_inter_satellite_links(timeslot),
        'satellite_ground': self.comm_manager.get_satellite_ground_links(timeslot),
        'bandwidth_usage': self.comm_manager.get_bandwidth_usage()
    }
    
    # 3. 可见性矩阵
    visibility = self.orbital_updater.get_visibility_at_time(timeslot)
    state['visibility'] = {
        'inter_satellite': visibility['inter_satellite'],
        'satellite_ground': visibility['satellite_ground']
    }
    
    return state
```

#### 2.5 任务卸载执行接口

```python
def execute_offloading(self, decision: Dict):
    """执行任务卸载"""
    
    source_sat_id = decision['source_satellite']
    target_sat_id = decision['target_satellite']
    task = decision['task']
    
    # 1. 计算通信延迟
    comm_delay = self.comm_manager.calculate_transmission_delay(
        source_id=source_sat_id,
        target_id=target_sat_id,
        data_size_mb=task.data_size_mb,
        timeslot=decision['timeslot']
    )
    
    # 2. 更新任务到达时间
    task.arrival_time += comm_delay
    
    # 3. 传输能量消耗
    transmission_energy = self.comm_manager.calculate_transmission_energy(
        source_id=source_sat_id,
        target_id=target_sat_id,
        data_size_mb=task.data_size_mb
    )
    
    # 4. 更新源卫星能量
    self.satellites[source_sat_id].battery_energy -= transmission_energy
    
    # 5. 添加任务到目标卫星
    success = self.satellites[target_sat_id].add_task(task)
    
    return success
```

### 3. PSO算法实现示例

```python
# src/agent/pso/pso_compute_optimizer.py

class PSOComputeOptimizer:
    """基于PSO的卫星边缘计算优化器"""
    
    def __init__(self, config_path: str):
        # 继承环境接口
        self.env = SpaceEnvironment(config_path)
        
        # PSO参数
        self.num_particles = 50
        self.max_iterations = 100
        self.w = 0.7  # 惯性权重
        self.c1 = 1.5  # 认知因子
        self.c2 = 1.5  # 社会因子
    
    def optimize_resource_allocation(self, timeslot: int):
        """优化资源分配"""
        
        # 1. 获取当前任务和系统状态
        tasks = self.env.get_current_tasks(timeslot)
        state = self.env.get_system_state(timeslot)
        
        # 2. 定义决策变量
        # - 任务分配矩阵 X[i,j]: 任务i是否分配给卫星j
        # - CPU分配向量 C[i]: 任务i的CPU分配比例
        # - 卸载决策矩阵 O[i,j]: 任务i是否从卫星j卸载
        
        # 3. 运行PSO优化
        best_solution = self.run_pso(tasks, state)
        
        # 4. 应用最优解
        self.apply_solution(best_solution, timeslot)
        
        return best_solution
    
    def fitness_function(self, solution: np.ndarray, tasks: List, state: Dict):
        """适应度函数：最小化延迟和能量，最大化完成率"""
        
        # 解码解决方案
        allocation = self.decode_allocation(solution)
        
        # 计算目标
        total_delay = self.calculate_total_delay(allocation, tasks, state)
        total_energy = self.calculate_total_energy(allocation, tasks, state)
        completion_rate = self.calculate_completion_rate(allocation, tasks, state)
        
        # 多目标加权
        fitness = (
            -0.4 * total_delay +          # 最小化延迟
            -0.3 * total_energy +          # 最小化能量
            0.3 * completion_rate          # 最大化完成率
        )
        
        return fitness
```

## 二、PettingZoo环境接口文档

### 1. PettingZoo环境结构

```python
# src/env/pettingzoo_env/space_env.py

from pettingzoo import AECEnv
from pettingzoo.utils import agent_selector
import gymnasium.spaces as spaces
import numpy as np

class SpaceMultiAgentEnv(AECEnv):
    """SPACE3多智能体环境"""
    
    metadata = {
        "render_modes": ["human", "rgb_array"],
        "name": "space_v0",
        "is_parallelizable": False,
        "render_fps": 2,
    }
    
    def __init__(self, config_path: str = None):
        super().__init__()
        
        # 加载配置
        self.config = yaml.safe_load(open(config_path))
        
        # 初始化环境组件
        self._init_environment_components()
        
        # 初始化智能体
        self._init_agents()
        
        # 定义动作和观察空间
        self._init_spaces()
```

### 2. 环境组件初始化

```python
def _init_environment_components(self):
    """初始化环境组件"""
    
    # 1. 时间管理器
    self.time_manager = create_time_manager_from_config(self.config)
    
    # 2. 轨道模块
    self.orbital_updater = OrbitalUpdater(config_file=self.config_path)
    
    # 3. 通信管理器
    self.comm_manager = CommunicationManager(
        orbital_updater=self.orbital_updater,
        config=self.config
    )
    
    # 4. 任务生成器
    self.task_generator = TaskGenerator(config_file=self.config_path)
    self.task_generator.load_locations_from_csv(
        'src/env/env_data/global_ground_stations.csv'
    )
    
    # 5. 任务分发器
    self.task_distributor = TaskDistributor(
        orbital_updater=self.orbital_updater,
        config_file=self.config_path,
        time_manager=self.time_manager
    )
    
    # 6. 卫星计算节点（作为智能体）
    self.satellites = {}
    satellites_at_t0 = self.orbital_updater.get_satellites_at_time(0)
    for sat_id_str in satellites_at_t0.keys():
        sat_id = int(sat_id_str)
        self.satellites[sat_id] = SatelliteCompute(
            satellite_id=sat_id,
            config=self.config,
            orbital_updater=self.orbital_updater,
            comm_manager=self.comm_manager
        )
```

### 3. 智能体定义

```python
def _init_agents(self):
    """初始化智能体"""
    
    # 每个卫星是一个智能体
    self.agents = [f"satellite_{sat_id}" for sat_id in self.satellites.keys()]
    self.possible_agents = self.agents[:]
    
    # 智能体选择器
    self._agent_selector = agent_selector(self.agents)
    self.agent_selection = self._agent_selector.next()
    
    # 智能体映射
    self.agent_to_satellite = {
        f"satellite_{sat_id}": sat_id 
        for sat_id in self.satellites.keys()
    }
```

### 4. 动作和观察空间定义

```python
def _init_spaces(self):
    """定义动作和观察空间"""
    
    # 观察空间：每个智能体的局部观察
    self.observation_spaces = {}
    for agent in self.agents:
        self.observation_spaces[agent] = spaces.Dict({
            # 自身状态
            'battery_level': spaces.Box(0, 1, shape=(1,), dtype=np.float32),
            'queue_length': spaces.Box(0, 100, shape=(1,), dtype=np.int32),
            'cpu_utilization': spaces.Box(0, 1, shape=(1,), dtype=np.float32),
            
            # 任务信息（队列中前10个任务）
            'task_priorities': spaces.Box(0, 10, shape=(10,), dtype=np.float32),
            'task_complexities': spaces.Box(0, 1e10, shape=(10,), dtype=np.float32),
            'task_deadlines': spaces.Box(0, 10000, shape=(10,), dtype=np.float32),
            
            # 邻居信息（最多10个邻居）
            'neighbor_queue_lengths': spaces.Box(0, 100, shape=(10,), dtype=np.int32),
            'neighbor_cpu_utils': spaces.Box(0, 1, shape=(10,), dtype=np.float32),
            'neighbor_distances': spaces.Box(0, 10000, shape=(10,), dtype=np.float32),
            
            # 通信状态
            'available_bandwidth': spaces.Box(0, 1e9, shape=(10,), dtype=np.float32),
            
            # 光照状态
            'illuminated': spaces.Discrete(2)
        })
    
    # 动作空间：每个智能体的决策
    self.action_spaces = {}
    for agent in self.agents:
        self.action_spaces[agent] = spaces.Dict({
            # 任务处理决策（对每个任务）
            'task_decisions': spaces.MultiDiscrete([3] * 10),  # 0:丢弃, 1:本地, 2:卸载
            
            # CPU分配（对本地处理的任务）
            'cpu_allocations': spaces.Box(0, 1, shape=(10,), dtype=np.float32),
            
            # 卸载目标（对卸载的任务）
            'offload_targets': spaces.MultiDiscrete([10] * 10),  # 目标卫星索引
        })
```

### 5. 核心环境方法

```python
def reset(self, seed=None, options=None):
    """重置环境"""
    
    # 重置时间
    self.current_timeslot = 0
    
    # 重置所有卫星
    for satellite in self.satellites.values():
        satellite.reset()
    
    # 重置智能体
    self.agents = self.possible_agents[:]
    self._agent_selector = agent_selector(self.agents)
    self.agent_selection = self._agent_selector.next()
    
    # 初始化奖励和完成状态
    self.rewards = {agent: 0 for agent in self.agents}
    self.terminations = {agent: False for agent in self.agents}
    self.truncations = {agent: False for agent in self.agents}
    self.infos = {agent: {} for agent in self.agents}
    
    # 生成初始任务
    self._generate_and_distribute_tasks()
    
    # 获取初始观察
    observations = {}
    for agent in self.agents:
        observations[agent] = self._get_observation(agent)
    
    return observations, self.infos

def step(self, action):
    """执行一步环境交互"""
    
    # 当前智能体
    agent = self.agent_selection
    sat_id = self.agent_to_satellite[agent]
    
    # 执行动作
    if action is not None:
        self._apply_action(agent, action)
    
    # 如果所有智能体都行动完毕，推进时隙
    if self._agent_selector.is_last():
        self._process_timeslot()
        self._calculate_rewards()
        self.current_timeslot += 1
        
        # 检查终止条件
        if self.current_timeslot >= self.config['system']['total_timeslots']:
            for agent in self.agents:
                self.terminations[agent] = True
    
    # 选择下一个智能体
    self.agent_selection = self._agent_selector.next()
    
    # 获取观察
    observations = {}
    for agent in self.agents:
        observations[agent] = self._get_observation(agent)
    
    return observations, self.rewards, self.terminations, self.truncations, self.infos
```

### 6. 观察获取接口

```python
def _get_observation(self, agent: str) -> Dict:
    """获取智能体观察"""
    
    sat_id = self.agent_to_satellite[agent]
    satellite = self.satellites[sat_id]
    
    # 获取卫星状态
    queue_status = satellite.get_queue_status()
    energy_status = satellite.get_energy_status()
    
    # 获取任务信息
    task_queue = satellite.task_queue[:10]  # 前10个任务
    task_priorities = [t.priority for t in task_queue]
    task_complexities = [t.complexity for t in task_queue]
    task_deadlines = [t.deadline for t in task_queue]
    
    # 填充到固定长度
    while len(task_priorities) < 10:
        task_priorities.append(0)
        task_complexities.append(0)
        task_deadlines.append(0)
    
    # 获取邻居信息
    neighbors = self._get_visible_neighbors(sat_id)
    neighbor_queue_lengths = []
    neighbor_cpu_utils = []
    neighbor_distances = []
    
    for neighbor_id in neighbors[:10]:
        neighbor = self.satellites[neighbor_id]
        neighbor_status = neighbor.get_queue_status()
        neighbor_queue_lengths.append(neighbor_status['queue_length'])
        neighbor_cpu_utils.append(neighbor_status['cpu_utilization'])
        
        # 计算距离
        distance = self.orbital_updater.calculate_distance(
            sat_id, neighbor_id, self.current_timeslot
        )
        neighbor_distances.append(distance)
    
    # 填充到固定长度
    while len(neighbor_queue_lengths) < 10:
        neighbor_queue_lengths.append(0)
        neighbor_cpu_utils.append(0)
        neighbor_distances.append(0)
    
    # 获取通信带宽
    available_bandwidth = self.comm_manager.get_available_bandwidth(
        sat_id, neighbors[:10], self.current_timeslot
    )
    while len(available_bandwidth) < 10:
        available_bandwidth.append(0)
    
    # 获取光照状态
    illuminated = self._check_satellite_illumination(sat_id, self.current_timeslot)
    
    observation = {
        'battery_level': np.array([energy_status['energy_percentage'] / 100], dtype=np.float32),
        'queue_length': np.array([queue_status['queue_length']], dtype=np.int32),
        'cpu_utilization': np.array([queue_status['cpu_utilization']], dtype=np.float32),
        'task_priorities': np.array(task_priorities, dtype=np.float32),
        'task_complexities': np.array(task_complexities, dtype=np.float32),
        'task_deadlines': np.array(task_deadlines, dtype=np.float32),
        'neighbor_queue_lengths': np.array(neighbor_queue_lengths, dtype=np.int32),
        'neighbor_cpu_utils': np.array(neighbor_cpu_utils, dtype=np.float32),
        'neighbor_distances': np.array(neighbor_distances, dtype=np.float32),
        'available_bandwidth': np.array(available_bandwidth, dtype=np.float32),
        'illuminated': int(illuminated)
    }
    
    return observation
```

### 7. 动作执行接口

```python
def _apply_action(self, agent: str, action: Dict):
    """应用智能体动作"""
    
    sat_id = self.agent_to_satellite[agent]
    satellite = self.satellites[sat_id]
    
    task_decisions = action['task_decisions']
    cpu_allocations = action['cpu_allocations']
    offload_targets = action['offload_targets']
    
    # 处理队列中的任务
    tasks = satellite.task_queue[:10]
    
    for i, task in enumerate(tasks):
        decision = task_decisions[i]
        
        if decision == 0:  # 丢弃
            satellite._drop_task(task, "agent_decision")
            
        elif decision == 1:  # 本地处理
            # 设置CPU分配
            cpu_alloc = cpu_allocations[i]
            satellite.set_cpu_allocation_strategy('custom', {
                task.task_id: cpu_alloc
            })
            
        elif decision == 2:  # 卸载
            # 获取目标卫星
            neighbors = self._get_visible_neighbors(sat_id)
            if offload_targets[i] < len(neighbors):
                target_sat_id = neighbors[offload_targets[i]]
                self._offload_task(task, sat_id, target_sat_id)
```

### 8. 奖励计算接口

```python
def _calculate_rewards(self):
    """计算所有智能体的奖励"""
    
    for agent in self.agents:
        sat_id = self.agent_to_satellite[agent]
        satellite = self.satellites[sat_id]
        
        # 获取本时隙的性能指标
        stats = satellite.get_statistics()
        
        # 奖励组成
        # 1. 任务完成奖励
        completion_reward = stats['total_tasks_processed'] * 10
        
        # 2. 延迟惩罚
        avg_delay = self._calculate_average_delay(satellite.completed_tasks)
        delay_penalty = -avg_delay * 0.1
        
        # 3. 能量效率奖励
        if stats['total_energy_consumed'] > 0:
            efficiency = stats['total_tasks_processed'] / stats['total_energy_consumed']
            energy_reward = efficiency * 100
        else:
            energy_reward = 0
        
        # 4. 队列长度惩罚
        queue_penalty = -stats['queue_length'] * 0.5
        
        # 5. 协作奖励（成功卸载）
        collab_reward = self._calculate_collaboration_reward(sat_id)
        
        # 总奖励
        total_reward = (
            completion_reward + 
            delay_penalty + 
            energy_reward + 
            queue_penalty + 
            collab_reward
        )
        
        self.rewards[agent] = total_reward
```

### 9. 使用示例

```python
# 训练MAPPO算法
from src.env.pettingzoo_env.space_env import SpaceMultiAgentEnv
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv

# 创建环境
env = SpaceMultiAgentEnv(config_path='src/env/physics_layer/config.yaml')

# 包装为向量化环境
vec_env = DummyVecEnv([lambda: env])

# 创建MAPPO模型
model = PPO(
    "MultiInputPolicy",
    vec_env,
    verbose=1,
    n_steps=2048,
    batch_size=64,
    n_epochs=10,
    learning_rate=3e-4
)

# 训练
model.learn(total_timesteps=1000000)

# 评估
obs = env.reset()
for _ in range(1000):
    actions = {}
    for agent in env.agents:
        action, _ = model.predict(obs[agent])
        actions[agent] = action
    
    obs, rewards, dones, truncs, infos = env.step(actions)
    
    if all(dones.values()):
        break
```

## 三、关键接口总结

### 环境层提供的接口

1. **时间管理**: `TimeManager.get_time_context(timeslot)`
2. **轨道计算**: `OrbitalUpdater.get_satellites_at_time(timeslot)`
3. **可见性**: `OrbitalUpdater.get_visibility_at_time(timeslot)`
4. **任务生成**: `TaskGenerator.generate_tasks_for_timeslot(timeslot)`
5. **任务分发**: `TaskDistributor.distribute_tasks(tasks, timeslot)`
6. **通信计算**: `CommunicationManager.calculate_transmission_delay()`
7. **卫星计算**: `SatelliteCompute.process_timeslot(duration, illuminated)`
8. **资源状态**: `SatelliteCompute.get_queue_status()`, `get_energy_status()`

### 算法层需要实现的接口

1. **资源分配策略**: `set_cpu_allocation_strategy(strategy, params)`
2. **任务调度决策**: `schedule_tasks(tasks, system_state)`
3. **卸载决策**: `decide_offloading(task, neighbors)`
4. **适应度评估**: `evaluate_fitness(solution, constraints)`

### PettingZoo环境必须实现的接口

1. **reset()**: 环境重置
2. **step(action)**: 执行动作
3. **render()**: 渲染（可选）
4. **close()**: 关闭环境
5. **observation_space**: 观察空间定义
6. **action_space**: 动作空间定义

## 四、注意事项

1. **时间同步**: 所有组件必须使用统一的时间管理器
2. **数据一致性**: 使用真实数据文件，不要编造数据
3. **能量约束**: 始终检查卫星能量状态
4. **通信延迟**: 考虑实际的通信延迟和带宽限制
5. **并行处理**: 卫星支持多任务并行，需合理分配CPU资源
6. **任务原子性**: 任务不可分割，必须完整处理

## 五、性能优化建议

1. **向量化计算**: 使用NumPy批量处理
2. **缓存机制**: 缓存轨道数据和可见性矩阵
3. **并行仿真**: 多个卫星可并行处理
4. **稀疏矩阵**: 大规模网络使用稀疏表示
5. **增量更新**: 只更新变化的状态