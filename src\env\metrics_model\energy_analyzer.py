"""
能耗分析接口模块
提供能耗相关指标的计算和分析
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np
import logging
import yaml

from src.env.satellite_cloud.satellite import Satellite, SatelliteTask
from src.env.physics_layer.task_tracking import TaskTrackingRecord, ProcessingNode


@dataclass
class EnergyMetrics:
    """能耗指标数据类"""
    total_energy: float  # 总能耗 (J)
    computation_energy: float  # 计算能耗 (J)
    communication_energy: float  # 通信能耗 (J)
    idle_energy: float  # 待机能耗 (J)
    avg_energy_per_task: float  # 单位任务能耗 (J/task)
    avg_energy_per_mb: float  # 单位数据能耗 (J/MB)
    energy_efficiency: float  # 能效比


@dataclass
class EnergyDistribution:
    """能耗分布指标"""
    mean: float
    std: float
    min: float
    max: float
    balance_factor: float  # 能耗均衡度
    gini_coefficient: float  # 基尼系数
    peak_to_avg_ratio: float  # 峰值能耗比


class EnergyAnalyzer:
    """能耗分析器"""
    
    def __init__(self, config_path: str):
        """
        初始化能耗分析器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 从配置中提取能耗参数
        self.zeta_leo = float(self.config['computation']['zeta_leo'])
        # 使用卫星对用户的发射功率
        self.tx_power_w = self.config['communication']['p_su_w']
        self.idle_power_w = self.config['computation']['idle_power_w']
        
        self.logger = logging.getLogger(__name__)
    
    def analyze_satellites(self, satellites: List[Satellite],
                         time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        分析卫星的能耗指标
        
        Args:
            satellites: 卫星实例列表
            time_window: 时间窗口(开始时间, 结束时间)
            
        Returns:
            能耗分析结果字典
        """
        if not satellites:
            return self._empty_metrics()
        
        # 收集每个卫星的能耗数据
        satellite_energies = []
        total_computation_energy = 0.0
        total_communication_energy = 0.0
        total_tasks_processed = 0
        total_data_processed = 0.0
        
        for satellite in satellites:
            # 直接从卫星获取已计算的能耗
            sat_energy = satellite.total_energy_consumed
            satellite_energies.append(sat_energy)
            
            # 统计任务信息
            completed_tasks = satellite.completed_tasks
            if time_window:
                start_time, end_time = time_window
                completed_tasks = [
                    t for t in completed_tasks
                    if start_time <= t.arrival_time <= end_time
                ]
            
            total_tasks_processed += len(completed_tasks)
            
            # 计算详细能耗（从任务中获取）
            for task in completed_tasks:
                # 计算能耗已在satellite.py中完成，直接使用
                if hasattr(task, 'energy_consumed'):
                    total_computation_energy += task.energy_consumed
                
                # 数据量统计
                if hasattr(task, 'data_size'):
                    total_data_processed += task.data_size
                
                # 通信能耗（如果有记录）
                if hasattr(task, 'communication_energy'):
                    total_communication_energy += task.communication_energy
        
        # 计算总能耗
        total_energy = sum(satellite_energies)
        
        # 计算能耗分布指标
        distribution = self._calculate_distribution(satellite_energies)
        
        # 计算归一化指标
        normalized_metrics = self._calculate_normalized_metrics(
            total_energy,
            total_tasks_processed,
            total_data_processed
        )
        
        return {
            'energy_metrics': EnergyMetrics(
                total_energy=total_energy,
                computation_energy=total_computation_energy,
                communication_energy=total_communication_energy,
                idle_energy=0.0,  # 待机能耗需要根据实际空闲时间计算
                avg_energy_per_task=normalized_metrics['energy_per_task'],
                avg_energy_per_mb=normalized_metrics['energy_per_mb'],
                energy_efficiency=normalized_metrics['energy_efficiency']
            ),
            'energy_distribution': distribution,
            'satellite_details': self._get_satellite_details(satellites)
        }
    
    def analyze_task_records(self, task_records: List[TaskTrackingRecord],
                           time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        分析任务记录的能耗指标
        
        Args:
            task_records: 任务跟踪记录列表
            time_window: 时间窗口
            
        Returns:
            能耗分析结果
        """
        if not task_records:
            return self._empty_metrics()
        
        # 过滤时间窗口
        if time_window:
            start_time, end_time = time_window
            filtered_records = [
                r for r in task_records
                if start_time <= r.generation_time <= end_time
            ]
        else:
            filtered_records = task_records
        
        # 统计能耗
        total_energy = 0.0
        computation_energy = 0.0
        communication_energy = 0.0
        total_data = 0.0
        
        for record in filtered_records:
            if record.processing_nodes:
                for node in record.processing_nodes:
                    # 使用处理节点中记录的能耗
                    if hasattr(node, 'energy_consumption'):
                        total_energy += node.energy_consumption
                        computation_energy += node.energy_consumption
                    
                    # 通信能耗估算
                    if hasattr(node, 'offload_transmission_time'):
                        comm_energy = self._estimate_communication_energy(
                            node.offload_transmission_time + node.result_transmission_time
                        )
                        communication_energy += comm_energy
                        total_energy += comm_energy
            
            total_data += record.data_size_mb
        
        # 计算归一化指标
        normalized_metrics = self._calculate_normalized_metrics(
            total_energy,
            len(filtered_records),
            total_data
        )
        
        return {
            'energy_metrics': EnergyMetrics(
                total_energy=total_energy,
                computation_energy=computation_energy,
                communication_energy=communication_energy,
                idle_energy=0.0,
                avg_energy_per_task=normalized_metrics['energy_per_task'],
                avg_energy_per_mb=normalized_metrics['energy_per_mb'],
                energy_efficiency=normalized_metrics['energy_efficiency']
            )
        }
    
    def _calculate_distribution(self, energies: List[float]) -> EnergyDistribution:
        """计算能耗分布指标"""
        if not energies:
            return EnergyDistribution(0, 0, 0, 0, 0, 0, 0)
        
        energies_array = np.array(energies)
        mean = np.mean(energies_array)
        std = np.std(energies_array)
        
        # 能耗均衡度
        balance_factor = 1 - (std / mean) if mean > 0 else 0
        
        # 基尼系数
        gini = self._calculate_gini_coefficient(energies_array)
        
        # 峰值能耗比
        peak_ratio = np.max(energies_array) / mean if mean > 0 else 0
        
        return EnergyDistribution(
            mean=mean,
            std=std,
            min=np.min(energies_array),
            max=np.max(energies_array),
            balance_factor=balance_factor,
            gini_coefficient=gini,
            peak_to_avg_ratio=peak_ratio
        )
    
    def _calculate_gini_coefficient(self, values: np.ndarray) -> float:
        """计算基尼系数"""
        if len(values) == 0:
            return 0
        
        sorted_values = np.sort(values)
        n = len(values)
        cumsum = np.cumsum(sorted_values)
        
        return (2 * np.sum((np.arange(1, n + 1)) * sorted_values)) / (n * cumsum[-1]) - (n + 1) / n
    
    def _calculate_normalized_metrics(self, total_energy: float,
                                     total_tasks: int,
                                     total_data: float) -> Dict:
        """计算归一化指标"""
        metrics = {}
        
        # 单位任务能耗
        metrics['energy_per_task'] = total_energy / total_tasks if total_tasks > 0 else 0
        
        # 单位数据能耗
        metrics['energy_per_mb'] = total_energy / total_data if total_data > 0 else 0
        
        # 能效比（处理的数据量/能耗）
        metrics['energy_efficiency'] = total_data / total_energy if total_energy > 0 else 0
        
        return metrics
    
    def _estimate_communication_energy(self, transmission_time: float) -> float:
        """估算通信能耗"""
        return self.tx_power_w * transmission_time
    
    def _get_satellite_details(self, satellites: List[Satellite]) -> List[Dict]:
        """获取卫星详细能耗信息"""
        details = []
        for sat in satellites:
            details.append({
                'satellite_id': sat.satellite_id,
                'total_energy': sat.total_energy_consumed,
                'tasks_processed': sat.total_tasks_processed,
                'tasks_dropped': sat.total_tasks_dropped,
                'cpu_utilization': 1.0 - sat.available_cpu,
                'queue_length': len(sat.task_queue),
                'current_processing': len(sat.current_tasks)
            })
        return details
    
    def _empty_metrics(self) -> Dict:
        """返回空指标"""
        return {
            'energy_metrics': EnergyMetrics(0, 0, 0, 0, 0, 0, 0),
            'energy_distribution': EnergyDistribution(0, 0, 0, 0, 0, 0, 0),
            'satellite_details': []
        }