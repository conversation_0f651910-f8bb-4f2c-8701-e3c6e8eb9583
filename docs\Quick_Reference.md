# SPACE3 快速参考指南

## 环境初始化与使用

### 基本使用
```python
from src.env.space_env import Space3PettingZooEnv

# 1. 创建环境
env = Space3PettingZooEnv()

# 2. 重置环境
observations, infos = env.reset(seed=42)

# 3. 环境循环
done = False
while not done:
    # 生成随机动作（实际使用时应该用强化学习算法）
    actions = {}
    for agent in env.agents:
        actions[agent] = env.action_spaces[agent].sample()
    
    # 执行动作
    observations, rewards, terminations, truncations, infos = env.step(actions)
    
    # 检查终止条件
    done = all(terminations.values())

env.close()
```

### 自定义配置
```python
# 创建自定义配置的环境
env = Space3PettingZooEnv(config_path="path/to/custom_config.yaml")
```

## 关键数据结构

### 观测空间结构
```python
observation = {
    'own_state': np.array([...]),          # (12,) 卫星状态
    'task_queue': np.array([...]),         # (20, 8) 任务队列
    'task_mask': np.array([...]),          # (20,) 真实任务掩码
    'action_mask': np.array([...]),        # (20, 16) 动作可行性
    'neighbor_states': np.array([...]),    # (10, 5) 邻居状态
    'comm_quality': np.array([...]),       # (10,) 通信质量
    'time_info': np.array([...])           # (3,) 时间信息
}
```

### 动作空间
```python
# 每个智能体的动作：20个任务位置，每个位置16种选择
action_sequence = np.array([0, 1, 2, 0, 11, ...])  # 长度20

# 动作含义：
# 0: 本地处理
# 1-10: 转发给邻居卫星 1-10
# 11-15: 卸载到云中心 0-4
```

### 奖励信息
```python
rewards = {
    'sat_111': 15.2,
    'sat_112': -2.1,
    # ...
}

# 奖励组成：任务完成奖励 + 延迟惩罚 + 能耗惩罚 + 协作奖励 + ...
```

## 模块间依赖关系图

```
环境层 (Environment Layer)
├── Space3PettingZooEnv          # 主环境类
├── ObservationBuilder           # 观测构建
├── SequenceActionHandler        # 动作处理  
└── RewardCalculator            # 奖励计算

计算层 (Computing Layer)  
├── SatelliteCompute            # 卫星计算节点
├── CloudCompute               # 云计算中心
└── ComputeModels             # 计算任务模型

物理层 (Physics Layer)
├── OrbitalUpdater             # 轨道动力学
├── CommunicationManager       # 通信管理
├── TaskGenerator             # 任务生成
└── TaskModels               # 任务数据模型

基础层 (Foundation Layer)
├── TimeManager               # 时间管理
├── ErrorHandling            # 异常处理
└── LoggingConfig           # 日志配置
```

## 配置文件结构

### 环境配置 (env_config.yaml)
```yaml
# 观测空间配置
max_queue_size: 20
task_feature_dim: 8
state_feature_dim: 12

# 奖励权重
reward_weights:
  completion: 10.0
  drop: -5.0
  delay: -0.01

# 数据路径
data_paths:
  ground_stations: "src/env/env_data/global_ground_stations.csv"
  base_config: "src/env/physics_layer/config.yaml"
```

### 系统配置 (config.yaml)
```yaml
system:
  num_leo_satellites: 72
  num_ground_stations: 420
  num_cloud_centers: 5
  timeslot_duration_s: 5
  num_timeslots: 1441

satellite_compute:
  cpu_frequency_ghz: 50
  battery_capacity_mj: 3.6
  solar_power_kw: 5
```

## 常用接口调用

### 获取环境状态
```python
# 获取卫星状态
for agent_id in env.agents:
    satellite = env.satellites[agent_id]
    queue_status = satellite.get_queue_status()
    energy_status = satellite.get_energy_status()
    statistics = satellite.get_statistics()
    
    print(f"{agent_id}: Queue={queue_status['queue_length']}, "
          f"Battery={energy_status['battery_level']:.1f}%")
```

### 获取物理状态
```python
# 获取当前时隙的可见性矩阵
visibility = env.current_visibility_matrices
sat_to_sat = visibility['satellite_to_satellite']    # (72, 72)
sat_to_ground = visibility['satellite_to_ground']    # (72, 420)
sat_to_cloud = visibility['satellite_to_cloud']     # (72, 5)

# 获取通信矩阵
comm_matrix = env.current_comm_matrix  # (72, 72)
```

### 动作验证示例
```python
# 检查动作有效性
action_handler = env.action_handler
for agent_id, action_seq in actions.items():
    visibility_info = env._extract_agent_visibility(agent_id, env.current_visibility_matrices)
    
    results = action_handler.process_action_sequence(
        action_seq, env.task_queues[agent_id], agent_id, visibility_info
    )
    
    for result in results:
        if not result['success']:
            print(f"Invalid action for {agent_id}: {result['target'].reason}")
```

## 性能监控

### 环境性能统计
```python
# 获取环境统计信息
print(f"当前时隙: {env.current_timeslot}/{env.max_timeslots}")
print(f"总任务生成: {env.total_tasks_generated}")
print(f"总任务完成: {env.total_tasks_completed}")
print(f"总任务丢弃: {env.total_tasks_dropped}")

# 获取各智能体累积奖励
for agent_id in env.agents:
    print(f"{agent_id}: {env.cumulative_rewards[agent_id]:.2f}")
```

### 奖励组件分析
```python
# 获取详细奖励分解
reward_calculator = env.reward_calculator
episode_summary = reward_calculator.get_episode_summary()

print(f"任务完成率: {episode_summary['completion_rate']:.2%}")
print(f"平均延迟: {episode_summary['average_delay']:.2f}s")
print(f"总能耗: {episode_summary['total_energy']:.2f}J")
print(f"每任务能耗: {episode_summary['energy_per_task']:.2f}J")
```

## 常见问题排查

### 配置问题
```python
# 检查配置文件是否正确加载
try:
    env = Space3PettingZooEnv("path/to/config.yaml")
except FileNotFoundError as e:
    print(f"配置文件未找到: {e}")
except ValueError as e:
    print(f"配置验证失败: {e}")
```

### 依赖模块问题  
```python
# 检查关键依赖模块
try:
    from src.env.Foundation_Layer.time_manager import TimeManager
    from src.env.physics_layer.orbital import OrbitalUpdater
    from src.env.satellite_cloud.satellite_compute import SatelliteCompute
    print("所有依赖模块加载成功")
except ImportError as e:
    print(f"模块导入失败: {e}")
```

### 观测形状问题
```python
# 验证观测形状
observation_builder = env.observation_builder
expected_shapes = observation_builder.get_observation_shape()

for agent_id in env.agents:
    obs = observations[agent_id]
    for key, expected_shape in expected_shapes.items():
        actual_shape = obs[key].shape
        if actual_shape != expected_shape:
            print(f"形状不匹配 {agent_id}.{key}: "
                  f"期望{expected_shape}, 实际{actual_shape}")
```

## 扩展开发

### 自定义奖励函数
```python
class CustomRewardCalculator(RewardCalculator):
    def _calculate_custom_bonus(self, state: Dict) -> float:
        # 实现自定义奖励逻辑
        return custom_bonus
        
    def calculate_rewards(self, action_results, satellite_states, task_statistics):
        # 调用父类方法获取基础奖励
        base_rewards = super().calculate_rewards(action_results, satellite_states, task_statistics)
        
        # 添加自定义奖励
        for agent_id in base_rewards:
            custom_bonus = self._calculate_custom_bonus(satellite_states[agent_id])
            base_rewards[agent_id] += custom_bonus
            
        return base_rewards
```

### 自定义观测构建
```python
class CustomObservationBuilder(ObservationBuilder):
    def _build_custom_features(self, satellite, task_queue) -> np.ndarray:
        # 实现自定义特征提取
        return custom_features
        
    def build_observation(self, agent_id, satellite, task_queue, **kwargs):
        # 获取基础观测
        base_obs = super().build_observation(agent_id, satellite, task_queue, **kwargs)
        
        # 添加自定义特征
        base_obs['custom_features'] = self._build_custom_features(satellite, task_queue)
        
        return base_obs
```

## 并行训练示例

### 多环境并行
```python
from src.env.space_env import make_parallel_env

# 创建多个并行环境
envs = make_parallel_env(n_envs=4, config_path="config.yaml")

# 并行重置
observations_list = []
for env in envs:
    obs, _ = env.reset()
    observations_list.append(obs)

# 并行执行
for step in range(100):
    actions_list = []
    for env in envs:
        actions = {agent: env.action_spaces[agent].sample() for agent in env.agents}
        actions_list.append(actions)
    
    results_list = []
    for env, actions in zip(envs, actions_list):
        results = env.step(actions)
        results_list.append(results)
```

## 日志配置
```python
import logging

# 配置日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 特定模块日志
orbital_logger = logging.getLogger('src.env.physics_layer.orbital')
orbital_logger.setLevel(logging.DEBUG)
```

---

**相关文档**:
- [环境架构文档](./SPACE3_Environment_Architecture.md)
- [接口参考文档](./Interface_Reference.md)

**版本**: v1.0  
**更新**: 2024年