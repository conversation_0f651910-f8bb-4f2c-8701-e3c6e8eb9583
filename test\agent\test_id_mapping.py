"""
测试任务分配中的ID映射问题
"""

import sys
from pathlib import Path
import numpy as np
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.orbital import OrbitalUpdater

# 加载任务生成器
print("="*60)
print("检查任务生成器的地面站数据")
print("="*60)
generator = TaskGenerator()
csv_path = Path(__file__).parent.parent.parent / 'src/env/env_data/global_ground_stations.csv'
generator.load_locations_from_csv(str(csv_path))

print(f"任务生成器地面站总数: {len(generator.locations)}")
print("\n前5个地面站:")
for i in range(5):
    loc = generator.locations[i]
    print(f"  索引{i}: ID={loc.location_id}, 位置=({loc.latitude}, {loc.longitude})")

print("\n后5个地面站:")
for i in range(-5, 0):
    loc = generator.locations[i]
    print(f"  索引{len(generator.locations)+i}: ID={loc.location_id}, 位置=({loc.latitude}, {loc.longitude})")

# 加载orbital
print("\n" + "="*60)
print("检查OrbitalUpdater的地面站数据")
print("="*60)
config_path = Path(__file__).parent.parent.parent / 'src/env/physics_layer/config.yaml'
orbital = OrbitalUpdater(None, str(config_path))

print(f"OrbitalUpdater地面站总数: {len(orbital.ground_stations)}")
gs_keys = sorted([int(k) for k in orbital.ground_stations.keys()])
print(f"地面站键范围: {min(gs_keys)} 到 {max(gs_keys)}")

# 构建可见性矩阵
satellites_dict = orbital.get_satellites_at_time(1)
sg_visibility, sg_distances = orbital.build_satellite_ground_visibility_matrix(satellites_dict, 1)

print(f"\n可见性矩阵形状: {sg_visibility.shape}")
print(f"距离矩阵形状: {sg_distances.shape}")

# 获取地面站的顺序（按字典顺序）
ground_station_list = list(orbital.ground_stations.values())
satellite_list = list(satellites_dict.values())

print(f"卫星数量: {len(satellite_list)}")
print(f"地面站数量: {len(ground_station_list)}")

# 显示地面站在矩阵中的顺序
print(f"\n地面站在矩阵中的顺序（前10个）:")
for i in range(min(10, len(ground_station_list))):
    gs = ground_station_list[i]
    print(f"  矩阵列{i}: 地面站ID={gs.station_id}, 位置=({gs.latitude}, {gs.longitude})")

print(f"\n地面站在矩阵中的顺序（后10个）:")
for i in range(max(0, len(ground_station_list)-10), len(ground_station_list)):
    gs = ground_station_list[i]
    print(f"  矩阵列{i}: 地面站ID={gs.station_id}, 位置=({gs.latitude}, {gs.longitude})")

# 关键问题：检查ID映射
print("\n" + "="*60)
print("检查任务分配时的ID映射问题")
print("="*60)

print("\n测试任务location_id到ground_id的映射:")
test_cases = [1, 10, 100, 200, 300, 400, 420]

for loc_id in test_cases:
    # 模拟任务分配中的转换
    ground_id = loc_id - 1  # 这是代码中的转换: ground_id = task.location_id - 1
    
    print(f"\n任务location_id={loc_id}:")
    print(f"  -> ground_id(数组索引)={ground_id}")
    
    # 从任务生成器获取位置
    task_location = None
    for loc in generator.locations:
        if loc.location_id == loc_id:
            task_location = loc
            break
    
    if task_location:
        print(f"  任务生成位置: ({task_location.latitude}, {task_location.longitude})")
    
    # 检查可见性矩阵中对应的地面站
    if ground_id < len(ground_station_list):
        matrix_gs = ground_station_list[ground_id]
        print(f"  可见性矩阵中对应的地面站ID: {matrix_gs.station_id}")
        print(f"  矩阵中地面站位置: ({matrix_gs.latitude}, {matrix_gs.longitude})")
        
        # 比较位置是否匹配
        if task_location:
            if matrix_gs.latitude == task_location.latitude and matrix_gs.longitude == task_location.longitude:
                print("  ✓ 位置匹配!")
            else:
                print(f"  ✗ 位置不匹配! 差异: Δlat={matrix_gs.latitude - task_location.latitude}, Δlon={matrix_gs.longitude - task_location.longitude}")
    else:
        print(f"  ✗ ground_id {ground_id} 超出可见性矩阵范围!")

# 检查地面站在矩阵中的顺序
print("\n" + "="*60)
print("检查可见性矩阵中地面站ID的顺序")
print("="*60)

# 检查地面站ID是否按顺序排列
ground_ids = [gs.station_id for gs in ground_station_list]
is_sorted = all(int(ground_ids[i]) <= int(ground_ids[i+1]) 
                for i in range(len(ground_ids)-1))
print(f"地面站IDs是否有序: {is_sorted}")
print(f"地面站ID列表前10个: {ground_ids[:10]}")
print(f"地面站ID列表后10个: {ground_ids[-10:]}")

# 检查地面站位置分布
print("\n可见性矩阵中地面站的纬度分布:")
matrix_lats = [gs.latitude for gs in ground_station_list]

if matrix_lats:
    print(f"  纬度范围: {min(matrix_lats)} 到 {max(matrix_lats)}")
    print(f"  北纬(>0)数量: {sum(1 for lat in matrix_lats if lat > 0)}")
    print(f"  南纬(<0)数量: {sum(1 for lat in matrix_lats if lat < 0)}")
    print(f"  南纬<-50数量: {sum(1 for lat in matrix_lats if lat < -50)}")
    
# 检查前100个地面站的纬度
print("\n前100个矩阵位置对应的纬度:")
for i in range(0, min(100, len(ground_station_list)), 10):
    gs = ground_station_list[i]
    print(f"  索引{i}: 地面站ID={gs.station_id}, 纬度={gs.latitude}")

print("\n" + "="*60)
print("问题诊断完成")
print("="*60)