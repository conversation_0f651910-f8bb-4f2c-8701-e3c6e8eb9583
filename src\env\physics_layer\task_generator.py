"""
Task Generator Module for SPACE2 Satellite Simulation
Responsible for generating tasks based on geographic locations and their characteristics
"""

import numpy as np
import pandas as pd
import yaml
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging

# Absolute imports according to coding standard
from src.env.physics_layer.task_models import (
    Location, Task, TaskType, GeographyType, ScaleType, 
    FunctionalType, TaskGenerationConfig
)
from src.env.Foundation_Layer.logging_config import get_logger
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError


class TaskGenerator:
    """
    Task generator that creates tasks based on geographic location characteristics
    using Poisson distribution for task arrival rates
    """
    
    def __init__(self, config_file: str = None, seed: int = None):
        """
        Initialize task generator with configuration
        
        Args:
            config_file: Path to configuration file (defaults to config.yaml)
            seed: Random seed for reproducibility (None for true randomness)
        """
        # Setup paths
        self.config_file = config_file or str(Path(__file__).parent / "config.yaml")
        
        # Load configuration
        self.config = self._load_config()
        self.generation_config = TaskGenerationConfig.from_config(self.config)
        
        # Initialize logger
        self.logger = get_logger(__name__)
        
        # Setup random seed - if None, don't set seed (use true randomness)
        self.seed = seed
        if self.seed is not None:
            np.random.seed(self.seed)
            self.logger.info(f"TaskGenerator initialized with fixed seed={self.seed}")
        else:
            # Don't set seed - use NumPy's default random state
            # This ensures true randomness for each run
            pass
        
        # Task ID counter
        self.task_id_counter = 0
        
        # Location data
        self.locations: List[Location] = []
        
        # System parameters from config
        system_config = self.config.get('system', {})
        self.timeslot_duration = system_config.get('timeslot_duration_s', 3)
        
        if self.seed is None:
            self.logger.info("TaskGenerator initialized with random seed (true randomness)")
    
    @handle_errors(module="task_generator", function="load_config")
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from yaml file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            raise SpaceSimulationError(
                message=f"Failed to load config file: {self.config_file}",
                error_code="CONFIG_LOAD_ERROR"
            )
    
    @handle_errors(module="task_generator", function="load_locations")
    def load_locations_from_csv(self, csv_file_path: str) -> None:
        """
        Load geographic locations from CSV file
        
        Args:
            csv_file_path: Path to CSV file containing location data
        """
        try:
            df = pd.read_csv(csv_file_path, encoding='utf-8')
            
            self.locations = []
            for _, row in df.iterrows():
                # Parse geography type
                geography = GeographyType.LAND if row['RegionType'] == 'Land' else GeographyType.OCEAN
                
                # Parse scale type
                scale_map = {
                    'Small': ScaleType.SMALL,
                    'Medium': ScaleType.MEDIUM,
                    'Large': ScaleType.LARGE
                }
                scale = scale_map.get(row['Size'], ScaleType.SMALL)
                
                # Parse functional type
                func_map = {
                    'Normal': FunctionalType.NORMAL,
                    'Industrial': FunctionalType.INDUSTRIAL,
                    'DelaySensitive': FunctionalType.DELAY_SENSITIVE
                }
                functional = func_map.get(row['PurposeType'], FunctionalType.NORMAL)
                
                location = Location(
                    location_id=int(row['ID']),
                    latitude=float(row['Latitude']),
                    longitude=float(row['Longitude']),
                    geography=geography,
                    scale=scale,
                    functional_type=functional
                )
                self.locations.append(location)
            
            self.logger.info(f"Loaded {len(self.locations)} locations from {csv_file_path}")
            
        except FileNotFoundError:
            raise SpaceSimulationError(
                message=f"Location CSV file not found: {csv_file_path}",
                error_code="FILE_NOT_FOUND"
            )
        except Exception as e:
            raise SpaceSimulationError(
                message=f"Error loading locations: {str(e)}",
                error_code="LOCATION_LOAD_ERROR"
            )
    
    def calculate_lambda(self, location: Location) -> float:
        """
        Calculate task generation rate (λ) for a location based on its characteristics
        
        Args:
            location: Location object
            
        Returns:
            Lambda value for Poisson distribution
        """
        config = self.generation_config
        
        if location.geography == GeographyType.OCEAN:
            return config.ocean_lambda
        elif location.geography == GeographyType.LAND:
            if location.scale == ScaleType.LARGE:
                return config.land_large_lambda
            elif location.scale == ScaleType.MEDIUM:
                return config.land_medium_lambda
            elif location.scale == ScaleType.SMALL:
                return config.land_small_lambda
        
        # Default fallback
        return config.ocean_lambda
    
    def sample_task_type(self, functional_type: FunctionalType) -> TaskType:
        """
        Sample task type based on functional type probabilities
        
        Args:
            functional_type: Functional type of the location
            
        Returns:
            Selected task type
        """
        config = self.generation_config
        
        # Get probability distribution based on functional type
        if functional_type == FunctionalType.NORMAL:
            probabilities = config.normal_probabilities
        elif functional_type == FunctionalType.INDUSTRIAL:
            probabilities = config.industrial_probabilities
        elif functional_type == FunctionalType.DELAY_SENSITIVE:
            probabilities = config.delay_sensitive_probabilities
        else:
            probabilities = config.normal_probabilities
        
        # Sample task type
        task_types = [TaskType.REALTIME, TaskType.NORMAL, TaskType.COMPUTE_INTENSIVE]
        return np.random.choice(task_types, p=probabilities)
    
    def generate_task_parameters(self, task_type: TaskType, current_time: float) -> Dict[str, Any]:
        """
        Generate task parameters based on task type
        
        Args:
            task_type: Type of task
            current_time: Current simulation time
            
        Returns:
            Dictionary containing task parameters
        """
        config = self.generation_config
        
        if task_type == TaskType.REALTIME:
            data_size_mb = np.random.uniform(*config.type1_data_range)
            complexity = config.type1_complexity
            deadline = current_time + config.type1_deadline_offset
        elif task_type == TaskType.NORMAL:
            data_size_mb = np.random.uniform(*config.type2_data_range)
            complexity = config.type2_complexity
            deadline = current_time + config.type2_deadline_offset
        elif task_type == TaskType.COMPUTE_INTENSIVE:
            data_size_mb = np.random.uniform(*config.type3_data_range)
            complexity = config.type3_complexity
            deadline = current_time + config.type3_deadline_offset
        else:
            # Fallback to normal type
            data_size_mb = np.random.uniform(*config.type2_data_range)
            complexity = config.type2_complexity
            deadline = current_time + config.type2_deadline_offset
        
        # Generate priority
        priority = np.random.randint(config.min_priority, config.max_priority + 1)
        
        return {
            'data_size_mb': data_size_mb,
            'complexity_cycles_per_bit': complexity,
            'deadline_timestamp': deadline,
            'priority': priority
        }
    
    @handle_errors(module="task_generator", function="generate_tasks")
    def generate_tasks_for_location(self, location: Location, timeslot: int) -> List[Task]:
        """
        Generate tasks for a specific location at a given timeslot
        
        Args:
            location: Location object
            timeslot: Current timeslot number
            
        Returns:
            List of generated tasks
        """
        # Calculate current time based on timeslot
        current_time = timeslot * self.timeslot_duration
        
        # Calculate task generation rate
        lambda_rate = self.calculate_lambda(location)
        
        # Sample number of tasks using Poisson distribution
        num_tasks = np.random.poisson(lambda_rate)
        
        tasks = []
        for _ in range(num_tasks):
            # Sample task type based on location's functional type
            task_type = self.sample_task_type(location.functional_type)
            
            # Generate task parameters
            params = self.generate_task_parameters(task_type, current_time)
            
            # Create task object
            self.task_id_counter += 1
            task = Task(
                task_id=self.task_id_counter,
                type_id=task_type,
                data_size_mb=params['data_size_mb'],
                complexity_cycles_per_bit=params['complexity_cycles_per_bit'],
                deadline_timestamp=params['deadline_timestamp'],
                priority=params['priority'],
                location_id=location.location_id,
                coordinates=location.coordinates,
                generation_time=current_time,
                geography=location.geography,
                scale=location.scale,
                functional_type=location.functional_type
            )
            tasks.append(task)
        
        if num_tasks > 0:
            self.logger.debug(
                f"Generated {num_tasks} tasks for location {location.location_id} "
                f"at timeslot {timeslot}"
            )
        
        return tasks
    
    def generate_tasks_for_timeslot(self, timeslot: int) -> Dict[int, List[Task]]:
        """
        Generate tasks for all locations at a given timeslot
        
        Args:
            timeslot: Current timeslot number
            
        Returns:
            Dictionary mapping location_id to list of tasks
        """
        if not self.locations:
            self.logger.warning("No locations loaded, cannot generate tasks")
            return {}
        
        tasks_by_location = {}
        total_tasks = 0
        
        for location in self.locations:
            tasks = self.generate_tasks_for_location(location, timeslot)
            if tasks:
                tasks_by_location[location.location_id] = tasks
                total_tasks += len(tasks)
        
        self.logger.info(
            f"Timeslot {timeslot}: Generated {total_tasks} tasks "
            f"across {len(tasks_by_location)} locations"
        )
        
        return tasks_by_location
    
    def reset(self, seed: Optional[int] = None):
        """
        Reset the generator state
        
        Args:
            seed: Optional new random seed (None to keep randomness)
        """
        self.task_id_counter = 0
        if seed is not None:
            self.seed = seed
            np.random.seed(self.seed)
            self.logger.info(f"TaskGenerator reset with fixed seed={self.seed}")
        else:
            self.seed = None
            # Don't reset seed - keep using random state
            self.logger.info("TaskGenerator reset with random seed")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get generation statistics
        
        Returns:
            Dictionary containing statistics
        """
        if not self.locations:
            return {"error": "No locations loaded"}
        
        # Calculate location statistics
        land_count = sum(1 for loc in self.locations if loc.geography == GeographyType.LAND)
        ocean_count = sum(1 for loc in self.locations if loc.geography == GeographyType.OCEAN)
        
        scale_counts = {
            ScaleType.SMALL: sum(1 for loc in self.locations if loc.scale == ScaleType.SMALL),
            ScaleType.MEDIUM: sum(1 for loc in self.locations if loc.scale == ScaleType.MEDIUM),
            ScaleType.LARGE: sum(1 for loc in self.locations if loc.scale == ScaleType.LARGE)
        }
        
        func_counts = {
            FunctionalType.NORMAL: sum(1 for loc in self.locations if loc.functional_type == FunctionalType.NORMAL),
            FunctionalType.INDUSTRIAL: sum(1 for loc in self.locations if loc.functional_type == FunctionalType.INDUSTRIAL),
            FunctionalType.DELAY_SENSITIVE: sum(1 for loc in self.locations if loc.functional_type == FunctionalType.DELAY_SENSITIVE)
        }
        
        return {
            "total_locations": len(self.locations),
            "total_tasks_generated": self.task_id_counter,
            "geography_distribution": {
                "land": land_count,
                "ocean": ocean_count
            },
            "scale_distribution": {k.value: v for k, v in scale_counts.items()},
            "functional_distribution": {k.value: v for k, v in func_counts.items()}
        }