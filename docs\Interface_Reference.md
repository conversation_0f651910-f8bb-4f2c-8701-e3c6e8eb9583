# SPACE3 接口参考文档

## 目录
- [1. 环境主接口](#1-环境主接口)
- [2. 物理层接口](#2-物理层接口)
- [3. 计算层接口](#3-计算层接口)
- [4. 环境组件接口](#4-环境组件接口)
- [5. 数据模型](#5-数据模型)
- [6. 异常处理](#6-异常处理)

---

## 1. 环境主接口

### 1.1 Space3PettingZooEnv 类

#### 1.1.1 构造函数
```python
def __init__(self, config_path: str = None)
```
**参数**:
- `config_path`: 环境配置文件路径，默认为 `src/env/space_env/env_config.yaml`

**功能**: 初始化SPACE3多智能体环境
**抛出异常**: 
- `FileNotFoundError`: 配置文件不存在
- `ValueError`: 配置验证失败

#### 1.1.2 PettingZoo标准接口

```python
def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]
```
**参数**:
- `seed`: 随机种子
- `options`: 重置选项（保留参数）

**返回值**:
- `observations`: 所有智能体的初始观测 `{agent_id: observation_dict}`
- `infos`: 额外信息 `{agent_id: info_dict}`

**观测结构**:
```python
observation = {
    'own_state': np.ndarray(shape=(12,), dtype=np.float32),
    'task_queue': np.ndarray(shape=(20, 8), dtype=np.float32),
    'task_mask': np.ndarray(shape=(20,), dtype=np.int8),
    'action_mask': np.ndarray(shape=(20, 16), dtype=np.int8),
    'neighbor_states': np.ndarray(shape=(10, 5), dtype=np.float32),
    'comm_quality': np.ndarray(shape=(10,), dtype=np.float32),
    'time_info': np.ndarray(shape=(3,), dtype=np.float32)
}
```

```python
def step(self, actions: Dict[str, np.ndarray]) -> Tuple[Dict, Dict, Dict, Dict, Dict]
```
**参数**:
- `actions`: 所有智能体的动作 `{agent_id: action_sequence}`
- `action_sequence`: `np.ndarray(shape=(20,), dtype=int)` 每个元素范围 [0, 15]

**返回值**:
- `observations`: 新观测 `{agent_id: observation_dict}`
- `rewards`: 奖励 `{agent_id: float}`
- `terminations`: 是否终止 `{agent_id: bool}`
- `truncations`: 是否截断 `{agent_id: bool}`
- `infos`: 步骤信息 `{agent_id: info_dict}`

**Info结构**:
```python
info = {
    'tasks_in_queue': int,
    'tasks_completed': int,
    'tasks_dropped': int,
    'energy_consumed': float,
    'cpu_usage': float,
    'cumulative_reward': float,
    'valid_actions': int,      # 来自动作执行结果
    'invalid_actions': int,
    'tasks_assigned': int,
    'local_assignments': int,
    'neighbor_assignments': int,
    'cloud_assignments': int,
    'tasks_dropped': int
}
```

#### 1.1.3 空间定义

```python
@property
def observation_spaces(self) -> Dict[str, spaces.Space]
```
**返回**: 每个智能体的观测空间定义

```python
@property  
def action_spaces(self) -> Dict[str, spaces.Space]
```
**返回**: 每个智能体的动作空间定义

```python
@property
def agents(self) -> List[str]
```
**返回**: 当前活跃智能体列表 `['sat_111', 'sat_112', ..., 'sat_182']`

#### 1.1.4 内部方法接口

```python
def _get_real_visibility_matrices(self, timeslot: int) -> Dict[str, np.ndarray]
```
**功能**: 获取真实可见性矩阵
**返回**:
```python
{
    'satellite_to_satellite': np.ndarray(shape=(72, 72), dtype=float),
    'satellite_to_ground': np.ndarray(shape=(72, 420), dtype=float),
    'satellite_to_cloud': np.ndarray(shape=(72, 5), dtype=float)
}
```

```python
def _distribute_new_tasks(self, tasks: List[Task]) -> None
```
**功能**: 基于可见性分配新生成的任务到卫星队列

```python
def _process_all_actions(self, actions: Dict[str, np.ndarray]) -> Dict[str, Dict]
```
**功能**: 处理所有智能体的动作序列
**返回**: 每个智能体的动作执行结果统计

---

## 2. 物理层接口

### 2.1 TimeManager 接口

#### 2.1.1 核心时间方法
```python
def get_time_context(self, simulation_step: int) -> TimeContext
```
**参数**: `simulation_step` - 仿真步数 [0, total_timeslots-1]
**返回**: `TimeContext` 对象
**功能**: 获取完整时间上下文信息

```python
@dataclass
class TimeContext:
    simulation_step: int          # 仿真步数
    simulation_time: float        # 仿真时间（秒）
    physical_time: datetime       # 对应物理时间
    timeslot_duration: float      # 时隙持续时间（秒）
```

```python
def get_simulation_time(self, simulation_step: int) -> float
```
**计算公式**: `simulation_time = simulation_step * timeslot_duration`

```python
def get_physical_time(self, simulation_step: int) -> datetime
```
**计算公式**: `physical_time = start_time + simulation_time`

#### 2.1.2 验证方法
```python
def is_valid_step(self, simulation_step: int) -> bool
```
**功能**: 检查仿真步数是否在有效范围内

```python
def get_time_range(self, start_step: int, end_step: int) -> List[TimeContext]
```
**功能**: 批量获取时间上下文列表

#### 2.1.3 工厂方法
```python
def create_time_manager_from_config(config: dict) -> TimeManager
```
**配置要求**:
```python
config = {
    'system': {
        'start_time': '2024-01-01T00:00:00',    # ISO格式时间字符串
        'timeslot_duration_s': 5,                # 时隙持续时间（秒）
        'num_timeslots': 1441                    # 总时隙数
    }
}
```

### 2.2 OrbitalUpdater 接口

#### 2.2.1 卫星状态获取
```python
def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]
```
**参数**: `time_step` - 时间步数
**返回**: 卫星ID到Satellite对象的映射
**Satellite对象属性**:
```python
@dataclass
class Satellite:
    satellite_id: str              # 卫星ID，如 'sat_111'
    time_slot: int                 # 时隙
    time: float                    # 时间戳
    latitude: float                # 纬度（度）
    longitude: float               # 经度（度）
    altitude: float                # 高度（米）
    x_ecef: float                  # ECEF X坐标（米）
    y_ecef: float                  # ECEF Y坐标（米）
    z_ecef: float                  # ECEF Z坐标（米）
    illuminated: bool              # 是否光照
    operational: bool              # 是否运行
```

#### 2.2.2 可见性矩阵构建
```python
def build_visibility_matrices(self, satellites: Dict[str, Satellite], time_step: int) -> Dict[str, np.ndarray]
```
**功能**: 构建三种可见性矩阵
**返回**:
```python
{
    'satellite_to_satellite': np.ndarray,    # (72, 72) 卫星间可见性
    'satellite_to_ground': np.ndarray,       # (72, 420) 卫星-地面可见性
    'satellite_to_cloud': np.ndarray         # (72, 5) 卫星-云可见性
}
```

**可见性判断阈值**:
- 卫星-卫星: 5500 km
- 卫星-地面: 2500 km  
- 卫星-云: 3300 km

#### 2.2.3 距离计算
```python
def calculate_distance_matrix(self, positions1: np.ndarray, positions2: np.ndarray) -> np.ndarray
```
**参数**:
- `positions1`: 第一组位置 (N, 3) ECEF坐标
- `positions2`: 第二组位置 (M, 3) ECEF坐标
**返回**: 距离矩阵 (N, M) 单位：米

```python
def calculate_distance(self, sat1: Satellite, sat2: Satellite) -> float
```
**功能**: 计算两颗卫星间3D距离
**单位**: 千米

#### 2.2.4 辅助方法
```python
def get_total_timeslots(self) -> int
def get_satellite_ids(self, time_step: int) -> List[str]
def get_ground_station_count(self) -> int    # 返回 420
def get_cloud_station_count(self) -> int     # 返回 5
```

### 2.3 CommunicationManager 接口

#### 2.3.1 激光链路通信
```python
def get_isl_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]
```
**返回**:
```python
{
    'data_rate_bps': np.ndarray,           # (72, 72) 数据速率，bps
    'snr_db': np.ndarray,                  # (72, 72) 信噪比，dB
    'distance_km': np.ndarray,             # (72, 72) 距离，km
    'visibility': np.ndarray,              # (72, 72) 可见性 0/1
    'propagation_delay_ms': np.ndarray,    # (72, 72) 传播延迟，ms
    'satellite_ids': List[str]             # 卫星ID列表
}
```

**ISL特性**:
- 激光频率: 1.94×10¹⁴ Hz
- 固定数据速率: 50 Gbps
- 传播延迟: distance_km / 299792.458

#### 2.3.2 RF链路通信
```python
def get_satellite_ground_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]
```
**返回**:
```python
{
    'uplink_data_rate_bps': np.ndarray,      # (72, 420) 上行速率
    'uplink_snr_db': np.ndarray,             # (72, 420) 上行SNR
    'downlink_data_rate_bps': np.ndarray,    # (72, 420) 下行速率
    'downlink_snr_db': np.ndarray,           # (72, 420) 下行SNR
    'distance_km': np.ndarray,               # (72, 420) 距离
    'visibility': np.ndarray,                # (72, 420) 可见性
    'elevation_angle_deg': np.ndarray,       # (72, 420) 仰角
    'path_loss_db': np.ndarray               # (72, 420) 路径损耗
}
```

```python
def get_satellite_cloud_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]
```
**返回**: 类似ground通信，但维度为(72, 5)

#### 2.3.3 通信质量计算
```python
def calculate_path_loss(self, distance_km: np.ndarray, frequency_hz: float) -> np.ndarray
```
**公式**: `Path_Loss(dB) = 20*log10(4π*d*f/c)`

```python
def calculate_snr(self, rx_power_dbm: np.ndarray, bandwidth_hz: float) -> np.ndarray
```
**公式**: `SNR(dB) = Rx_Power(dBm) - Noise_Power(dBm)`

```python
def calculate_data_rate(self, snr_db: np.ndarray, bandwidth_hz: float) -> np.ndarray
```
**Shannon公式**: `C = B * log₂(1 + SNR) * η`

#### 2.3.4 链路质量指标
```python
def get_link_quality_metrics(self, time_step: int) -> Dict[str, Dict]
```
**返回**:
```python
{
    'isl': {
        'avg_data_rate_gbps': float,
        'active_links': int,
        'total_possible_links': int,
        'connectivity_ratio': float
    },
    'satellite_ground': {
        'avg_uplink_rate_mbps': float,
        'avg_downlink_rate_mbps': float,
        'active_links': int,
        'coverage_ratio': float
    },
    'satellite_cloud': {
        'avg_uplink_rate_mbps': float,
        'avg_downlink_rate_mbps': float,
        'active_links': int,
        'connectivity_ratio': float
    }
}
```

### 2.4 TaskGenerator 接口

#### 2.4.1 位置数据管理
```python
def load_locations_from_csv(self, csv_file_path: str) -> None
```
**CSV格式要求**:
```csv
location_id,latitude,longitude,geographical_type,scale_type,functional_type
0,-89.5,-179.5,Ocean,Large,DataProcessing
1,-89.5,-178.5,Land,Medium,Communication
...
```

**地理类型枚举**:
```python
class GeographicalType(Enum):
    OCEAN = "Ocean"
    LAND = "Land"

class ScaleType(Enum): 
    LARGE = "Large"
    MEDIUM = "Medium"
    SMALL = "Small"

class FunctionalType(Enum):
    DATA_PROCESSING = "DataProcessing"
    COMMUNICATION = "Communication" 
    COMPUTATION = "Computation"
```

#### 2.4.2 任务生成
```python
def generate_tasks_for_timeslot(self, timeslot: int) -> Dict[int, List[Task]]
```
**功能**: 为所有位置生成指定时隙的任务
**返回**: location_id -> Task列表的映射

```python
def generate_tasks_for_location(self, location: Location, timeslot: int) -> List[Task]
```
**生成过程**:
1. 计算任务到达率λ
2. 泊松分布采样任务数量
3. 为每个任务分配类型和参数

#### 2.4.3 任务到达率计算
```python
def calculate_lambda(self, location: Location) -> float
```
**计算规则**:
```python
lambda_values = {
    (GeographicalType.OCEAN, ScaleType.LARGE): 1.0,
    (GeographicalType.LAND, ScaleType.LARGE): 10.0,
    (GeographicalType.LAND, ScaleType.MEDIUM): 6.0,
    (GeographicalType.LAND, ScaleType.SMALL): 3.0
}
```

#### 2.4.4 任务类型配置
```python
TASK_TYPE_CONFIG = {
    TaskType.REALTIME: {
        'data_size_mb_range': (10, 20),
        'complexity_cycles_per_bit': 100,
        'deadline_seconds': 5,
        'priority_range': (4, 5)
    },
    TaskType.NORMAL: {
        'data_size_mb_range': (20, 50),
        'complexity_cycles_per_bit': 200,
        'deadline_seconds': 15,
        'priority_range': (2, 4)
    },
    TaskType.COMPUTE_INTENSIVE: {
        'data_size_mb_range': (50, 100),
        'complexity_cycles_per_bit': 500,
        'deadline_seconds': 30,
        'priority_range': (1, 3)
    }
}
```

#### 2.4.5 功能类型概率分布
```python
FUNCTIONAL_TYPE_PROBABILITIES = {
    FunctionalType.DATA_PROCESSING: {
        TaskType.REALTIME: 0.6,
        TaskType.NORMAL: 0.3,
        TaskType.COMPUTE_INTENSIVE: 0.1
    },
    FunctionalType.COMMUNICATION: {
        TaskType.REALTIME: 0.8,
        TaskType.NORMAL: 0.2,
        TaskType.COMPUTE_INTENSIVE: 0.0
    },
    FunctionalType.COMPUTATION: {
        TaskType.REALTIME: 0.1,
        TaskType.NORMAL: 0.3,
        TaskType.COMPUTE_INTENSIVE: 0.6
    }
}
```

---

## 3. 计算层接口

### 3.1 SatelliteCompute 接口

#### 3.1.1 构造函数
```python
def __init__(self, satellite_id: str, config: Dict = None, 
             orbital_updater: Optional[OrbitalUpdater] = None,
             comm_manager: Optional[CommunicationManager] = None)
```

#### 3.1.2 任务管理
```python
def add_task(self, task: Task) -> bool
```
**功能**: 添加任务到处理队列
**检查项**:
1. 队列容量 (max_tasks_in_queue)
2. 能量可行性 (battery_level > energy_threshold)
3. 任务有效性

**返回**: 是否成功添加

```python
def process_timeslot(self, duration: float, illuminated: bool) -> ProcessingResult
```
**功能**: 处理一个时隙的任务
**参数**:
- `duration`: 时隙持续时间（秒）
- `illuminated`: 是否在阳光照射下

**处理流程**:
1. 更新电池状态（太阳能充电/放电）
2. DPSQ动态优先级调度
3. 并行任务处理（最多200个并行任务）
4. 能量消耗计算
5. 完成任务统计

**返回**:
```python
@dataclass
class ProcessingResult:
    completed_tasks: List[ComputeTask]      # 完成的任务
    dropped_tasks: List[ComputeTask]        # 丢弃的任务
    energy_consumed: float                  # 消耗的能量（焦耳）
    processing_time_used: float             # 使用的处理时间（秒）
    cpu_utilization: float                  # CPU利用率
    battery_level_after: float              # 处理后电池电量百分比
```

#### 3.1.3 状态查询
```python
def get_queue_status(self) -> Dict[str, Any]
```
**返回**:
```python
{
    'queue_length': int,                    # 队列中任务数
    'num_processing': int,                  # 正在处理的任务数
    'num_waiting': int,                     # 等待中的任务数
    'battery_level': float,                 # 电池电量百分比
    'cpu_utilization': float,               # CPU利用率
    'can_accept_tasks': bool                # 是否可接受新任务
}
```

```python
def get_energy_status(self) -> Dict[str, Any]
```
**返回**:
```python
{
    'battery_capacity_j': float,            # 电池容量（焦耳）
    'current_charge_j': float,              # 当前电量（焦耳）
    'battery_level': float,                 # 电量百分比
    'total_consumed': float,                # 总消耗能量
    'in_sunlight': bool,                    # 是否光照
    'can_accept_tasks': bool,               # 是否可接受新任务
    'solar_power_w': float,                 # 太阳能功率（瓦特）
    'charge_rate_w': float                  # 当前充电功率
}
```

```python
def get_statistics(self) -> Dict[str, Any]
```
**返回**:
```python
{
    'total_completed': int,                 # 总完成任务数
    'total_dropped': int,                   # 总丢弃任务数
    'total_energy_consumed': float,         # 总能量消耗
    'avg_utilization': float,               # 平均CPU利用率
    'avg_processing_time': float,           # 平均处理时间
    'current_load': int,                    # 当前负载
    'uptime': float,                        # 运行时间
    'efficiency_ratio': float               # 效率比率
}
```

#### 3.1.4 DPSQ调度算法
```python
def calculate_dynamic_priority_score(self, task: ComputeTask, current_time: float) -> float
```
**评分公式**:
```
Score = w_p × f_p(P_i) + w_d × f_d(D_i,t) - w_c × f_c(S_i,C_i)
```

**因子定义**:
- `f_p(P_i) = P_i / 5.0` (优先级因子)
- `f_d(D_i,t) = 1.0 / (1.0 + remaining_time/60.0)` (截止时间因子)  
- `f_c(S_i,C_i) = C_i / (S_i × 10⁹)` (复杂度因子)

**权重配置**:
- `w_p = 0.4` (优先级权重)
- `w_d = 0.4` (截止时间权重)
- `w_c = 0.2` (复杂度权重)

#### 3.1.5 能量模型
```python
def update_battery(self, duration: float, illuminated: bool) -> float
```
**充电模型**:
```python
if illuminated:
    charge_power = solar_power_kw * 1000  # 5000W
    energy_gained = charge_power * duration
    new_charge = min(battery_capacity, current_charge + energy_gained)
```

**任务能耗计算**:
```python
energy_per_task = task.complexity * zeta_leo  # zeta_leo = 1e-9 J/cycle
```

### 3.2 CloudCompute 接口

#### 3.2.1 构造函数
```python
def __init__(self, cloud_id: str, config: Dict = None)
```

#### 3.2.2 任务处理
```python
def add_task(self, task: Task, arrival_time: float = 0.0) -> bool
```
**特点**: 云计算容量大，很少拒绝任务

```python
def process_batch(self, duration: float) -> List[ComputeTask]
```
**功能**: 批量并行处理任务
**特点**: 
- 无能量约束
- 高并行度处理
- CPU频率是卫星的2倍 (100 GHz vs 50 GHz)

#### 3.2.3 容量管理
```python
def get_processing_capacity(self) -> Dict[str, Any]
```
**返回**:
```python
{
    'total_capacity': int,                  # 总处理容量
    'available_capacity': int,              # 可用容量
    'utilization_rate': float,              # 利用率
    'queue_utilization': float,             # 队列利用率
    'max_parallel_tasks': int,              # 最大并行任务数
    'current_processing': int               # 当前处理任务数
}
```

```python
def get_queue_status(self) -> Dict[str, Any]
```
**返回**:
```python
{
    'queue_length': int,                    # 队列长度
    'num_processing': int,                  # 处理中任务数
    'queue_utilization': float,             # 队列利用率
    'processing_utilization': float,        # 处理利用率
    'estimated_wait_time': float            # 估计等待时间
}
```

#### 3.2.4 调度策略
```python
def set_scheduling_policy(self, policy: str) -> None
```
**支持策略**:
- `"FIFO"`: 先进先出（默认）
- `"PRIORITY"`: 优先级调度
- `"SJF"`: 最短任务优先

```python
def estimate_completion_time(self, task: ComputeTask) -> float
```
**功能**: 估算任务完成时间，考虑队列等待

---

## 4. 环境组件接口

### 4.1 ObservationBuilder 接口

#### 4.1.1 构造函数
```python
def __init__(self, max_queue_size: int = 20, task_feature_dim: int = 8,
             state_feature_dim: int = 12, neighbor_feature_dim: int = 5,
             num_action_targets: int = 16, max_visible_neighbors: int = 10)
```

#### 4.1.2 观测构建
```python
def build_observation(self, agent_id: str, satellite, task_queue: List,
                     visibility_matrices: Dict, comm_matrix: Optional[np.ndarray],
                     current_timeslot: int, max_timeslots: int,
                     satellite_index: int, all_satellites: Dict) -> Dict[str, np.ndarray]
```

**观测组件构建方法**:

```python
def _build_own_state(self, satellite, current_timeslot: int, max_timeslots: int) -> np.ndarray
```
**状态特征 [12维]**:
```python
[
    cpu_utilization,        # CPU利用率 [0,1]
    memory_utilization,     # 内存利用率 [0,1]  
    battery_level,          # 电池电量 [0,1]
    queue_length,          # 队列长度 [0,1]
    waiting_tasks,         # 等待任务数 [0,1]
    completed_tasks,       # 完成任务数 [0,1]
    dropped_tasks,         # 丢弃任务数 [0,1]
    energy_consumed,       # 能量消耗 [0,1]
    illumination_status,   # 光照状态 {0,1}
    time_progress,         # 时间进度 [0,1]
    remaining_time,        # 剩余时间 [0,1]
    processing_load        # 处理负载 [0,1]
]
```

```python
def _build_task_queue(self, task_queue: List) -> Tuple[np.ndarray, np.ndarray]
```
**任务特征 [8维]**:
```python
[
    task_type,            # 任务类型 [0,1]
    priority,             # 优先级 [0,1]
    data_size,           # 数据大小 [0,1]
    complexity,          # 计算复杂度 [0,1]
    remaining_deadline,   # 剩余截止时间 [0,1]
    latitude,            # 归一化纬度 [0,1]
    longitude,           # 归一化经度 [0,1]
    urgency              # 紧急程度 [0,1]
]
```

```python
def _build_action_mask(self, task_queue: List, satellite_index: int,
                      visibility_matrices: Dict) -> np.ndarray
```
**掩码规则**:
- 本地处理: 总是可行 (mask[i, 0] = 1)
- 邻居转发: 基于可见性 (mask[i, 1:11] 根据satellite_to_satellite矩阵)
- 云卸载: 需要地面站可见 (mask[i, 11:16] 根据satellite_to_ground矩阵)

```python
def _build_neighbor_states(self, satellite_index: int, visibility_matrices: Dict,
                          all_satellites: Dict) -> np.ndarray
```
**邻居特征 [5维]**:
```python
[
    visibility_strength,  # 可见性强度 [0,1]
    cpu_utilization,     # 邻居CPU利用率 [0,1]
    queue_length,        # 邻居队列长度 [0,1]
    battery_level,       # 邻居电池电量 [0,1]
    illumination        # 邻居光照状态 {0,1}
]
```

#### 4.1.3 验证和形状查询
```python
def validate_observation(self, observation: Dict) -> bool
```
**验证项**:
- 键完整性检查
- 形状匹配验证  
- 数值范围检查 [0,1]

```python
def get_observation_shape(self) -> Dict[str, Tuple]
```
**返回**: 各观测组件的形状字典

### 4.2 SequenceActionHandler 接口

#### 4.2.1 构造函数
```python
def __init__(self, config: Dict)
```

#### 4.2.2 动作处理
```python
def process_action_sequence(self, action_sequence: np.ndarray, task_queue: List,
                           agent_id: str, visibility_info: Dict,
                           resource_info: Optional[Dict] = None) -> List[Dict]
```

**处理结果格式**:
```python
assignment = {
    'task': Task,                 # 任务对象
    'action': int,               # 原始动作索引
    'target': ActionTarget,      # 解析后的目标
    'success': bool,             # 是否成功
    'agent_id': str,            # 执行智能体ID
    'task_index': int           # 任务在队列中的索引
}
```

```python
def parse_action(self, action: int, agent_id: str, visibility_info: Dict) -> ActionTarget
```

**ActionTarget结构**:
```python
@dataclass
class ActionTarget:
    target_type: str              # 'local', 'neighbor', 'cloud', 'invalid'
    target_id: Optional[str]      # 目标ID，如 'sat_112' 或 'cloud_0'
    target_index: Optional[int]   # 目标索引
    is_valid: bool               # 是否有效
    reason: Optional[str]        # 无效原因
```

#### 4.2.3 批量验证
```python
def validate_batch_actions(self, all_actions: Dict[str, np.ndarray],
                          all_task_queues: Dict[str, List],
                          global_visibility: Dict,
                          global_resources: Optional[Dict] = None) -> Dict[str, List]
```

**验证流程**:
1. 解析每个智能体的动作序列
2. 检查可见性约束
3. 验证目标容量限制
4. 负载均衡检查

#### 4.2.4 动作映射
```python
action_map = {
    0: ActionTarget(target_type='local'),                      # 本地处理
    1-10: ActionTarget(target_type='neighbor', target_index=i-1),  # 邻居卫星
    11-15: ActionTarget(target_type='cloud', target_index=i-11)    # 云中心
}
```

#### 4.2.5 统计接口
```python
def get_statistics(self) -> Dict[str, Any]
```
**返回**:
```python
{
    'total_actions': int,
    'valid_actions': int,
    'invalid_actions': int,
    'local_assignments': int,
    'neighbor_assignments': int,
    'cloud_assignments': int,
    'validation_failures': {
        'out_of_range': int,
        'no_visibility': int,
        'target_overload': int,
        'resource_insufficient': int
    }
}
```

### 4.3 RewardCalculator 接口

#### 4.3.1 构造函数
```python
def __init__(self, config: Dict)
```

**奖励权重配置**:
```python
reward_weights = {
    'completion': 10.0,           # 任务完成奖励权重
    'drop': -5.0,                # 任务丢弃惩罚权重
    'delay': -0.01,              # 延迟惩罚权重
    'energy': -0.001,            # 能耗惩罚权重
    'load_balance': 1.0,         # 负载均衡奖励权重
    'invalid_action': -1.0,      # 无效动作惩罚权重
    'cooperation': 2.0           # 协作奖励权重
}
```

#### 4.3.2 奖励计算
```python
def calculate_rewards(self, action_results: Dict, satellite_states: Dict,
                     task_statistics: Dict, global_metrics: Optional[Dict] = None) -> Dict[str, float]
```

**输入格式**:
```python
# action_results: 来自ActionHandler的执行结果
# satellite_states: 来自SatelliteCompute的状态信息
satellite_states[agent_id] = {
    'completed_tasks': int,
    'dropped_tasks': int,
    'energy_consumed': float,
    'cpu_usage': float,
    'memory_usage': float,
    'queue_length': int,
    'battery_level': float
}

# task_statistics: 任务统计信息
task_statistics[agent_id] = {
    'average_delay': float,
    'completed_priority_sum': float,
    'dropped_priority_sum': float,
    'timeout_count': int
}
```

#### 4.3.3 奖励组件计算

```python
def _calculate_completion_reward(self, state: Dict, task_stats: Dict) -> float
```
**公式**: `reward = completed_tasks × weight_completion + priority_bonus`

```python
def _calculate_delay_penalty(self, state: Dict, task_stats: Dict) -> float
```
**公式**: `penalty = weight_delay × (normalized_delay²) × 100`

```python
def _calculate_energy_penalty(self, state: Dict) -> float
```
**公式**: `penalty = weight_energy × normalized_energy × 100 + battery_bonus`

```python
def _calculate_load_balance_bonus(self, state: Dict, load_variance: float) -> float
```
**均衡奖励**: 当负载方差低于目标值时给予奖励

```python
def _calculate_cooperation_bonus(self, agent_id: str, action_results: Dict,
                                satellite_states: Dict) -> float
```
**协作奖励**: 成功转发任务给邻居或云的奖励

#### 4.3.4 塑形奖励
```python
def compute_shaped_reward(self, current_state: Dict, previous_state: Dict,
                         agent_id: str) -> float
```
**塑形项**:
- 队列长度改善奖励
- 电池管理奖励（充电/避免过放）
- 负载优化奖励

#### 4.3.5 统计接口
```python
def get_episode_summary(self) -> Dict[str, float]
```
**返回**:
```python
{
    'completion_rate': float,      # 任务完成率
    'average_delay': float,        # 平均延迟
    'total_energy': float,         # 总能量消耗
    'energy_per_task': float,      # 每任务能耗
    'total_completed': int,        # 总完成任务数
    'total_dropped': int          # 总丢弃任务数
}
```

---

## 5. 数据模型

### 5.1 任务相关模型

#### 5.1.1 Task类
```python
@dataclass
class Task:
    task_id: str                    # 任务唯一标识
    type_id: TaskType              # 任务类型枚举
    priority: int                  # 优先级 [1-5]
    data_size_mb: float           # 数据大小（MB）
    complexity_cycles_per_bit: float  # 计算复杂度（cycles/bit）
    deadline_timestamp: float      # 截止时间戳
    generation_time: float         # 生成时间
    location_id: int              # 生成位置ID
    coordinates: Tuple[float, float]  # 地理坐标（纬度，经度）
    assignment_status: AssignmentStatus  # 分配状态
```

#### 5.1.2 TaskType枚举
```python
class TaskType(Enum):
    REALTIME = 1                  # 实时任务
    NORMAL = 2                    # 普通任务  
    COMPUTE_INTENSIVE = 3         # 计算密集型任务
```

#### 5.1.3 ComputeTask类
```python
@dataclass
class ComputeTask:
    task_id: str                  # 任务ID
    priority: float               # 优先级
    deadline: float               # 截止时间
    data_size_mb: float          # 数据大小
    complexity: float            # 计算复杂度（cycles）
    drop_penalty: float          # 丢弃惩罚
    arrival_time: float          # 到达时间
    start_time: Optional[float]  # 开始处理时间
    completion_time: Optional[float]  # 完成时间
    status: TaskStatus           # 任务状态
```

#### 5.1.4 TaskStatus枚举
```python
class TaskStatus(Enum):
    WAITING = "waiting"           # 等待中
    PROCESSING = "processing"     # 处理中
    COMPLETED = "completed"       # 已完成
    DROPPED = "dropped"          # 已丢弃
    TIMEOUT = "timeout"          # 超时
```

### 5.2 位置相关模型

#### 5.2.1 Location类
```python
@dataclass
class Location:
    location_id: int                      # 位置ID
    latitude: float                       # 纬度
    longitude: float                      # 经度
    geographical_type: GeographicalType   # 地理类型
    scale_type: ScaleType                # 规模类型
    functional_type: FunctionalType      # 功能类型
```

### 5.3 电池模型

#### 5.3.1 Battery类
```python
class Battery:
    def __init__(self, capacity_j: float, initial_charge_percent: float = 100.0):
        self.capacity = capacity_j            # 电池容量（焦耳）
        self.current_charge = capacity_j * initial_charge_percent / 100.0
        
    def get_charge_percentage(self) -> float:
        """获取电量百分比"""
        
    def consume_energy(self, energy_j: float) -> bool:
        """消耗能量，返回是否成功"""
        
    def charge(self, energy_j: float) -> float:
        """充电，返回实际充入的能量"""
        
    def can_provide_energy(self, required_energy_j: float) -> bool:
        """检查是否能提供所需能量"""
```

---

## 6. 异常处理

### 6.1 自定义异常类

```python
class SpaceSimulationError(Exception):
    """SPACE3仿真基础异常类"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ConfigurationError(SpaceSimulationError):
    """配置错误"""
    pass

class DataLoadError(SpaceSimulationError):
    """数据加载错误"""
    pass

class ComputationError(SpaceSimulationError):
    """计算错误"""
    pass

class ResourceExhaustionError(SpaceSimulationError):
    """资源耗尽错误"""
    pass
```

### 6.2 错误处理装饰器

```python
def handle_errors(module: str, function: str):
    """统一错误处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {module}.{function}: {str(e)}")
                raise SpaceSimulationError(
                    message=f"Failed in {module}.{function}: {str(e)}",
                    error_code=f"{module.upper()}_{function.upper()}_FAILURE"
                )
        return wrapper
    return decorator
```

### 6.3 使用示例

```python
@handle_errors(module="orbital", function="update_positions")
def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]:
    if not self.is_valid_timestep(time_step):
        raise ValueError(f"Invalid time step: {time_step}")
    # 实现逻辑...
```

---

**文档版本**: v1.0  
**最后更新**: 2024年  
**相关文档**: [SPACE3_Environment_Architecture.md](./SPACE3_Environment_Architecture.md)