"""
测试本地运行程序
验证系统各组件是否正常工作
"""

import sys
import os
from pathlib import Path
import yaml
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from agent.local.run_local import LocalRunner


def test_initialization():
    """测试系统初始化"""
    print("测试系统初始化...")
    
    config_path = Path(__file__).parent.parent.parent / "src" / "env" / "physics_layer" / "config.yaml"
    
    try:
        runner = LocalRunner(str(config_path))
        print("✓ 系统初始化成功")
        
        # 验证组件
        assert runner.orbital_updater is not None, "轨道管理器未初始化"
        print("✓ 轨道管理器初始化成功")
        
        assert runner.comm_manager is not None, "通信管理器未初始化"
        print("✓ 通信管理器初始化成功")
        
        assert runner.task_generator is not None, "任务生成器未初始化"
        print("✓ 任务生成器初始化成功")
        
        assert len(runner.satellites) == 72, f"卫星数量错误: {len(runner.satellites)}"
        print(f"✓ 成功初始化 {len(runner.satellites)} 个卫星")
        
        return runner
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        raise


def test_single_timeslot(runner: LocalRunner):
    """测试单个时隙运行"""
    print("\n测试单个时隙运行...")
    
    try:
        # 运行第一个时隙
        timeslot = 1
        current_time = 0.0
        
        # 更新轨道
        runner.orbital_updater.update_visibility_matrices(timeslot)
        print("✓ 轨道更新成功")
        
        # 生成任务
        tasks = runner.task_generator.generate_tasks(timeslot)
        print(f"✓ 生成了 {len(tasks)} 个任务")
        
        # 获取可见性矩阵
        sg_visibility = runner.orbital_updater.get_satellite_ground_visibility(timeslot)
        print(f"✓ 获取可见性矩阵成功，形状: {sg_visibility.shape}")
        
        # 分配任务
        runner._assign_tasks_to_satellites(tasks, sg_visibility, current_time)
        
        # 统计分配情况
        total_queued = sum(len(sat.task_queue) for sat in runner.satellites)
        print(f"✓ 任务分配成功，队列中总任务数: {total_queued}")
        
        # 处理任务
        runner._process_satellite_tasks(current_time)
        
        # 统计处理情况
        total_processing = sum(len(sat.current_tasks) for sat in runner.satellites)
        print(f"✓ 任务处理启动，处理中任务数: {total_processing}")
        
        return True
    except Exception as e:
        print(f"✗ 时隙运行失败: {e}")
        raise


def test_detailed_output(runner: LocalRunner):
    """测试详细输出功能"""
    print("\n测试详细输出功能...")
    
    try:
        # 运行几个时隙以产生数据
        for timeslot in range(1, 6):
            current_time = (timeslot - 1) * 5.0
            
            # 更新和处理
            runner.orbital_updater.update_visibility_matrices(timeslot)
            tasks = runner.task_generator.generate_tasks(timeslot)
            sg_visibility = runner.orbital_updater.get_satellite_ground_visibility(timeslot)
            runner._assign_tasks_to_satellites(tasks, sg_visibility, current_time)
            runner._process_satellite_tasks(current_time)
            
            # 输出详细数据
            if timeslot in [1, 5]:
                print(f"\n输出时隙 {timeslot} 的详细数据:")
                runner.output_detailed_timeslot_data(timeslot, current_time)
        
        print("✓ 详细输出功能正常")
        return True
    except Exception as e:
        print(f"✗ 详细输出失败: {e}")
        raise


def test_metrics_extraction(runner: LocalRunner):
    """测试指标提取功能"""
    print("\n测试指标提取功能...")
    
    try:
        # 运行更多时隙以生成足够数据
        for timeslot in range(6, 11):
            current_time = (timeslot - 1) * 5.0
            runner.orbital_updater.update_visibility_matrices(timeslot)
            tasks = runner.task_generator.generate_tasks(timeslot)
            sg_visibility = runner.orbital_updater.get_satellite_ground_visibility(timeslot)
            runner._assign_tasks_to_satellites(tasks, sg_visibility, current_time)
            runner._process_satellite_tasks(current_time)
        
        # 提取指标
        simulation_data = {
            'satellites': runner.satellites,
            'satellites_tasks': [
                sat.completed_tasks + sat.current_tasks + sat.dropped_tasks 
                for sat in runner.satellites
            ]
        }
        
        metrics = runner.metrics_analyzer.extract_metrics(simulation_data)
        
        # 验证指标
        assert 'latency_metrics' in metrics, "缺少延迟指标"
        print("✓ 延迟指标提取成功")
        
        assert 'energy_metrics' in metrics, "缺少能耗指标"
        print("✓ 能耗指标提取成功")
        
        assert 'completion_metrics' in metrics, "缺少完成率指标"
        print("✓ 完成率指标提取成功")
        
        # 输出一些关键指标
        if 'summary' in metrics:
            summary = metrics['summary']
            print(f"\n关键指标:")
            for key, value in summary.items():
                print(f"  {key}: {value}")
        
        return True
    except Exception as e:
        print(f"✗ 指标提取失败: {e}")
        raise


def main():
    """主测试函数"""
    print("="*60)
    print("开始测试本地运行程序")
    print("="*60)
    
    try:
        # 测试初始化
        runner = test_initialization()
        
        # 测试单时隙运行
        test_single_timeslot(runner)
        
        # 测试详细输出
        test_detailed_output(runner)
        
        # 测试指标提取
        test_metrics_extraction(runner)
        
        print("\n" + "="*60)
        print("所有测试通过！")
        print("="*60)
        
    except Exception as e:
        print("\n" + "="*60)
        print(f"测试失败: {e}")
        print("="*60)
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())