"""
算法性能指标统一接口模块
为算法对比研究提供标准化的性能评估接口
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import json
import csv
import logging
from pathlib import Path
import yaml

from src.env.metrics_model.latency_analyzer import LatencyAnalyzer
from src.env.metrics_model.energy_analyzer import EnergyAnalyzer
from src.env.metrics_model.completion_analyzer import CompletionAnalyzer
from src.env.physics_layer.task_tracking import TaskTrackingRecord
from src.env.satellite_cloud.satellite import Satellite


@dataclass
class AlgorithmResult:
    """算法运行结果"""
    algorithm_name: str
    task_records: Optional[List[TaskTrackingRecord]] = None
    satellites: Optional[List[Satellite]] = None
    time_window: Optional[Tuple[float, float]] = None
    additional_info: Dict = None


class AlgorithmMetrics:
    """算法性能指标提取主类"""
    
    def __init__(self, config_path: str):
        """
        初始化指标提取器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 初始化各分析器
        self.latency_analyzer = LatencyAnalyzer(config_path)
        self.energy_analyzer = EnergyAnalyzer(config_path)
        self.completion_analyzer = CompletionAnalyzer(config_path)
        
        self.logger = logging.getLogger(__name__)
    
    def extract_metrics(self, 
                       simulation_data: Dict,
                       time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        从仿真数据提取性能指标
        
        Args:
            simulation_data: 仿真数据字典，包含:
                - 'task_records': 任务跟踪记录列表
                - 'satellites': 卫星实例列表
                - 'satellites_tasks': 卫星任务列表
            time_window: 时间窗口(开始时间, 结束时间)
            
        Returns:
            综合性能指标字典
        """
        metrics = {
            'time_window': time_window,
            'latency_metrics': {},
            'energy_metrics': {},
            'completion_metrics': {}
        }
        
        # 提取延迟指标
        if 'task_records' in simulation_data and simulation_data['task_records']:
            latency_results = self.latency_analyzer.analyze_task_records(
                simulation_data['task_records'],
                time_window
            )
            metrics['latency_metrics']['task_based'] = latency_results
        
        if 'satellites_tasks' in simulation_data and simulation_data['satellites_tasks']:
            latency_results = self.latency_analyzer.analyze_satellite_tasks(
                simulation_data['satellites_tasks'],
                time_window
            )
            metrics['latency_metrics']['satellite_based'] = latency_results
        
        # 提取能耗指标
        if 'satellites' in simulation_data and simulation_data['satellites']:
            energy_results = self.energy_analyzer.analyze_satellites(
                simulation_data['satellites'],
                time_window
            )
            metrics['energy_metrics'] = energy_results
        elif 'task_records' in simulation_data and simulation_data['task_records']:
            energy_results = self.energy_analyzer.analyze_task_records(
                simulation_data['task_records'],
                time_window
            )
            metrics['energy_metrics'] = energy_results
        
        # 提取完成率指标
        if 'satellites_tasks' in simulation_data and simulation_data['satellites_tasks']:
            completion_results = self.completion_analyzer.analyze_satellite_tasks(
                simulation_data['satellites_tasks'],
                time_window
            )
            metrics['completion_metrics'] = completion_results
        elif 'task_records' in simulation_data and simulation_data['task_records']:
            completion_results = self.completion_analyzer.analyze_task_records(
                simulation_data['task_records'],
                time_window
            )
            metrics['completion_metrics'] = completion_results
        
        # 计算综合指标
        metrics['summary'] = self._calculate_summary(metrics)
        
        return metrics
    
    def extract_metrics_from_satellites(self,
                                      satellites: List[Satellite],
                                      time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        从卫星实例直接提取指标
        
        Args:
            satellites: 卫星实例列表
            time_window: 时间窗口
            
        Returns:
            性能指标字典
        """
        # 收集所有任务
        satellites_tasks = []
        for sat in satellites:
            all_tasks = (sat.completed_tasks + 
                        sat.current_tasks + 
                        sat.dropped_tasks + 
                        sat.task_queue)
            satellites_tasks.append(all_tasks)
        
        simulation_data = {
            'satellites': satellites,
            'satellites_tasks': satellites_tasks
        }
        
        return self.extract_metrics(simulation_data, time_window)
    
    def compare_algorithms(self, 
                         algorithm_results: List[AlgorithmResult]) -> Dict[str, Any]:
        """
        对比多个算法的性能
        
        Args:
            algorithm_results: 算法运行结果列表
            
        Returns:
            算法对比结果字典
        """
        comparison = {
            'algorithms': [],
            'comparison_table': {},
            'best_performers': {}
        }
        
        # 提取每个算法的指标
        for result in algorithm_results:
            simulation_data = {}
            if result.task_records:
                simulation_data['task_records'] = result.task_records
            if result.satellites:
                simulation_data['satellites'] = result.satellites
                # 收集卫星任务
                satellites_tasks = []
                for sat in result.satellites:
                    all_tasks = (sat.completed_tasks + 
                               sat.current_tasks + 
                               sat.dropped_tasks + 
                               sat.task_queue)
                    satellites_tasks.append(all_tasks)
                simulation_data['satellites_tasks'] = satellites_tasks
            
            metrics = self.extract_metrics(simulation_data, result.time_window)
            
            comparison['algorithms'].append({
                'name': result.algorithm_name,
                'metrics': metrics
            })
        
        # 创建对比表
        comparison['comparison_table'] = self._create_comparison_table(comparison['algorithms'])
        
        # 找出最佳表现者
        comparison['best_performers'] = self._find_best_performers(comparison['comparison_table'])
        
        return comparison
    
    def _calculate_summary(self, metrics: Dict) -> Dict:
        """计算综合指标摘要"""
        summary = {}
        
        # 延迟摘要
        if 'latency_metrics' in metrics:
            if 'satellite_based' in metrics['latency_metrics']:
                latency_data = metrics['latency_metrics']['satellite_based']
                if 'end_to_end_latency' in latency_data:
                    summary['avg_end_to_end_latency'] = latency_data['end_to_end_latency'].mean
                if 'normalized_metrics' in latency_data:
                    summary['avg_latency_per_task'] = latency_data['normalized_metrics'].get('avg_latency_per_task', 0)
        
        # 能耗摘要
        if 'energy_metrics' in metrics and 'energy_metrics' in metrics['energy_metrics']:
            energy_data = metrics['energy_metrics']['energy_metrics']
            summary['total_energy'] = energy_data.total_energy
            summary['avg_energy_per_task'] = energy_data.avg_energy_per_task
            summary['avg_energy_per_mb'] = energy_data.avg_energy_per_mb
        
        # 完成率摘要
        if 'completion_metrics' in metrics and 'overall_metrics' in metrics['completion_metrics']:
            completion_data = metrics['completion_metrics']['overall_metrics']
            summary['overall_completion_rate'] = completion_data.overall_completion_rate
            summary['timely_completion_rate'] = completion_data.timely_completion_rate
            
            if 'priority_metrics' in metrics['completion_metrics']:
                priority_data = metrics['completion_metrics']['priority_metrics']
                summary['weighted_completion_rate'] = priority_data.weighted_completion_rate
        
        return summary
    
    def _create_comparison_table(self, algorithms: List[Dict]) -> Dict:
        """创建算法对比表"""
        table = {}
        
        # 提取关键指标
        key_metrics = [
            'avg_end_to_end_latency',
            'avg_latency_per_task',
            'total_energy',
            'avg_energy_per_task',
            'overall_completion_rate',
            'weighted_completion_rate'
        ]
        
        for metric in key_metrics:
            table[metric] = {}
            for alg in algorithms:
                alg_name = alg['name']
                summary = alg['metrics'].get('summary', {})
                table[metric][alg_name] = summary.get(metric, 0)
        
        return table
    
    def _find_best_performers(self, comparison_table: Dict) -> Dict:
        """找出每个指标的最佳表现者"""
        best_performers = {}
        
        for metric, values in comparison_table.items():
            if not values:
                continue
            
            # 根据指标类型确定是最大值还是最小值更好
            if 'latency' in metric or 'energy' in metric:
                # 延迟和能耗越小越好
                best_value = min(values.values())
                best_alg = [k for k, v in values.items() if v == best_value][0]
            else:
                # 完成率越大越好
                best_value = max(values.values())
                best_alg = [k for k, v in values.items() if v == best_value][0]
            
            best_performers[metric] = {
                'algorithm': best_alg,
                'value': best_value
            }
        
        return best_performers
    
    def export_results(self, metrics: Dict, output_path: str, 
                      format: str = 'json') -> None:
        """
        导出分析结果
        
        Args:
            metrics: 指标字典
            output_path: 输出文件路径
            format: 输出格式 ('json', 'csv')
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if format == 'json':
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=2, default=str)
        elif format == 'csv':
            # 将嵌套字典展平为CSV格式
            flat_data = self._flatten_dict(metrics)
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=flat_data.keys())
                writer.writeheader()
                writer.writerow(flat_data)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        self.logger.info(f"Results exported to {output_path}")
    
    def _flatten_dict(self, d: Dict, parent_key: str = '', sep: str = '.') -> Dict:
        """将嵌套字典展平"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif hasattr(v, '__dict__'):
                items.extend(self._flatten_dict(asdict(v), new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)