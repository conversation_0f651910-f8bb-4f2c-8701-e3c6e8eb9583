# SPACE4 系统性能优化详细分析

## 1. 并行化处理实现方案

### 1.1 当前串行处理的问题
```python
# 当前代码：串行处理72个卫星
for sat_id, satellite in self.satellites.items():
    processing_result = satellite.process_timeslot(duration, illuminated)
    # 每个卫星独立处理，但串行执行
```

### 1.2 并行化方案

#### 方案A：多进程并行（推荐）
```python
from multiprocessing import Pool, Manager
import pickle

def process_satellite_task(args):
    """独立的卫星处理函数"""
    sat_id, satellite_state, tasks, duration, illuminated = args
    # 重建卫星对象（因为对象不能直接传递给子进程）
    satellite = SatelliteCompute.from_state(satellite_state)
    result = satellite.process_timeslot(duration, illuminated)
    return sat_id, result, satellite.get_state()

class ParallelLocalComputeSimulator:
    def _process_satellites_parallel(self, timeslot, duration):
        """并行处理所有卫星"""
        # 准备参数
        tasks = []
        for sat_id, satellite in self.satellites.items():
            illuminated = self._check_satellite_illumination(sat_id, timeslot)
            satellite_state = satellite.get_state()  # 序列化卫星状态
            tasks.append((sat_id, satellite_state, None, duration, illuminated))
        
        # 使用进程池并行处理
        with Pool(processes=min(8, len(self.satellites))) as pool:
            results = pool.map(process_satellite_task, tasks)
        
        # 更新卫星状态
        for sat_id, result, new_state in results:
            self.satellites[sat_id].set_state(new_state)
            # 处理结果...
```

#### 方案B：线程并行（适用于I/O密集型）
```python
from concurrent.futures import ThreadPoolExecutor

def _process_satellites_threaded(self, timeslot, duration):
    """使用线程池处理卫星"""
    with ThreadPoolExecutor(max_workers=16) as executor:
        futures = {}
        for sat_id, satellite in self.satellites.items():
            illuminated = self._check_satellite_illumination(sat_id, timeslot)
            future = executor.submit(
                satellite.process_timeslot, duration, illuminated
            )
            futures[future] = sat_id
        
        # 收集结果
        for future in futures:
            sat_id = futures[future]
            result = future.result()
            # 处理结果...
```

### 1.3 并行化的挑战与解决方案

**挑战1：共享状态管理**
- 问题：卫星间可能有通信或协作
- 解决：使用Manager()创建共享数据结构，或在时隙结束后同步

**挑战2：对象序列化**
- 问题：复杂对象不能直接传递给子进程
- 解决：实现get_state()和from_state()方法

**挑战3：负载均衡**
- 问题：某些卫星任务多，某些少
- 解决：使用动态任务分配或工作窃取算法

## 2. DPSQ分数缓存正确性分析

### 2.1 您的担忧是正确的！

DPSQ分数计算公式：
```python
Score(T_i, t_now) = w_p * f_p(P_i) + w_d * f_d(D_i, t_now) - w_c * f_c(S_i, C_i)
```

其中 `f_d(D_i, t_now) = 1 / ((D_i - t_now) + epsilon)` 是时间相关的！

### 2.2 正确的缓存策略

```python
class ImprovedDPSQScheduler:
    def __init__(self):
        # 缓存结构：(task_id, timestamp) -> score
        self.score_cache = {}
        self.cache_granularity = 1.0  # 1秒粒度
        
    def calculate_priority_score(self, task, current_time, bandwidth=None):
        """改进的分数计算，正确使用缓存"""
        
        # 缓存键包含时间戳（量化到秒）
        time_key = int(current_time / self.cache_granularity)
        cache_key = (task.task_id, time_key)
        
        # 检查缓存
        if cache_key in self.score_cache:
            return self.score_cache[cache_key]
        
        # 分离时间相关和时间无关的部分
        # 时间无关部分（可以永久缓存）
        static_score = self._calculate_static_score(task, bandwidth)
        
        # 时间相关部分（必须每次计算）
        time_remaining = max(task.deadline - current_time, 0)
        urgency_score = self.w_urgency / (time_remaining + self.epsilon_urgency)
        
        total_score = static_score + urgency_score
        
        # 缓存（带时间戳）
        self.score_cache[cache_key] = total_score
        
        # 清理旧缓存（避免内存泄漏）
        self._cleanup_old_cache(current_time)
        
        return total_score
    
    def _calculate_static_score(self, task, bandwidth):
        """计算时间无关的分数部分"""
        # 这部分可以永久缓存
        cache_key = (task.task_id, 'static')
        if cache_key in self.static_cache:
            return self.static_cache[cache_key]
            
        f_priority = 4 - task.priority
        communication_time = task.data_size_mb / bandwidth if bandwidth > 0 else float('inf')
        computation_time = task.complexity / self.f_sat
        f_cost = communication_time + computation_time
        
        static_score = self.w_priority * f_priority - self.w_cost * f_cost
        self.static_cache[cache_key] = static_score
        return static_score
```

### 2.3 更好的优化方案：批量计算

```python
def calculate_priority_scores_batch(self, tasks, current_time):
    """批量计算分数，利用NumPy向量化"""
    n = len(tasks)
    
    # 向量化计算
    priorities = np.array([4 - t.priority for t in tasks])
    deadlines = np.array([t.deadline for t in tasks])
    data_sizes = np.array([t.data_size_mb for t in tasks])
    complexities = np.array([t.complexity for t in tasks])
    
    # 批量计算
    f_priority = priorities
    f_urgency = 1.0 / (np.maximum(deadlines - current_time, 0) + self.epsilon_urgency)
    f_cost = data_sizes / self.bandwidth + complexities / self.f_sat
    
    scores = (self.w_priority * f_priority + 
              self.w_urgency * f_urgency - 
              self.w_cost * f_cost)
    
    return scores
```

## 3. 批量任务分配实现

### 3.1 当前的逐个分配
```python
# 当前：每个任务独立查找最近卫星
for task in tasks:
    satellite_id, distance = self.find_nearest_visible_satellite(task, timeslot)
    # 逐个处理...
```

### 3.2 批量分配优化

```python
class BatchTaskDistributor:
    def distribute_tasks_batch(self, tasks, timeslot):
        """批量分配任务到卫星"""
        
        # 1. 一次性获取所有可见性信息
        satellites = self.orbital_updater.get_satellites_at_time(timeslot)
        vis_matrix, dist_matrix = self.orbital_updater.build_satellite_ground_visibility_matrix(
            satellites, timeslot
        )
        
        # 2. 按位置分组任务
        tasks_by_location = {}
        for task in tasks:
            loc_id = task.location_id
            if loc_id not in tasks_by_location:
                tasks_by_location[loc_id] = []
            tasks_by_location[loc_id].append(task)
        
        # 3. 批量分配
        assignments = []
        for loc_id, loc_tasks in tasks_by_location.items():
            ground_idx = self.location_to_ground_idx[loc_id]
            
            # 找到该位置的所有可见卫星
            visible_sats = np.where(vis_matrix[:, ground_idx])[0]
            
            if len(visible_sats) > 0:
                # 找最近的卫星
                distances = dist_matrix[visible_sats, ground_idx]
                nearest_idx = visible_sats[np.argmin(distances)]
                nearest_distance = dist_matrix[nearest_idx, ground_idx]
                
                # 批量分配该位置的所有任务到最近卫星
                for task in loc_tasks:
                    assignment = TaskAssignment(task_id=task.task_id, task=task)
                    assignment.mark_assigned(nearest_idx, nearest_distance, timeslot)
                    assignments.append(assignment)
            else:
                # 无可见卫星，批量标记为待重试
                for task in loc_tasks:
                    assignment = TaskAssignment(task_id=task.task_id, task=task)
                    assignment.mark_retry(timeslot)
                    assignments.append(assignment)
        
        return assignments
```

### 3.3 进一步优化：负载均衡

```python
def distribute_with_load_balancing(self, tasks, timeslot):
    """考虑负载均衡的批量分配"""
    
    # 获取每个卫星的当前负载
    satellite_loads = {sat_id: sat.get_queue_length() 
                      for sat_id, sat in self.satellites.items()}
    
    # 使用匈牙利算法或贪心算法进行负载均衡分配
    # ...
```

## 4. 日志优化与CSV结果缓存

### 4.1 日志优化方案

```python
import logging
import csv
from dataclasses import asdict

class OptimizedSimulator:
    def __init__(self):
        # 配置日志级别
        logging.getLogger().setLevel(logging.WARNING)  # 减少日志输出
        
        # 结果缓存器
        self.result_cache = []
        self.csv_writer = None
        self.csv_file = None
        
    def setup_csv_logging(self, output_path="simulation_results.csv"):
        """设置CSV输出"""
        self.csv_file = open(output_path, 'w', newline='', encoding='utf-8')
        fieldnames = [
            'timeslot', 'task_id', 'task_type', 'priority', 
            'data_size_mb', 'complexity', 'generation_time',
            'assigned_satellite', 'assignment_time', 'completion_time',
            'total_delay', 'energy_consumed', 'status'
        ]
        self.csv_writer = csv.DictWriter(self.csv_file, fieldnames=fieldnames)
        self.csv_writer.writeheader()
    
    def log_task_result(self, timeslot, task, assignment, result):
        """记录任务结果到CSV"""
        row = {
            'timeslot': timeslot,
            'task_id': task.task_id,
            'task_type': task.type_id.value if hasattr(task.type_id, 'value') else task.type_id,
            'priority': task.priority,
            'data_size_mb': task.data_size_mb,
            'complexity': task.complexity_cycles_per_bit,
            'generation_time': task.generation_time,
            'assigned_satellite': assignment.assigned_satellite_id if assignment else None,
            'assignment_time': assignment.assignment_time if assignment else None,
            'completion_time': result.completion_time if result else None,
            'total_delay': result.total_delay if result else None,
            'energy_consumed': result.energy_consumed if result else None,
            'status': result.status if result else 'FAILED'
        }
        
        # 缓存结果
        self.result_cache.append(row)
        
        # 定期写入（每100条）
        if len(self.result_cache) >= 100:
            self.flush_results()
    
    def flush_results(self):
        """将缓存的结果写入CSV"""
        if self.csv_writer and self.result_cache:
            self.csv_writer.writerows(self.result_cache)
            self.csv_file.flush()  # 确保写入磁盘
            self.result_cache = []
    
    def finalize(self):
        """完成时调用，确保所有数据都写入"""
        self.flush_results()
        if self.csv_file:
            self.csv_file.close()
        
        # 生成汇总统计
        self.generate_summary_statistics()
    
    def generate_summary_statistics(self):
        """生成汇总统计CSV"""
        # 读回数据进行统计分析
        import pandas as pd
        df = pd.read_csv("simulation_results.csv")
        
        # 统计分析
        summary = {
            'total_tasks': len(df),
            'completed_tasks': len(df[df['status'] == 'COMPLETED']),
            'failed_tasks': len(df[df['status'] == 'FAILED']),
            'avg_delay': df['total_delay'].mean(),
            'avg_energy': df['energy_consumed'].mean(),
            'completion_rate_by_type': df.groupby('task_type')['status'].apply(
                lambda x: (x == 'COMPLETED').mean()
            ).to_dict()
        }
        
        # 保存汇总
        with open('simulation_summary.json', 'w') as f:
            import json
            json.dump(summary, f, indent=2)
```

### 4.2 使用示例

```python
simulator = OptimizedSimulator()
simulator.setup_csv_logging("results/simulation_run_001.csv")

# 运行仿真
for timeslot in range(100):
    results = simulator.process_timeslot(timeslot)
    # 结果自动记录到CSV

simulator.finalize()  # 完成并生成统计
```

## 5. GPU加速实现方案

### 5.1 可见性矩阵计算的GPU加速

```python
import cupy as cp  # GPU版本的NumPy

class GPUOrbitalUpdater:
    def build_visibility_matrix_gpu(self, satellites, ground_stations):
        """使用GPU加速可见性矩阵计算"""
        
        # 将数据传输到GPU
        sat_positions = cp.array([[s.lat, s.lon, s.alt] for s in satellites])
        ground_positions = cp.array([[g.lat, g.lon, 0] for g in ground_stations])
        
        # GPU上的向量化计算
        # 转换为ECEF坐标（GPU并行）
        sat_ecef = self._lat_lon_to_ecef_gpu(sat_positions)
        ground_ecef = self._lat_lon_to_ecef_gpu(ground_positions)
        
        # 计算距离矩阵（使用GPU的广播机制）
        # 这里72x420的矩阵运算在GPU上会非常快
        diff = sat_ecef[:, cp.newaxis, :] - ground_ecef[cp.newaxis, :, :]
        distances = cp.sqrt(cp.sum(diff**2, axis=2))
        
        # 计算可见性
        visibility = distances <= self.threshold
        
        # 传回CPU（只在需要时）
        return cp.asnumpy(visibility), cp.asnumpy(distances)
    
    def _lat_lon_to_ecef_gpu(self, coords):
        """GPU上的坐标转换"""
        lat_rad = cp.radians(coords[:, 0])
        lon_rad = cp.radians(coords[:, 1])
        alt = coords[:, 2]
        
        r = self.earth_radius + alt
        x = r * cp.cos(lat_rad) * cp.cos(lon_rad)
        y = r * cp.cos(lat_rad) * cp.sin(lon_rad)
        z = r * cp.sin(lat_rad)
        
        return cp.stack([x, y, z], axis=1)
```

### 5.2 批量DPSQ分数计算的GPU加速

```python
class GPUDPSQScheduler:
    def calculate_scores_gpu(self, tasks, current_time):
        """GPU批量计算DPSQ分数"""
        
        # 准备数据
        n = len(tasks)
        priorities = cp.array([t.priority for t in tasks])
        deadlines = cp.array([t.deadline for t in tasks])
        data_sizes = cp.array([t.data_size_mb for t in tasks])
        complexities = cp.array([t.complexity for t in tasks])
        
        # GPU并行计算
        f_priority = 4 - priorities
        time_remaining = cp.maximum(deadlines - current_time, 0)
        f_urgency = 1.0 / (time_remaining + self.epsilon_urgency)
        f_cost = data_sizes / self.bandwidth + complexities / self.f_sat
        
        scores = (self.w_priority * f_priority + 
                 self.w_urgency * f_urgency - 
                 self.w_cost * f_cost)
        
        return cp.asnumpy(scores)
```

### 5.3 GPU加速的注意事项

1. **数据传输开销**：GPU加速只有在数据量大时才有优势
2. **内存限制**：GPU内存有限，需要分批处理
3. **适用场景**：
   - ✅ 矩阵运算（可见性计算）
   - ✅ 批量数值计算（DPSQ分数）
   - ❌ 复杂逻辑控制（任务调度）
   - ❌ 频繁的小数据操作

### 5.4 实际性能提升预估

- 可见性矩阵计算：10-50倍加速
- DPSQ批量计算：5-20倍加速
- 整体系统：1.5-3倍加速（受限于Amdahl定律）

## 总结与建议

1. **并行化**：使用多进程池是最实用的方案，预期2-3倍加速
2. **DPSQ缓存**：必须考虑时间因素，建议使用批量计算而非缓存
3. **批量分配**：按位置分组可大幅减少重复计算
4. **日志优化**：CSV缓存方案既保留完整数据又提升性能
5. **GPU加速**：适合矩阵运算，但需要权衡数据传输开销

**推荐实施顺序**：
1. 先实现日志优化（简单，立即见效）
2. 再实现批量任务分配（中等难度，明显提升）
3. 然后实现并行化（需要重构，但提升最大）
4. 最后考虑GPU加速（需要额外依赖，提升有限）