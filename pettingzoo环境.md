基于Dec-POMDP理论模型的PettingZoo Parallel环境设计

  【设计规范统一声明】
  本文档为最终统一版本，所有类名、接口定义以此为准。
  任何后续修改需通过正式变更管理流程。

  1. 核心映射关系

  将Dec-POMDP模型映射到PettingZoo Parallel框架：

  Dec-POMDP → PettingZoo Parallel映射：
  - 状态空间 $\mathcal{S}$ → 环境内部状态 (self.state)
  - 智能体集合 $\mathcal{L}$ → self.agents (72个LEO卫星)
  - 观测空间 $\Omega_j$ → observation_spaces字典
  - 动作空间 $\mathcal{A}_j$ → action_spaces字典
  - 奖励函数 $\mathcal{R}_j$ → rewards字典
  - 状态转移 $\mathcal{P}$ → step()函数逻辑

  2. 环境架构设计（统一规范）

  class SPACE2Environment(ParallelEnv):
      """
      基于Dec-POMDP的LEO卫星任务调度环境
      """

      def __init__(self):
          # 时间管理 - 1441个时隙
          self.current_timeslot = 0
          self.max_timeslots = 1441

          # 智能体定义 - 72个LEO卫星
          self.agents = [f"satellite_{i}" for i in
  range(72)]
          self.possible_agents = self.agents[:]

          # 全局状态 s(t) - 包含所有系统信息
          self.global_state = {
              'satellite_positions': {},  # 卫星位置
  p_j(t)
              'energy_levels': {},        # 能量状态
  e_j(t)
              'cpu_utilization': {},      # CPU利用率
  ρ_j^cpu(t)
              'task_queues': {},          # 任务队列
  Q_j(t)
              'link_quality_matrix': None # 链路质量
  L(t)
          }

  3. 观测空间设计（统一规范版本）

  基于局部可观测性原则，每个卫星智能体只能观测：

  def observation_space(self, agent):
      """
      o_j(t) = {s_j^local(t), N_j(t), T_j^user(t), mask_j(t)}
      【修正】增加action_mask作为观测的一部分，符合RL标准范式
      """
      return spaces.Dict({
          # 自身状态 s_j^local(t)
          'local_state': spaces.Dict({
              'position': spaces.Box(low=-180, high=180,
   shape=(2,), dtype=np.float32),  # 统一：2D经纬度坐标
              'energy': spaces.Box(low=0, high=100,
  shape=(1,), dtype=np.float32),
              'cpu_utilization': spaces.Box(low=0,
  high=1, shape=(1,), dtype=np.float32),
              'queue_length': spaces.Discrete(self.max_queue_size),  # 统一：使用max_queue_size
              'time_context': spaces.Box(low=0, high=1,
  shape=(2,), dtype=np.float32)  # [当前时隙/总时隙, 轨道相位]
          }),

          # 邻居状态 N_j(t) - 可见卫星信息
          'neighbor_states': spaces.Box(
              low=0, high=1,
              shape=(self.max_neighbors, self.neighbor_features),
              dtype=np.float32
          ),

          # 用户任务 T_j^user(t) - 覆盖区域任务
          'user_tasks': spaces.Box(
              low=0, high=1,
              shape=(self.max_tasks, self.task_features),
              dtype=np.float32
          ),

          # 【修正】动作掩码 - 指示哪些任务槽位有效
          'action_mask': spaces.MultiBinary(self.max_queue_size)
      })

  4. 动作空间设计（修正版本）

  基于结构化复合动作空间理论：

  def action_space(self, agent):
      """
      a_j(t) = <D_j(t), P_j(t)>
      结构化动作：卸载策略 + 资源分配
      【修正】移除action_mask，符合RL标准信息流
      """
      return spaces.Dict({
          # 卸载策略向量 D_j(t) - 离散决策
          # 0: 本地处理, 1: 地面站, 2: 邻居卫星, 3: 云端
          'offloading_decisions': spaces.MultiDiscrete(
              [self.num_offload_targets] * self.max_queue_size,
              dtype=np.int32
          ),

          # 资源分配向量 P_j(t) - 连续决策
          # 每个任务分配的CPU资源比例，总和应≤1
          'resource_allocation': spaces.Box(
              low=0, high=1,
              shape=(self.max_queue_size,),
              dtype=np.float32
          )
      })

  【设计说明】
  - action_mask已移至observation_space，由环境提供给智能体
  - 智能体策略网络应使用mask过滤无效动作
  - 环境在step()中验证动作有效性并处理违规情况

  5. 状态转移机制（修正因果关系）

  def step(self, actions):
      """
      并行环境的核心状态转移函数
      【修正】确保状态更新的因果一致性
      """
      # 1. 动作有效性验证（基于当前状态）
      validated_actions = self._validate_actions(actions)

      # 2. 收集所有智能体动作
      offloading_decisions = {agent:
  validated_actions[agent]['offloading_decisions']
                             for agent in self.agents}
      resource_allocations = {agent:
  validated_actions[agent]['resource_allocation']
                             for agent in self.agents}

      # 3. 基于t时刻状态执行决策（保持因果关系）
      self._execute_offloading(offloading_decisions)  # 基于t时刻链路
      self._allocate_resources(resource_allocations)  # 基于t时刻资源
      completed_tasks = self._process_tasks()         # 处理t时刻任务

      # 4. 状态转移：t → t+1（物理层更新）
      self._update_orbital_dynamics()  # 轨道演进
      self._update_link_quality()      # 基于新位置更新链路
      self._update_energy_states()     # 能量消耗和充电
      self._generate_new_tasks()       # 生成新任务

      # 5. 基于完整状态信息计算奖励
      rewards = self._calculate_rewards(completed_tasks, self.global_state)

      # 6. 生成t+1时刻观测（包含action_mask）
      observations = self._generate_observations()

      # 7. 更新时间步
      self.current_timeslot += 1

      # 8. 检查终止条件
      terminations = {agent: self.current_timeslot >= self.max_timeslots
                     for agent in self.agents}
      truncations = {agent: False for agent in self.agents}

      # 9. 信息字典（包含调试信息）
      infos = {agent: {
          'completed_tasks': completed_tasks.get(agent, 0),
          'energy_level': self.global_state['energy_levels'][agent],
          'queue_utilization': len(self.global_state['task_queues'][agent]) / self.max_queue_size
      } for agent in self.agents}

      return observations, rewards, terminations, truncations, infos

  6. 奖励计算理论（修正数学一致性）

  基于分层奖励机制：

  def _calculate_rewards(self, completed_tasks, global_state):
      """
      r_j(s,a) = r_j^local(s_j,a_j) + r_j^regional(s,a)
      【修正】明确状态依赖，确保数学表达与实现一致
      """
      rewards = {}
      current_state = global_state  # 明确状态依赖

      for agent in self.agents:
          # 个体奖励 r_j^local(s_j,a_j) - 基于局部状态和动作
          local_reward = 0
          priority_weights = {1: 10.0, 2: 6.0, 3: 3.0}

          # 任务完成奖励（依赖于动作效果）
          for priority in [1, 2, 3]:
              completed_p = self._count_completed_tasks(agent, priority)
              local_reward += priority_weights[priority] * completed_p

          # 资源效率奖励（依赖于局部状态）
          energy_efficiency = self._get_energy_efficiency(agent, current_state)
          local_reward += self.alpha * energy_efficiency

          # 个体惩罚项
          local_reward -= self.beta * self._get_energy_consumption(agent)
          local_reward -= self.zeta * self._get_average_delay(agent)

          # 区域协作奖励 r_j^regional(s,a) - 基于全局状态
          regional_reward = 0

          # 负载均衡奖励（全局状态依赖）
          load_balance_score = self._get_regional_load_balance(agent, current_state)
          regional_reward += self.gamma * load_balance_score

          # 协作效率奖励（全局动作依赖）
          cooperation_bonus = self._get_cooperation_bonus(agent, completed_tasks)
          regional_reward += self.delta * cooperation_bonus

          # 总奖励组合
          rewards[agent] = local_reward + regional_reward

      return rewards

  【数学一致性说明】
  - local_reward: 依赖智能体j的局部状态s_j和动作a_j
  - regional_reward: 依赖全局状态s和所有智能体动作a
  - 确保奖励函数的状态依赖性在理论和实现中保持一致

  7. 关键设计考虑

  7.1 部分可观测性处理

  - 观测范围限制：基于卫星间可见性矩阵限制观测
  - 信息延迟：考虑星间通信延迟对观测的影响
  - 观测噪声：添加现实的传感器噪声

  7.2 动作有效性验证（增强版本）

  def _validate_actions(self, actions):
      """
      确保动作满足约束条件
      【修正】增加基于action_mask的验证逻辑
      """
      validated_actions = {}

      for agent, action in actions.items():
          validated_action = action.copy()

          # 获取当前智能体的有效动作掩码
          action_mask = self._get_action_mask(agent)

          # 1. 基于mask过滤无效任务槽位的动作
          for i in range(self.max_queue_size):
              if not action_mask[i]:  # 无效槽位
                  validated_action['offloading_decisions'][i] = 0  # 强制本地处理
                  validated_action['resource_allocation'][i] = 0   # 无资源分配

          # 2. 资源分配约束：总和不超过1
          resource_sum = np.sum(validated_action['resource_allocation'])
          if resource_sum > 1:
              validated_action['resource_allocation'] *= (1.0 / resource_sum)

          # 3. 卸载目标可达性验证
          valid_targets = self._get_valid_offload_targets(agent)
          for i, target in enumerate(validated_action['offloading_decisions']):
              if action_mask[i] and target not in valid_targets:
                  validated_action['offloading_decisions'][i] = 0  # 回退到本地处理

          validated_actions[agent] = validated_action

      return validated_actions

  def _get_action_mask(self, agent):
      """
      生成动作掩码，指示哪些任务槽位有效
      """
      queue = self.global_state['task_queues'][agent]
      mask = np.zeros(self.max_queue_size, dtype=bool)
      mask[:len(queue)] = True  # 只有有任务的槽位才有效
      return mask

  7.3 时空一致性

  - 轨道周期性：利用轨道周期性优化状态预测
  - 时隙同步：确保所有智能体在同一时隙决策
  - 因果关系：保证动作效果的时序因果性

  8. 环境初始化流程

  def reset(self, seed=None, options=None):
      """
      重置环境到初始状态
      """
      # 1. 设置随机种子
      self.np_random, seed = seeding.np_random(seed)

      # 2. 初始化时间
      self.current_timeslot = 0

      # 3. 加载卫星初始位置
      self._load_initial_satellite_positions()

      # 4. 初始化能量状态
      self._initialize_energy_states()

      # 5. 清空任务队列
      self._clear_task_queues()

      # 6. 生成初始用户任务
      self._generate_initial_tasks()

      # 7. 计算初始链路质量
      self._compute_initial_link_quality()

      # 8. 生成初始观测
      observations = self._generate_observations()

      return observations, {}

  9. 理论优势

  1. 真实性：基于实际卫星轨道数据和通信模型
  2. 可扩展性：支持不同规模的卫星星座
  3. 灵活性：动作空间支持复杂的资源调度策略
  4. 完整性：考虑了能量、通信、计算等多重约束
  5. 协同性：通过区域奖励促进多智能体协作

  10. 实现挑战与解决方案

  挑战1：动作空间维度动态变化
  - 解决：使用最大队列长度，配合action_mask机制处理无效动作
  - 【修正】mask现已正确放置在观测空间中

  挑战2：大规模智能体并行计算
  - 解决：向量化操作，批处理状态更新
  - 优化：使用NumPy广播和矩阵运算减少循环

  挑战3：长时序依赖
  - 解决：在观测空间中增加time_context，增强时序表达能力
  - 【修正】统一时间信息的表示格式

  挑战4：奖励稀疏性
  - 解决：设计分层奖励机制，包含中间奖励和协作奖励
  - 【修正】确保奖励函数的数学一致性

  挑战5：逻辑一致性维护
  - 解决：建立统一的设计规范，所有接口定义以本文档为准
  - 【新增】实施严格的变更管理流程，防止设计分歧

  挑战6：因果关系正确性
  - 解决：在step()函数中明确区分基于t时刻状态的决策执行和t→t+1的状态转移
  - 【修正】确保动作效果和状态更新的时序逻辑正确
  【逻辑严密性审查总结】

  本文档已完成以下关键修正：

  1. 设计规范统一化
     - 统一类名为 SPACE2Environment
     - 统一观测空间定义（2D经纬度坐标，包含time_context）
     - 消除重复和冲突的接口定义

  2. 强化学习范式修正
     - 将action_mask从动作空间移至观测空间
     - 确保环境→智能体的信息流正确性
     - 增强动作有效性验证机制

  3. 因果关系修正
     - 明确step()函数中状态更新的时序逻辑
     - 区分基于t时刻的决策执行和t→t+1的状态转移
     - 确保奖励计算的状态依赖性

  4. 数学一致性保证
     - 奖励函数的理论表达与代码实现对齐
     - 明确局部奖励和区域奖励的状态依赖关系

  本规范为最终版本，后续实现应严格遵循此设计。