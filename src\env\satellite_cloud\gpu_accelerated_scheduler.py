"""
GPU加速的DPSQ调度器
使用PyTorch批量计算任务优先级分数
"""

import torch
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import logging
from dataclasses import dataclass

# Absolute imports
from src.env.satellite_cloud.compute_models import ComputeTask
from src.env.satellite_cloud.satellite_compute import DPSQScheduler
from src.env.Foundation_Layer.logging_config import get_logger


class GPUDPSQScheduler(DPSQScheduler):
    """
    GPU加速版本的DPSQ调度器
    使用PyTorch进行批量优先级分数计算
    """
    
    def __init__(self, config: Dict, device: str = None):
        """
        初始化GPU加速调度器
        
        Args:
            config: 配置字典
            device: 计算设备 ('cuda', 'cpu', 或 None自动选择)
        """
        super().__init__(config)
        
        # 设置计算设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        self.logger = get_logger(__name__)
        self.logger.info(f"GPUDPSQScheduler initialized with device: {self.device}")
        
        # 预计算常量张量
        self._precompute_constants()
        
        # 性能统计
        self.gpu_stats = {
            'batch_sizes': [],
            'computation_times': [],
            'transfer_times': []
        }
    
    def _precompute_constants(self):
        """预计算常量并传输到GPU"""
        # 权重参数
        self.w_priority_tensor = torch.tensor(self.w_priority, dtype=torch.float32, device=self.device)
        self.w_urgency_tensor = torch.tensor(self.w_urgency, dtype=torch.float32, device=self.device)
        self.w_cost_tensor = torch.tensor(self.w_cost, dtype=torch.float32, device=self.device)
        
        # 其他常量
        self.epsilon_tensor = torch.tensor(self.epsilon_urgency, dtype=torch.float32, device=self.device)
        self.f_sat_tensor = torch.tensor(self.f_sat, dtype=torch.float32, device=self.device)
        self.bandwidth_tensor = torch.tensor(self.default_bandwidth, dtype=torch.float32, device=self.device)
    
    def calculate_priority_scores_batch(self, tasks: List[ComputeTask], 
                                      current_time: float,
                                      bandwidth: Optional[float] = None) -> np.ndarray:
        """
        GPU批量计算任务优先级分数
        
        Args:
            tasks: 任务列表
            current_time: 当前时间
            bandwidth: 可用带宽
            
        Returns:
            优先级分数数组
        """
        if not tasks:
            return np.array([])
        
        n_tasks = len(tasks)
        
        # 如果任务数太少，使用CPU计算可能更快
        if n_tasks < 10:
            return np.array([self.calculate_priority_score(task, current_time, bandwidth) 
                           for task in tasks])
        
        import time
        transfer_start = time.time()
        
        # 准备数据并传输到GPU
        priorities = torch.tensor([task.priority for task in tasks], 
                                 dtype=torch.float32, device=self.device)
        deadlines = torch.tensor([task.deadline for task in tasks], 
                                dtype=torch.float32, device=self.device)
        data_sizes = torch.tensor([task.data_size_mb for task in tasks], 
                                 dtype=torch.float32, device=self.device)
        complexities = torch.tensor([task.complexity for task in tasks], 
                                   dtype=torch.float32, device=self.device)
        
        current_time_tensor = torch.tensor(current_time, dtype=torch.float32, device=self.device)
        
        if bandwidth is not None and bandwidth > 0:
            bandwidth_tensor = torch.tensor(bandwidth, dtype=torch.float32, device=self.device)
        else:
            bandwidth_tensor = self.bandwidth_tensor
        
        transfer_time = time.time() - transfer_start
        compute_start = time.time()
        
        # GPU批量计算
        # 优先级因子: f_p(P_i) = 4 - P_i
        f_priority = 4.0 - priorities
        
        # 紧急度因子: f_d(D_i, t_now) = 1 / ((D_i - t_now) + epsilon)
        time_remaining = torch.maximum(deadlines - current_time_tensor, 
                                      torch.zeros_like(deadlines))
        f_urgency = 1.0 / (time_remaining + self.epsilon_tensor)
        
        # 成本因子: f_c(S_i, C_i) = S_i/B + C_i/F
        communication_time = data_sizes / bandwidth_tensor
        computation_time = complexities / self.f_sat_tensor
        f_cost = communication_time + computation_time
        
        # 总分数
        scores = (self.w_priority_tensor * f_priority + 
                 self.w_urgency_tensor * f_urgency - 
                 self.w_cost_tensor * f_cost)
        
        compute_time = time.time() - compute_start
        
        # 记录统计
        self.gpu_stats['batch_sizes'].append(n_tasks)
        self.gpu_stats['transfer_times'].append(transfer_time)
        self.gpu_stats['computation_times'].append(compute_time)
        
        # 转换回CPU/NumPy
        return scores.cpu().numpy()
    
    def sort_tasks_by_priority(self, tasks: List[ComputeTask], 
                              current_time: float,
                              bandwidth: Optional[float] = None) -> List[Tuple[ComputeTask, float]]:
        """
        使用GPU批量计算并排序任务
        
        Returns:
            排序后的(任务, 分数)列表
        """
        if not tasks:
            return []
        
        # 批量计算分数
        scores = self.calculate_priority_scores_batch(tasks, current_time, bandwidth)
        
        # 创建(任务, 分数)对并排序
        task_score_pairs = list(zip(tasks, scores))
        task_score_pairs.sort(key=lambda x: x[1], reverse=True)
        
        return task_score_pairs
    
    def batch_estimate_processing_times(self, tasks: List[ComputeTask],
                                       cpu_allocation: float = 1.0,
                                       bandwidth: Optional[float] = None) -> np.ndarray:
        """
        GPU批量估算任务处理时间
        
        Returns:
            处理时间数组 (N, 3) - [通信时间, 计算时间, 总时间]
        """
        if not tasks:
            return np.array([])
        
        n_tasks = len(tasks)
        
        # 传输到GPU
        data_sizes = torch.tensor([task.data_size_mb for task in tasks], 
                                 dtype=torch.float32, device=self.device)
        complexities = torch.tensor([task.complexity for task in tasks], 
                                   dtype=torch.float32, device=self.device)
        
        if bandwidth is not None and bandwidth > 0:
            bandwidth_tensor = torch.tensor(bandwidth, dtype=torch.float32, device=self.device)
        else:
            bandwidth_tensor = self.bandwidth_tensor
        
        cpu_alloc_tensor = torch.tensor(cpu_allocation, dtype=torch.float32, device=self.device)
        overhead_tensor = torch.tensor(1 + self.processing_overhead, dtype=torch.float32, device=self.device)
        
        # 批量计算
        communication_times = data_sizes / bandwidth_tensor
        computation_times = (complexities / (self.f_sat_tensor * cpu_alloc_tensor)) * overhead_tensor
        total_times = communication_times + computation_times
        
        # 组合结果
        results = torch.stack([communication_times, computation_times, total_times], dim=1)
        
        return results.cpu().numpy()
    
    def select_tasks_for_processing(self, tasks: List[ComputeTask],
                                   available_cycles: float,
                                   current_time: float,
                                   bandwidth: Optional[float] = None) -> List[ComputeTask]:
        """
        使用GPU加速选择可以在当前时隙处理的任务
        
        Args:
            tasks: 候选任务列表
            available_cycles: 可用CPU周期
            current_time: 当前时间
            bandwidth: 可用带宽
            
        Returns:
            选中的任务列表
        """
        if not tasks:
            return []
        
        # 批量计算优先级分数
        scores = self.calculate_priority_scores_batch(tasks, current_time, bandwidth)
        
        # 批量估算处理需求
        complexities = torch.tensor([task.complexity for task in tasks], 
                                   dtype=torch.float32, device=self.device)
        
        # 按分数排序
        sorted_indices = np.argsort(scores)[::-1]  # 降序
        
        selected_tasks = []
        remaining_cycles = available_cycles
        
        for idx in sorted_indices:
            task = tasks[idx]
            required_cycles = task.complexity
            
            if required_cycles <= remaining_cycles:
                selected_tasks.append(task)
                remaining_cycles -= required_cycles
            
            # 如果剩余资源太少，停止
            if remaining_cycles < 1e6:  # 小于1M cycles
                break
        
        return selected_tasks
    
    def optimize_cpu_allocation(self, tasks: List[ComputeTask],
                              available_cycles: float,
                              current_time: float) -> Dict[int, float]:
        """
        使用GPU优化CPU分配策略
        
        Returns:
            任务ID到CPU分配比例的映射
        """
        if not tasks:
            return {}
        
        n_tasks = len(tasks)
        
        # 传输到GPU
        priorities = torch.tensor([task.priority for task in tasks], 
                                 dtype=torch.float32, device=self.device)
        deadlines = torch.tensor([task.deadline for task in tasks], 
                                dtype=torch.float32, device=self.device)
        complexities = torch.tensor([task.complexity for task in tasks], 
                                   dtype=torch.float32, device=self.device)
        
        current_time_tensor = torch.tensor(current_time, dtype=torch.float32, device=self.device)
        
        # 计算紧急度
        time_remaining = torch.maximum(deadlines - current_time_tensor, 
                                      torch.ones_like(deadlines))
        urgency_weights = 1.0 / time_remaining
        
        # 计算优先级权重
        priority_weights = (4.0 - priorities) / 3.0  # 归一化到[0.33, 1]
        
        # 综合权重
        combined_weights = urgency_weights * priority_weights
        
        # 计算CPU分配比例
        total_weight = torch.sum(combined_weights)
        if total_weight > 0:
            allocation_ratios = combined_weights / total_weight
        else:
            allocation_ratios = torch.ones(n_tasks, device=self.device) / n_tasks
        
        # 转换回字典
        allocations = {}
        ratios = allocation_ratios.cpu().numpy()
        for task, ratio in zip(tasks, ratios):
            allocations[task.task_id] = float(ratio)
        
        return allocations
    
    def get_performance_stats(self) -> Dict[str, float]:
        """获取GPU性能统计"""
        stats = {}
        
        if self.gpu_stats['batch_sizes']:
            stats['avg_batch_size'] = np.mean(self.gpu_stats['batch_sizes'])
            stats['total_batches'] = len(self.gpu_stats['batch_sizes'])
        
        if self.gpu_stats['transfer_times']:
            stats['avg_transfer_time'] = np.mean(self.gpu_stats['transfer_times'])
            stats['total_transfer_time'] = np.sum(self.gpu_stats['transfer_times'])
        
        if self.gpu_stats['computation_times']:
            stats['avg_computation_time'] = np.mean(self.gpu_stats['computation_times'])
            stats['total_computation_time'] = np.sum(self.gpu_stats['computation_times'])
        
        # 计算加速比
        if self.gpu_stats['computation_times'] and self.gpu_stats['batch_sizes']:
            avg_batch = np.mean(self.gpu_stats['batch_sizes'])
            avg_gpu_time = np.mean(self.gpu_stats['computation_times'])
            # 估算CPU时间（假设每个任务0.001秒）
            estimated_cpu_time = avg_batch * 0.001
            stats['estimated_speedup'] = estimated_cpu_time / avg_gpu_time if avg_gpu_time > 0 else 1.0
        
        stats['device'] = str(self.device)
        
        return stats


class GPUSatelliteCompute:
    """
    使用GPU加速的卫星计算节点
    集成GPU DPSQ调度器
    """
    
    def __init__(self, satellite_id: int, config: Dict, device: str = None):
        """
        初始化GPU加速的卫星计算节点
        """
        self.satellite_id = satellite_id
        self.config = config
        self.device = device
        
        # 使用GPU调度器
        self.scheduler = GPUDPSQScheduler(config, device)
        
        # 任务队列
        self.task_queue: List[ComputeTask] = []
        
        # 其他初始化...
        self.logger = get_logger(f"GPUSatellite_{satellite_id}")
    
    def process_tasks_batch(self, duration: float, available_cycles: float) -> List[ComputeTask]:
        """
        批量处理任务（使用GPU加速）
        """
        if not self.task_queue:
            return []
        
        current_time = getattr(self, 'current_time', 0)
        
        # 使用GPU批量计算优先级并选择任务
        selected_tasks = self.scheduler.select_tasks_for_processing(
            self.task_queue, available_cycles, current_time
        )
        
        # 处理选中的任务
        completed_tasks = []
        for task in selected_tasks:
            # 更新任务状态
            task.status = 'COMPLETED'
            task.completion_time = current_time + duration
            completed_tasks.append(task)
            
            # 从队列移除
            self.task_queue.remove(task)
        
        return completed_tasks