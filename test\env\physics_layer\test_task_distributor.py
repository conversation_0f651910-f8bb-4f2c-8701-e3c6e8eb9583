"""
Test module for TaskDistributor
Tests task distribution and retry mechanism based on satellite visibility
"""

import sys
import os
import numpy as np
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.task_distributor import TaskDistributor
from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_models import (
    Task, TaskType, GeographyType, ScaleType, FunctionalType,
    AssignmentStatus, Location
)
from src.env.physics_layer.orbital import OrbitalUpdater


class TestTaskDistributor:
    """Test class for TaskDistributor"""
    
    def __init__(self):
        """Initialize test environment"""
        self.orbital_updater = OrbitalUpdater()
        self.distributor = TaskDistributor(orbital_updater=self.orbital_updater)
        self.generator = TaskGenerator(seed=42)
        
        # Test timeslots
        self.test_timeslots = [1, 10, 100, 500, 1000]
        
    def create_test_task(self, task_id: int, location_id: int, 
                        lat: float = 40.0, lon: float = -74.0) -> Task:
        """Create a test task with specified parameters"""
        return Task(
            task_id=task_id,
            type_id=TaskType.NORMAL,
            data_size_mb=30.0,
            complexity_cycles_per_bit=200,
            deadline_timestamp=1000.0,
            priority=2,
            location_id=location_id,
            coordinates=(lat, lon),
            generation_time=0.0,
            geography=GeographyType.LAND,
            scale=ScaleType.MEDIUM,
            functional_type=FunctionalType.NORMAL
        )
    
    def test_nearest_satellite_selection(self):
        """Test that the nearest visible satellite is selected"""
        print("\n=== Testing Nearest Satellite Selection ===")
        
        test_timeslot = 100
        
        # Create test task at a known ground station location
        # Use location_id that exists in ground stations
        test_task = self.create_test_task(1, location_id=1)
        
        # Find nearest satellite
        satellite_id, distance = self.distributor.find_nearest_visible_satellite(
            test_task, test_timeslot
        )
        
        if satellite_id is not None:
            print(f"  Timeslot {test_timeslot}:")
            print(f"    Task location: {test_task.coordinates}")
            print(f"    Nearest satellite: {satellite_id}")
            print(f"    Distance: {distance:.2f} km")
            
            # Verify that this is indeed the nearest satellite
            satellites = self.orbital_updater.get_satellites_at_time(test_timeslot)
            visibility_matrix, distance_matrix = \
                self.orbital_updater.build_satellite_ground_visibility_matrix(
                    satellites, test_timeslot
                )
            
            ground_idx = self.distributor.location_to_ground_idx.get(test_task.location_id)
            if ground_idx is not None:
                visible_satellites = np.where(visibility_matrix[:, ground_idx])[0]
                if len(visible_satellites) > 0:
                    min_distance = np.min(distance_matrix[visible_satellites, ground_idx])
                    assert abs(distance - min_distance) < 0.01, "Not the nearest satellite"
                    print("    ✓ Verified as nearest satellite")
        else:
            print(f"  No visible satellites at timeslot {test_timeslot}")
        
        print("  ✓ Nearest satellite selection tested")
    
    def test_task_assignment(self):
        """Test basic task assignment functionality"""
        print("\n=== Testing Task Assignment ===")
        
        for timeslot in self.test_timeslots[:3]:  # Test first 3 timeslots
            print(f"\n  --- Timeslot {timeslot} ---")
            
            # Create test task
            task = self.create_test_task(
                task_id=1000 + timeslot,
                location_id=1
            )
            
            # Assign task
            assignment = self.distributor.assign_task_to_satellite(task, timeslot)
            
            print(f"    Task {task.task_id}:")
            print(f"      Status: {assignment.status.value}")
            
            if assignment.status == AssignmentStatus.ASSIGNED:
                print(f"      Assigned to satellite: {assignment.assigned_satellite_id}")
                print(f"      Distance: {assignment.distance_km:.2f} km")
                assert assignment.assigned_satellite_id is not None, "No satellite ID"
                assert assignment.distance_km > 0, "Invalid distance"
            elif assignment.status == AssignmentStatus.RETRYING:
                print(f"      Retry count: {assignment.retry_count}")
                print(f"      Scheduled for retry")
            elif assignment.status == AssignmentStatus.FAILED:
                print(f"      Failed: {assignment.failure_reason}")
            
            # Verify assignment is tracked
            tracked = self.distributor.get_assignment(task.task_id)
            assert tracked is not None, "Assignment not tracked"
            assert tracked.task_id == task.task_id, "Task ID mismatch"
        
        print("\n  ✓ Task assignment functionality verified")
    
    def test_retry_mechanism(self):
        """Test retry mechanism for failed assignments"""
        print("\n=== Testing Retry Mechanism ===")
        
        # Create a task that might fail initially
        task = self.create_test_task(
            task_id=2000,
            location_id=100,  # Use a location that might not have visibility
            lat=85.0,  # Near north pole - limited satellite coverage
            lon=0.0
        )
        
        max_attempts = 3  # Initial attempt + 2 retries
        timeslot = 1
        
        for attempt in range(max_attempts + 1):
            print(f"\n  Attempt {attempt + 1} at timeslot {timeslot}:")
            
            if attempt == 0:
                # Initial assignment
                assignment = self.distributor.assign_task_to_satellite(task, timeslot)
            else:
                # Process retry queue
                retry_results = self.distributor.process_retry_queue(timeslot)
                if retry_results:
                    assignment = retry_results[0]
                else:
                    break
            
            print(f"    Status: {assignment.status.value}")
            print(f"    Retry count: {assignment.retry_count}")
            
            if assignment.status == AssignmentStatus.ASSIGNED:
                print(f"    Success! Assigned to satellite {assignment.assigned_satellite_id}")
                break
            elif assignment.status == AssignmentStatus.FAILED:
                print(f"    Failed: {assignment.failure_reason}")
                assert assignment.retry_count <= self.distributor.max_retries, "Too many retries"
                break
            
            timeslot += 1
        
        # Verify retry count doesn't exceed max
        final_assignment = self.distributor.get_assignment(task.task_id)
        assert final_assignment.retry_count <= self.distributor.max_retries, \
            f"Retry count {final_assignment.retry_count} exceeds max {self.distributor.max_retries}"
        
        print("\n  ✓ Retry mechanism verified")
    
    def test_batch_distribution(self):
        """Test distributing multiple tasks in a batch"""
        print("\n=== Testing Batch Distribution ===")
        
        timeslot = 50
        
        # Create multiple test tasks from different locations
        tasks = [
            self.create_test_task(3001, location_id=1, lat=40.7, lon=-74.0),   # New York
            self.create_test_task(3002, location_id=2, lat=51.5, lon=-0.1),    # London
            self.create_test_task(3003, location_id=3, lat=35.7, lon=139.7),   # Tokyo
            self.create_test_task(3004, location_id=4, lat=-33.9, lon=18.4),   # Cape Town
            self.create_test_task(3005, location_id=5, lat=-23.5, lon=-46.6),  # São Paulo
        ]
        
        # Distribute all tasks
        assignments = self.distributor.distribute_tasks(tasks, timeslot)
        
        print(f"\n  Distributed {len(tasks)} tasks at timeslot {timeslot}:")
        
        assigned_count = 0
        retrying_count = 0
        failed_count = 0
        
        for assignment in assignments:
            if assignment.status == AssignmentStatus.ASSIGNED:
                assigned_count += 1
                print(f"    Task {assignment.task_id}: Assigned to satellite {assignment.assigned_satellite_id}")
            elif assignment.status == AssignmentStatus.RETRYING:
                retrying_count += 1
                print(f"    Task {assignment.task_id}: Retrying (attempt {assignment.retry_count})")
            elif assignment.status == AssignmentStatus.FAILED:
                failed_count += 1
                print(f"    Task {assignment.task_id}: Failed - {assignment.failure_reason}")
        
        print(f"\n  Summary:")
        print(f"    Assigned: {assigned_count}")
        print(f"    Retrying: {retrying_count}")
        print(f"    Failed: {failed_count}")
        
        assert len(assignments) == len(tasks), "Not all tasks processed"
        print("\n  ✓ Batch distribution verified")
    
    def test_timeslot_simulation(self):
        """Test complete timeslot simulation with retries"""
        print("\n=== Testing Timeslot Simulation ===")
        
        # Reset distributor
        self.distributor.reset()
        
        # Simulate multiple timeslots
        for timeslot in range(1, 6):
            print(f"\n  --- Timeslot {timeslot} ---")
            
            # Generate new tasks for this timeslot
            new_tasks = []
            for i in range(3):
                task = self.create_test_task(
                    task_id=4000 + timeslot * 10 + i,
                    location_id=(i % 5) + 1
                )
                new_tasks.append(task)
            
            # Simulate timeslot
            result = self.distributor.simulate_timeslot(new_tasks, timeslot)
            
            print(f"    New tasks: {result['new_tasks']}")
            print(f"    Retry tasks: {result['retry_tasks']}")
            print(f"    Total processed: {result['total_processed']}")
            print(f"    Assigned: {result['assigned']}")
            print(f"    Retrying: {result['retrying']}")
            print(f"    Failed: {result['failed']}")
            print(f"    Retry queue size: {result['retry_queue_size']}")
            print(f"    Success rate: {result['success_rate']:.2%}")
        
        print("\n  ✓ Timeslot simulation verified")
    
    def test_statistics_collection(self):
        """Test statistics collection and reporting"""
        print("\n=== Testing Statistics Collection ===")
        
        # Reset and run some distributions
        self.distributor.reset()
        
        total_tasks = 0
        for timeslot in range(10):
            # Create tasks
            tasks = [
                self.create_test_task(
                    task_id=5000 + timeslot * 10 + i,
                    location_id=(i % 10) + 1
                )
                for i in range(5)
            ]
            total_tasks += len(tasks)
            
            # Distribute
            self.distributor.simulate_timeslot(tasks, timeslot)
        
        # Get statistics
        stats = self.distributor.get_statistics()
        
        print("\n  Distribution Statistics:")
        print(f"    Total tasks: {stats['total_tasks']}")
        print(f"    Assigned tasks: {stats['assigned_tasks']}")
        print(f"    Failed tasks: {stats['failed_tasks']}")
        print(f"    Retried tasks: {stats['retried_tasks']}")
        print(f"    Success rate: {stats['assignment_success_rate']:.2%}")
        
        if 'avg_assignment_distance' in stats and stats['avg_assignment_distance'] > 0:
            print(f"    Average assignment distance: {stats['avg_assignment_distance']:.2f} km")
        
        if 'avg_retry_count' in stats:
            print(f"    Average retry count: {stats['avg_retry_count']:.2f}")
        
        # Verify statistics
        assert stats['total_tasks'] == total_tasks, "Total tasks mismatch"
        assert stats['assigned_tasks'] >= 0, "Invalid assigned count"
        assert stats['failed_tasks'] >= 0, "Invalid failed count"
        assert 0 <= stats['assignment_success_rate'] <= 1, "Invalid success rate"
        
        print("\n  ✓ Statistics collection verified")
    
    def test_deadline_enforcement(self):
        """Test that tasks fail when deadline is exceeded"""
        print("\n=== Testing Deadline Enforcement ===")
        
        current_time = 100.0
        
        # Create task with very short deadline
        task = self.create_test_task(
            task_id=6000,
            location_id=1
        )
        task.deadline_timestamp = current_time + 10.0  # Deadline in 10 seconds
        
        # Initial assignment at time 100
        timeslot = int(current_time / self.distributor.timeslot_duration)
        assignment = self.distributor.assign_task_to_satellite(task, timeslot)
        
        if assignment.status == AssignmentStatus.RETRYING:
            print(f"  Task scheduled for retry, deadline at {task.deadline_timestamp}")
            
            # Advance time past deadline
            future_timeslot = int((current_time + 15.0) / self.distributor.timeslot_duration)
            
            # Process retry after deadline
            retry_results = self.distributor.process_retry_queue(future_timeslot)
            
            if retry_results:
                final_assignment = retry_results[0]
                print(f"  Final status: {final_assignment.status.value}")
                print(f"  Failure reason: {final_assignment.failure_reason}")
                
                assert final_assignment.status == AssignmentStatus.FAILED, "Should fail after deadline"
                assert "DEADLINE" in final_assignment.failure_reason, "Should fail due to deadline"
        
        print("\n  ✓ Deadline enforcement verified")
    
    def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("TASK DISTRIBUTOR TEST SUITE")
        print("=" * 60)
        
        try:
            self.test_nearest_satellite_selection()
            self.test_task_assignment()
            self.test_retry_mechanism()
            self.test_batch_distribution()
            self.test_timeslot_simulation()
            self.test_statistics_collection()
            self.test_deadline_enforcement()
            
            print("\n" + "=" * 60)
            print("ALL TESTS PASSED ✓")
            print("=" * 60)
            
        except AssertionError as e:
            print(f"\n❌ TEST FAILED: {e}")
            raise
        except Exception as e:
            print(f"\n❌ UNEXPECTED ERROR: {e}")
            raise


if __name__ == "__main__":
    tester = TestTaskDistributor()
    tester.run_all_tests()