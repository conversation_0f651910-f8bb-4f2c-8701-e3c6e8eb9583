"""
Satellite compute resource management module
Implements DPSQ (Dynamic Priority Score Queuing) scheduling algorithm
Focuses on compute resource management without task_tracking dependency
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import heapq
import logging
import yaml
from pathlib import Path

# Absolute imports as per coding standard
from src.env.satellite_cloud.compute_models import (
    ComputeTask, TaskStatus, ProcessingNode, NodeType, SatelliteState, ProcessingResult
)
from src.env.physics_layer.task_models import Task
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.Foundation_Layer.logging_config import get_logger

# GPU acceleration imports (optional)
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False


class DPSQScheduler:
    """Dynamic Priority Score Queuing scheduler with optional GPU acceleration"""

    def __init__(self, config: Dict, enable_gpu: bool = None, device: str = None):
        """
        Initialize scheduler with configuration parameters

        Args:
            config: Configuration dictionary from config.yaml
            enable_gpu: Whether to enable GPU acceleration (None for auto-detect)
            device: Specific device to use ('cuda', 'cpu', or None for auto)
        """
        # Extract queuing parameters - no default values as per coding standard
        queuing_config = config['queuing']
        self.w_priority = queuing_config['w_priority']
        self.w_urgency = queuing_config['w_urgency']
        self.w_cost = queuing_config['w_cost']
        self.epsilon_urgency = queuing_config['epsilon_urgency']
        self.max_queue_size = queuing_config['max_queue_size']

        # Extract computation parameters - no default values as per coding standard
        computation_config = config['computation']
        self.f_sat = float(computation_config['f_leo_hz'])
        self.zeta_leo = float(computation_config['zeta_leo'])
        self.processing_overhead = computation_config['processing_overhead_ratio']

        # Extract communication parameters - no default values as per coding standard
        comm_config = config['communication']
        self.default_bandwidth = comm_config['b_us_hz'] / 8e6  # Convert to MB/s

        # GPU acceleration setup
        self.gpu_enabled = False
        self.device = None
        self.gpu_stats = {
            'batch_sizes': [],
            'computation_times': [],
            'transfer_times': []
        }

        # Check GPU configuration
        gpu_config = config.get('gpu', {})
        if enable_gpu is None:
            enable_gpu = gpu_config.get('enable_gpu_acceleration', True)

        if enable_gpu and TORCH_AVAILABLE:
            self._setup_gpu_acceleration(device or gpu_config.get('device'))

        self.logger = get_logger(__name__)
        if self.gpu_enabled:
            self.logger.info(f"DPSQScheduler initialized with GPU acceleration on {self.device}")
        else:
            self.logger.info("DPSQScheduler initialized with CPU-only computation")

    def _setup_gpu_acceleration(self, device: str = None):
        """Setup GPU acceleration if available"""
        try:
            if device is None:
                self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            else:
                self.device = torch.device(device)

            # Only enable GPU if CUDA is actually available
            if self.device.type == 'cuda' and torch.cuda.is_available():
                self.gpu_enabled = True
                self._precompute_gpu_constants()
            else:
                self.gpu_enabled = False
                self.device = torch.device('cpu')
        except Exception as e:
            self.logger.warning(f"Failed to setup GPU acceleration: {e}")
            self.gpu_enabled = False
            self.device = None

    def _precompute_gpu_constants(self):
        """Precompute constants as GPU tensors"""
        if not self.gpu_enabled:
            return

        try:
            # Weight parameters
            self.w_priority_tensor = torch.tensor(self.w_priority, dtype=torch.float32, device=self.device)
            self.w_urgency_tensor = torch.tensor(self.w_urgency, dtype=torch.float32, device=self.device)
            self.w_cost_tensor = torch.tensor(self.w_cost, dtype=torch.float32, device=self.device)

            # Other constants
            self.epsilon_tensor = torch.tensor(self.epsilon_urgency, dtype=torch.float32, device=self.device)
            self.f_sat_tensor = torch.tensor(self.f_sat, dtype=torch.float32, device=self.device)
            self.bandwidth_tensor = torch.tensor(self.default_bandwidth, dtype=torch.float32, device=self.device)
        except Exception as e:
            self.logger.warning(f"Failed to precompute GPU constants: {e}")
            self.gpu_enabled = False

    def calculate_priority_score(self, task: ComputeTask, current_time: float,
                                bandwidth: Optional[float] = None) -> float:
        """
        Calculate dynamic priority score for task
        
        Score(T_i, t_now) = w_p * f_p(P_i) + w_d * f_d(D_i, t_now) - w_c * f_c(S_i, C_i)
        
        Args:
            task: Task object
            current_time: Current time (seconds)
            bandwidth: Available bandwidth (MB/s)
            
        Returns:
            Dynamic priority score
        """
        if bandwidth is None or bandwidth <= 0:
            bandwidth = self.default_bandwidth
            
        # Priority factor: f_p(P_i) = 4 - P_i (so priority 1 gets highest score)
        # Priority 1 (high) -> score 3, Priority 2 (medium) -> score 2, Priority 3 (low) -> score 1
        f_priority = 4 - task.priority
        
        # Urgency factor: f_d(D_i, t_now) = 1 / ((D_i - t_now) + epsilon)
        time_remaining = max(task.deadline - current_time, 0)
        f_urgency = 1.0 / (time_remaining + self.epsilon_urgency)
        
        # Cost factor: f_c(S_i, C_i) = T_proc,i = S_i/B_link + C_i/F_sat
        if bandwidth > 0:
            communication_time = task.data_size_mb / bandwidth
        else:
            communication_time = float('inf')
        computation_time = task.complexity / self.f_sat
        f_cost = communication_time + computation_time
        
        # Calculate total score
        score = (self.w_priority * f_priority + 
                self.w_urgency * f_urgency - 
                self.w_cost * f_cost)
        
        return score

    def calculate_priority_scores_batch(self, tasks: List[ComputeTask],
                                      current_time: float,
                                      bandwidth: Optional[float] = None) -> np.ndarray:
        """
        GPU批量计算任务优先级分数（如果GPU可用），否则使用CPU

        Args:
            tasks: 任务列表
            current_time: 当前时间
            bandwidth: 可用带宽

        Returns:
            优先级分数数组
        """
        if not tasks:
            return np.array([])

        n_tasks = len(tasks)

        # 如果GPU不可用或任务数太少，使用CPU计算
        if not self.gpu_enabled or n_tasks < 10:
            return np.array([self.calculate_priority_score(task, current_time, bandwidth)
                           for task in tasks])

        try:
            import time
            transfer_start = time.time()

            # 准备数据并传输到GPU
            priorities = torch.tensor([task.priority for task in tasks],
                                     dtype=torch.float32, device=self.device)
            deadlines = torch.tensor([task.deadline for task in tasks],
                                    dtype=torch.float32, device=self.device)
            data_sizes = torch.tensor([task.data_size_mb for task in tasks],
                                     dtype=torch.float32, device=self.device)
            complexities = torch.tensor([task.complexity for task in tasks],
                                       dtype=torch.float32, device=self.device)

            current_time_tensor = torch.tensor(current_time, dtype=torch.float32, device=self.device)

            if bandwidth is not None and bandwidth > 0:
                bandwidth_tensor = torch.tensor(bandwidth, dtype=torch.float32, device=self.device)
            else:
                bandwidth_tensor = self.bandwidth_tensor

            transfer_time = time.time() - transfer_start
            compute_start = time.time()

            # GPU批量计算
            # 优先级因子: f_p(P_i) = 4 - P_i
            f_priority = 4.0 - priorities

            # 紧急度因子: f_d(D_i, t_now) = 1 / ((D_i - t_now) + epsilon)
            time_remaining = torch.maximum(deadlines - current_time_tensor,
                                          torch.zeros_like(deadlines))
            f_urgency = 1.0 / (time_remaining + self.epsilon_tensor)

            # 成本因子: f_c(S_i, C_i) = S_i/B + C_i/F
            communication_time = data_sizes / bandwidth_tensor
            computation_time = complexities / self.f_sat_tensor
            f_cost = communication_time + computation_time

            # 总分数
            scores = (self.w_priority_tensor * f_priority +
                     self.w_urgency_tensor * f_urgency -
                     self.w_cost_tensor * f_cost)

            compute_time = time.time() - compute_start

            # 记录统计
            self.gpu_stats['batch_sizes'].append(n_tasks)
            self.gpu_stats['transfer_times'].append(transfer_time)
            self.gpu_stats['computation_times'].append(compute_time)

            # 转换回CPU/NumPy
            return scores.cpu().numpy()

        except Exception as e:
            self.logger.warning(f"GPU batch computation failed, falling back to CPU: {e}")
            # 回退到CPU计算
            return np.array([self.calculate_priority_score(task, current_time, bandwidth)
                           for task in tasks])

    def estimate_processing_time(self, task: ComputeTask,
                                cpu_allocation: float = 1.0,
                                bandwidth: Optional[float] = None) -> Tuple[float, float, float]:
        """
        Estimate task processing time
        
        Args:
            task: Task object
            cpu_allocation: CPU allocation ratio (0-1)
            bandwidth: Available bandwidth (MB/s)
            
        Returns:
            (total_time, communication_time, computation_time) in seconds
        """
        if bandwidth is None or bandwidth <= 0:
            bandwidth = self.default_bandwidth
            
        # Communication delay
        if bandwidth > 0:
            communication_time = task.data_size_mb / bandwidth
        else:
            communication_time = float('inf')
        
        # Computation delay (considering CPU allocation)
        effective_cpu = self.f_sat * cpu_allocation
        computation_time = task.complexity / effective_cpu
        
        # Consider processing overhead
        total_time = (communication_time + computation_time) * (1 + self.processing_overhead)
        
        return total_time, communication_time, computation_time
    
    def check_feasibility(self, task: ComputeTask, current_time: float,
                         cpu_allocation: float = 1.0,
                         bandwidth: Optional[float] = None) -> bool:
        """
        Check if task can be completed before deadline
        
        Args:
            task: Task object
            current_time: Current time
            cpu_allocation: CPU allocation ratio
            bandwidth: Available bandwidth
            
        Returns:
            True if feasible, False otherwise
        """
        total_time, _, _ = self.estimate_processing_time(task, cpu_allocation, bandwidth)
        estimated_completion = current_time + total_time
        
        return estimated_completion <= task.deadline

    def get_gpu_performance_stats(self) -> Dict[str, float]:
        """获取GPU性能统计"""
        stats = {}

        if self.gpu_stats['batch_sizes']:
            stats['avg_batch_size'] = np.mean(self.gpu_stats['batch_sizes'])
            stats['total_batches'] = len(self.gpu_stats['batch_sizes'])

        if self.gpu_stats['transfer_times']:
            stats['avg_transfer_time'] = np.mean(self.gpu_stats['transfer_times'])
            stats['total_transfer_time'] = np.sum(self.gpu_stats['transfer_times'])

        if self.gpu_stats['computation_times']:
            stats['avg_computation_time'] = np.mean(self.gpu_stats['computation_times'])
            stats['total_computation_time'] = np.sum(self.gpu_stats['computation_times'])

        # 计算加速比
        if self.gpu_stats['computation_times'] and self.gpu_stats['batch_sizes']:
            avg_batch = np.mean(self.gpu_stats['batch_sizes'])
            avg_gpu_time = np.mean(self.gpu_stats['computation_times'])
            # 估算CPU时间（假设每个任务0.001秒）
            estimated_cpu_time = avg_batch * 0.001
            stats['estimated_speedup'] = estimated_cpu_time / avg_gpu_time if avg_gpu_time > 0 else 1.0

        stats['gpu_enabled'] = self.gpu_enabled
        stats['device'] = str(self.device) if self.device else 'None'

        return stats


class SatelliteCompute:
    """Satellite compute resource management class"""
    
    def __init__(self, satellite_id: int, config_path: str = None,
                 config: Dict = None,
                 orbital_updater: Optional[OrbitalUpdater] = None,
                 comm_manager: Optional[CommunicationManager] = None,
                 enable_gpu: bool = None,
                 device: str = None):
        """
        Initialize satellite compute manager

        Args:
            satellite_id: Satellite ID
            config_path: Path to configuration file
            config: Configuration dictionary (if config_path not provided)
            orbital_updater: Orbital updater instance
            comm_manager: Communication manager instance
            enable_gpu: Whether to enable GPU acceleration (None for auto-detect)
            device: Specific device to use ('cuda', 'cpu', or None for auto)
        """
        self.satellite_id = satellite_id
        
        # Load configuration - must be provided via parameter as per coding standard
        if config is not None:
            self.config = config
        elif config_path is not None:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            raise ValueError("Either config_path or config must be provided")
        
        # Initialize external managers
        self.orbital_updater = orbital_updater
        self.comm_manager = comm_manager
        
        # Initialize scheduler with GPU support
        self.scheduler = DPSQScheduler(self.config, enable_gpu=enable_gpu, device=device)
        
        # Task queues and state
        self.task_queue: List[ComputeTask] = []  # Waiting queue
        self.processing_tasks: List[ComputeTask] = []  # Tasks currently being processed
        self.completed_tasks: List[ComputeTask] = []
        self.dropped_tasks: List[ComputeTask] = []
        
        # Resource state - no default values as per coding standard
        computation_config = self.config['computation']
        self.cpu_frequency = float(computation_config['f_leo_hz'])
        self.max_battery = float(computation_config['leo_battery_capacity_j'])
        self.battery_energy = self.max_battery
        self.solar_power = float(computation_config['leo_solar_power_w'])
        self.energy_threshold = computation_config['energy_threshold_ratio']
        
        # Processing state
        self.is_processing = False
        self.available_cycles = 0.0  # Available CPU cycles for current timeslot
        self.processing_start_time = 0.0
        self.current_time = 0.0  # Current simulation time
        
        # Resource allocation strategy (set by algorithm layer)
        self.cpu_allocation_strategy = 'uniform'  # Default: uniform allocation
        self.cpu_allocation_params = {}
        
        # Communication state - no default values as per coding standard
        comm_config = self.config['communication']
        self.current_bandwidth = comm_config['b_us_hz'] / 8e6  # MB/s
        
        # Performance tracking
        self.total_energy_consumed = 0.0
        self.total_tasks_processed = 0
        self.total_tasks_dropped = 0
        self.total_penalty = 0.0
        
        # Caching for performance
        self.priority_cache: Dict[str, float] = {}
        
        self.logger = get_logger(f"SatelliteCompute_{satellite_id}")
        
    @handle_errors(module="satellite_compute", function="add_task")
    def add_task(self, task: Task) -> bool:
        """
        Add task to processing queue
        
        Args:
            task: Task object from task_models
            
        Returns:
            True if successfully added, False if rejected
        """
        # Check queue capacity
        if len(self.task_queue) >= self.scheduler.max_queue_size:
            self.logger.warning(f"Queue full, rejecting task {task.task_id}")
            return False
        
        # Use task's arrival_time if available, otherwise use current_time
        arrival_time = getattr(task, 'arrival_time', self.current_time)
        
        # Convert Task to ComputeTask
        compute_task = ComputeTask.from_task(
            task, 
            arrival_time=arrival_time,
            drop_penalty=self.config['computation']['default_drop_penalty']
        )
        
        # Check energy feasibility
        if not self.can_accept_task(compute_task):
            self.logger.warning(f"Insufficient energy for task {task.task_id}")
            return False
        
        self.task_queue.append(compute_task)
        self.logger.info(f"Added task {task.task_id} to queue (queue size: {len(self.task_queue)})")
        return True
    
    def can_accept_task(self, task: ComputeTask) -> bool:
        """
        Check if satellite can accept task (energy constraint)
        
        Args:
            task: ComputeTask object
            
        Returns:
            True if can accept, False otherwise
        """
        # Calculate required energy
        required_energy = self.scheduler.zeta_leo * task.complexity
        
        # Check against energy threshold
        min_energy = self.max_battery * self.energy_threshold
        available_energy = self.battery_energy - min_energy
        
        return available_energy >= required_energy
    
    @handle_errors(module="satellite_compute", function="process_timeslot")
    def process_timeslot(self, duration: float, illuminated: bool) -> ProcessingResult:
        """
        Process tasks for one timeslot - supports parallel processing
        
        Args:
            duration: Timeslot duration in seconds
            illuminated: Whether satellite is in sunlight
            
        Returns:
            ProcessingResult with completed tasks and metrics
        """
        result = ProcessingResult()
        
        # Update energy state
        self._update_energy(duration, illuminated)
        
        # Calculate available CPU cycles for this timeslot
        self.available_cycles = self.cpu_frequency * duration
        
        # Process all tasks in parallel based on available cycles
        completed_tasks = self._process_tasks_parallel(duration)
        for task in completed_tasks:
            result.completed_tasks.append(task)
            self.completed_tasks.append(task)
            self.total_tasks_processed += 1
        
        # Schedule new tasks from queue to fill available capacity
        self._schedule_tasks_for_processing()
        
        # Check timeouts and drop expired tasks
        self._check_timeouts()
        
        # Calculate metrics
        result.energy_consumed = self.total_energy_consumed
        result.cpu_utilization = len(self.processing_tasks) / max(1, self.scheduler.max_queue_size)
        result.queue_wait_time = self._calculate_average_wait_time()
        
        return result
    
    def _update_energy(self, duration: float, illuminated: bool):
        """Update battery energy state"""
        # Solar charging if illuminated
        if illuminated:
            charged_energy = self.solar_power * duration
            self.battery_energy = min(self.battery_energy + charged_energy, self.max_battery)
        
        # Energy consumption from processing multiple tasks
        if self.processing_tasks:
            # Vectorized energy calculation using NumPy
            num_tasks = len(self.processing_tasks)
            if num_tasks > 0:
                # Extract remaining complexities into NumPy array
                complexities = np.array([task.remaining_complexity for task in self.processing_tasks])
                
                # Calculate cycles per task
                cycles_per_task = self.cpu_frequency * duration / num_tasks
                
                # Calculate actual cycles processed (vectorized min operation)
                cycles_processed = np.minimum(complexities, cycles_per_task)
                
                # Total energy consumed
                total_cycles_processed = np.sum(cycles_processed)
                consumed_energy = total_cycles_processed * self.scheduler.zeta_leo
                
                self.battery_energy = max(0, self.battery_energy - consumed_energy)
                self.total_energy_consumed += consumed_energy
    
    def _process_tasks_parallel(self, duration: float) -> List[ComputeTask]:
        """
        Process multiple tasks in parallel based on available CPU cycles
        
        Args:
            duration: Processing duration in seconds
            
        Returns:
            List of completed tasks
        """
        if not self.processing_tasks:
            return []
        
        completed_tasks = []
        total_cycles_available = self.cpu_frequency * duration
        
        # Use cached priority scores if available, only recalculate if not cached
        # Priority scores are calculated once when tasks enter processing
        self.processing_tasks.sort(key=lambda x: getattr(x, 'priority_score', 0), reverse=True)
        
        # Vectorized processing using NumPy for better performance
        num_tasks = len(self.processing_tasks)
        if num_tasks > 0:
            # Extract task complexities into NumPy array for vectorized operations
            remaining_complexities = np.array([task.remaining_complexity for task in self.processing_tasks])
            
            # Calculate cycle allocations for all tasks at once
            cycles_per_task = total_cycles_available / num_tasks  # Simple uniform allocation for parallel tasks
            cycles_allocated = np.minimum(remaining_complexities, cycles_per_task)
            
            # Update all complexities at once
            remaining_complexities -= cycles_allocated
            
            # Update task properties using vectorized results
            for i, task in enumerate(self.processing_tasks):
                task.remaining_complexity = remaining_complexities[i]
                
                # Update task timing
                task.accumulated_processing_time += duration * (cycles_allocated[i] / total_cycles_available)
                task.accumulated_energy += cycles_allocated[i] * self.scheduler.zeta_leo
                
                # Check if completed
                if task.remaining_complexity <= 0:
                    task.status = TaskStatus.COMPLETED
                    task.processing_progress = 1.0
                    task.completion_time = self.current_time + duration
                    
                    # Calculate complete delay and energy in environment
                    self._calculate_task_complete_metrics(task)
                    
                    completed_tasks.append(task)
        
        # Remove completed tasks from processing list
        if completed_tasks:
            # Use set of task IDs for O(1) lookup instead of repeated list operations
            completed_ids = {task.task_id for task in completed_tasks}
            self.processing_tasks = [t for t in self.processing_tasks if t.task_id not in completed_ids]
        
        return completed_tasks
    
    def _schedule_tasks_for_processing(self):
        """
        Schedule multiple tasks from queue for parallel processing
        """
        if not self.task_queue:
            return
        
        # Calculate how many tasks we can handle based on CPU capacity
        # Estimate based on average task complexity
        max_parallel = min(len(self.task_queue), 
                          self.config['computation'].get('max_parallel_tasks', 200))
        
        # Move tasks from queue to processing based on priority
        current_time = self.current_time
        tasks_to_schedule = []
        
        # Use batch computation for better performance
        candidate_tasks = self.task_queue[:max_parallel - len(self.processing_tasks)]

        if candidate_tasks:
            # Batch calculate priority scores
            scores = self.scheduler.calculate_priority_scores_batch(
                candidate_tasks, current_time, self.current_bandwidth
            )

            # Check feasibility and prepare for scheduling
            for task, score in zip(candidate_tasks, scores):
                # Cache the score in the task
                task.priority_score = score
                task.last_priority_update = current_time

                # Check feasibility
                if self.scheduler.check_feasibility(task, current_time, 1.0, self.current_bandwidth):
                    tasks_to_schedule.append((score, task))
        
        # Sort by score and schedule
        tasks_to_schedule.sort(key=lambda x: x[0], reverse=True)
        
        for _, task in tasks_to_schedule:
            if len(self.processing_tasks) >= max_parallel:
                break
                
            # Move from queue to processing
            self.task_queue.remove(task)
            self.processing_tasks.append(task)
            
            # Initialize task for processing
            task.status = TaskStatus.PROCESSING
            task.start_time = current_time
            task.processing_start_time = current_time  # Record when processing actually starts
            task.allocated_cpu = 1.0 / (len(self.processing_tasks) + 1)  # Share CPU
            
            self.logger.debug(f"Scheduled task {task.task_id} for parallel processing")
    
    def _process_current_task(self, duration: float) -> Optional[ComputeTask]:
        """
        Process current task for given duration
        
        Args:
            duration: Processing duration in seconds
            
        Returns:
            Completed task if finished, None otherwise
        """
        if not self.current_task:
            return None
        
        # Calculate processing cycles for this duration
        cycles_available = self.cpu_frequency * duration
        cycles_needed = self.current_task.remaining_complexity
        
        # Process (limited by remaining complexity)
        cycles_processed = min(cycles_available, cycles_needed)
        self.current_task.remaining_complexity -= cycles_processed
        
        # Update progress
        if self.current_task.complexity > 0:
            self.processing_progress = 1.0 - (self.current_task.remaining_complexity / self.current_task.complexity)
        else:
            self.processing_progress = 1.0
        
        # Update task timing
        self.current_task.accumulated_processing_time += duration
        
        # Check if completed
        if self.current_task.remaining_complexity <= 0:
            self.current_task.status = TaskStatus.COMPLETED
            self.current_task.processing_progress = 1.0
            self.current_task.energy_consumed = self.current_task.accumulated_energy
            return self.current_task
        
        return None
    
    def _schedule_next_task(self) -> Optional[ComputeTask]:
        """
        Schedule next task using DPSQ algorithm
        
        Returns:
            Selected task or None if no suitable task
        """
        if not self.task_queue:
            return None
        
        # Use batch computation for better performance
        current_time = self.current_time  # Use instance variable
        task_scores = []

        # Batch calculate priority scores
        scores = self.scheduler.calculate_priority_scores_batch(
            self.task_queue, current_time, self.current_bandwidth
        )

        # Check feasibility for each task
        for task, score in zip(self.task_queue, scores):
            if self.scheduler.check_feasibility(task, current_time, 1.0, self.current_bandwidth):
                task_scores.append((score, task))
        
        if not task_scores:
            # No feasible tasks, drop the oldest expired task
            for task in self.task_queue[:]:
                if task.deadline <= current_time:
                    self._drop_task(task, "timeout")
            return None
        
        # Select task with highest score
        task_scores.sort(key=lambda x: x[0], reverse=True)
        _, selected_task = task_scores[0]
        
        # Remove from queue
        self.task_queue.remove(selected_task)
        
        # Initialize task for processing
        selected_task.status = TaskStatus.PROCESSING
        selected_task.start_time = current_time
        selected_task.allocated_cpu = 1.0  # Full CPU allocation (atomic processing)
        
        # Estimate completion time
        total_time, comm_time, comp_time = self.scheduler.estimate_processing_time(
            selected_task, 1.0, self.current_bandwidth
        )
        selected_task.processing_delay = comp_time
        selected_task.communication_delay = comm_time
        selected_task.total_delay = total_time
        selected_task.completion_time = current_time + total_time
        
        self.logger.info(f"Scheduled task {selected_task.task_id} for processing")
        return selected_task
    
    def _check_timeouts(self):
        """Check and drop timeout tasks"""
        current_time = self.current_time  # Use instance variable
        timeout_tasks = [task for task in self.task_queue if task.deadline <= current_time]
        
        for task in timeout_tasks:
            self._drop_task(task, "timeout")
    
    def _drop_task(self, task: ComputeTask, reason: str):
        """Drop task and update statistics"""
        if task in self.task_queue:
            self.task_queue.remove(task)
        
        task.status = TaskStatus.DROPPED
        self.dropped_tasks.append(task)
        self.total_tasks_dropped += 1
        self.total_penalty += task.drop_penalty
        
        self.logger.info(f"Dropped task {task.task_id}, reason: {reason}")
    
    def _calculate_average_wait_time(self) -> float:
        """Calculate average queue waiting time"""
        if not self.task_queue:
            return 0.0
        
        current_time = self.current_time  # Use instance variable
        # Vectorized calculation using NumPy
        arrival_times = np.array([task.arrival_time for task in self.task_queue])
        wait_times = current_time - arrival_times
        return np.mean(wait_times)
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        return {
            'queue_length': len(self.task_queue),
            'processing_count': len(self.processing_tasks),
            'is_processing': len(self.processing_tasks) > 0,
            'processing_task_ids': [t.task_id for t in self.processing_tasks],
            'battery_energy': self.battery_energy,
            'energy_percentage': (self.battery_energy / self.max_battery) * 100,
            'cpu_utilization': len(self.processing_tasks) / max(1, self.config['computation'].get('max_parallel_tasks', 200))
        }
    
    def get_energy_status(self) -> Dict[str, Any]:
        """Get energy status"""
        return {
            'battery_energy': self.battery_energy,
            'max_battery': self.max_battery,
            'energy_percentage': (self.battery_energy / self.max_battery) * 100,
            'total_energy_consumed': self.total_energy_consumed,
            'energy_threshold': self.max_battery * self.energy_threshold,
            'can_accept_tasks': self.battery_energy > (self.max_battery * self.energy_threshold)
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics"""
        return {
            'satellite_id': self.satellite_id,
            'total_tasks_processed': self.total_tasks_processed,
            'total_tasks_dropped': self.total_tasks_dropped,
            'total_penalty': self.total_penalty,
            'total_energy_consumed': self.total_energy_consumed,
            'queue_length': len(self.task_queue),
            'processing_count': len(self.processing_tasks),
            'is_processing': len(self.processing_tasks) > 0,
            'completed_tasks': len(self.completed_tasks),
            'dropped_tasks': len(self.dropped_tasks),
            'battery_energy': self.battery_energy,
            'current_bandwidth': self.current_bandwidth,
            'cpu_frequency': self.cpu_frequency
        }
    
    def set_cpu_allocation_strategy(self, strategy: str = 'uniform', params: Dict = None):
        """
        Set CPU allocation strategy (called by algorithm layer)
        
        Args:
            strategy: Allocation strategy ('uniform', 'priority', 'custom')
            params: Strategy-specific parameters
        """
        self.cpu_allocation_strategy = strategy
        self.cpu_allocation_params = params or {}
        self.logger.debug(f"Set CPU allocation strategy to {strategy}")
    
    def _calculate_task_complete_metrics(self, task: ComputeTask):
        """
        Calculate complete delay and energy metrics for a completed task
        This is done in the environment layer, not by the algorithm
        
        Args:
            task: Completed task
        """
        # Get communication parameters
        comm_config = self.config['communication']
        comp_config = self.config['computation']
        
        # Calculate uplink delay (user to satellite)
        uplink_bandwidth = comm_config['b_us_hz']  # Hz
        # Simplified: assume effective rate is 10% of bandwidth due to SNR/coding
        uplink_rate_mbps = uplink_bandwidth * 0.1 / 8e6  # MB/s
        task.actual_uplink_time = task.data_size_mb / uplink_rate_mbps
        
        # Add propagation delay (assuming 1200km altitude, ~1800km slant range)
        propagation_delay = 1800000 / comm_config['light_speed_ms']
        task.actual_uplink_time += propagation_delay
        
        # Queue delay
        if hasattr(task, 'processing_start_time') and task.processing_start_time:
            queue_delay = task.processing_start_time - task.arrival_time
        else:
            queue_delay = task.start_time - task.arrival_time if task.start_time else 0
        
        # Processing delay (considering resource sharing)
        cpu_allocation = self._get_cpu_allocation_for_task(task)
        effective_cpu = self.cpu_frequency * cpu_allocation
        task.actual_processing_time = task.complexity / effective_cpu
        
        # Downlink delay (satellite to user, result is 1/10 of original)
        downlink_bandwidth = comm_config['b_su_hz']  # Hz
        downlink_rate_mbps = downlink_bandwidth * 0.15 / 8e6  # MB/s (better SNR)
        result_size_mb = task.data_size_mb * 0.1
        task.actual_downlink_time = result_size_mb / downlink_rate_mbps + propagation_delay
        
        # Total delay
        task.actual_total_delay = (task.actual_uplink_time + queue_delay + 
                                  task.actual_processing_time + task.actual_downlink_time)
        
        # Energy calculations
        # Uplink energy (user transmit power * time)
        p_u_w = comm_config['p_u_w']
        uplink_energy = p_u_w * (task.data_size_mb / uplink_rate_mbps)
        
        # Processing energy
        processing_energy = task.complexity * comp_config['zeta_leo']
        
        # Downlink energy (satellite transmit power * time)
        p_su_w = comm_config['p_su_w']
        downlink_energy = p_su_w * (result_size_mb / downlink_rate_mbps)
        
        # Total energy
        task.energy_consumed = uplink_energy + processing_energy + downlink_energy
        task.accumulated_energy = task.energy_consumed
        
        # Record parallel task count for analysis
        task.parallel_task_count = len(self.processing_tasks)
    
    def _get_cpu_allocation_for_task(self, task: ComputeTask) -> float:
        """
        Get CPU allocation ratio for a task based on current strategy
        
        Args:
            task: Task to allocate CPU for
            
        Returns:
            CPU allocation ratio (0-1)
        """
        if self.cpu_allocation_strategy == 'uniform':
            # Equal allocation among all processing tasks
            num_tasks = len(self.processing_tasks)
            return 1.0 / max(1, num_tasks)
        elif self.cpu_allocation_strategy == 'custom':
            # Use externally provided allocation
            return self.cpu_allocation_params.get(task.task_id, 1.0)
        else:
            # Default to uniform
            return 1.0 / max(1, len(self.processing_tasks))
    
    def reset(self):
        """Reset satellite state"""
        self.task_queue.clear()
        self.processing_tasks.clear()
        self.completed_tasks.clear()
        self.dropped_tasks.clear()
        
        self.battery_energy = self.max_battery
        self.is_processing = False
        self.available_cycles = 0.0
        
        self.total_energy_consumed = 0.0
        self.total_tasks_processed = 0
        self.total_tasks_dropped = 0
        self.total_penalty = 0.0
        
        self.priority_cache.clear()
        
        self.logger.info(f"Reset satellite {self.satellite_id}")

    def get_gpu_performance_stats(self) -> Dict[str, Any]:
        """获取GPU性能统计信息"""
        return self.scheduler.get_gpu_performance_stats()

    def is_gpu_enabled(self) -> bool:
        """检查是否启用了GPU加速"""
        return self.scheduler.gpu_enabled


# Alias for backward compatibility with existing code
Satellite = SatelliteCompute