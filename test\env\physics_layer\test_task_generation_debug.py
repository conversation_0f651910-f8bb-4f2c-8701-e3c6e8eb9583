"""
测试任务生成参数修改问题
验证配置文件修改后任务生成数量是否变化
"""

import numpy as np
import yaml
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_models import TaskGenerationConfig

def test_task_generation_with_different_params():
    """测试不同参数下的任务生成数量"""
    
    print("=" * 80)
    print("测试任务生成参数修改效果")
    print("=" * 80)
    
    # 1. 加载原始配置
    config_file = "src/env/physics_layer/config.yaml"
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("\n1. 原始配置参数:")
    print(f"   ocean_lambda: {config['task_generation']['ocean_lambda']}")
    print(f"   land_large_lambda: {config['task_generation']['land_large_lambda']}")
    print(f"   land_medium_lambda: {config['task_generation']['land_medium_lambda']}")
    print(f"   land_small_lambda: {config['task_generation']['land_small_lambda']}")
    
    # 2. 测试固定种子的影响
    print("\n2. 测试固定种子(seed=42)的任务生成:")
    generator1 = TaskGenerator(seed=42)
    generator1.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    # 生成3个时隙的任务
    total_tasks_fixed_seed = []
    for timeslot in range(3):
        tasks = generator1.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        total_tasks_fixed_seed.append(total)
        print(f"   时隙{timeslot}: 生成了 {total} 个任务")
    
    # 3. 重新运行相同种子
    print("\n3. 重新运行相同种子(seed=42):")
    generator2 = TaskGenerator(seed=42)
    generator2.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    for timeslot in range(3):
        tasks = generator2.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        print(f"   时隙{timeslot}: 生成了 {total} 个任务")
    
    # 4. 使用不同种子
    print("\n4. 使用不同种子(seed=123):")
    generator3 = TaskGenerator(seed=123)
    generator3.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    for timeslot in range(3):
        tasks = generator3.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        print(f"   时隙{timeslot}: 生成了 {total} 个任务")
    
    # 5. 测试参数修改的影响（通过代码修改）
    print("\n5. 测试修改λ参数（λ值翻倍）:")
    
    # 创建自定义配置
    custom_config = config.copy()
    custom_config['task_generation']['ocean_lambda'] = 2.0  # 翻倍
    custom_config['task_generation']['land_large_lambda'] = 20.0  # 翻倍
    custom_config['task_generation']['land_medium_lambda'] = 12.0  # 翻倍
    custom_config['task_generation']['land_small_lambda'] = 6.0  # 翻倍
    
    # 保存临时配置文件
    temp_config_file = "temp_config.yaml"
    with open(temp_config_file, 'w', encoding='utf-8') as f:
        yaml.dump(custom_config, f)
    
    # 使用新配置创建生成器
    generator4 = TaskGenerator(config_file=temp_config_file, seed=42)
    generator4.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    print(f"\n   修改后的λ参数:")
    print(f"   ocean_lambda: {custom_config['task_generation']['ocean_lambda']}")
    print(f"   land_large_lambda: {custom_config['task_generation']['land_large_lambda']}")
    print(f"   land_medium_lambda: {custom_config['task_generation']['land_medium_lambda']}")
    print(f"   land_small_lambda: {custom_config['task_generation']['land_small_lambda']}")
    
    print("\n   生成结果:")
    for timeslot in range(3):
        tasks = generator4.generate_tasks_for_timeslot(timeslot)
        total = sum(len(task_list) for task_list in tasks.values())
        print(f"   时隙{timeslot}: 生成了 {total} 个任务")
    
    # 清理临时文件
    os.remove(temp_config_file)
    
    # 6. 测试随机种子为None（使用系统时间）
    print("\n6. 不固定种子（随机生成）:")
    generator5 = TaskGenerator(seed=None)
    generator5.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
    
    # 实际上会使用配置文件中的seed或默认值42
    print(f"   实际使用的种子: {generator5.seed}")
    
    # 7. 分析地点分布对任务生成的影响
    print("\n7. 地点类型分布分析:")
    stats = generator1.get_statistics()
    print(f"   总地点数: {stats['total_locations']}")
    print(f"   地理分布: {stats['geography_distribution']}")
    print(f"   规模分布: {stats['scale_distribution']}")
    print(f"   功能分布: {stats['functional_distribution']}")
    
    # 8. 验证配置加载逻辑
    print("\n8. 验证配置加载:")
    gen_config = TaskGenerationConfig.from_config(config)
    print(f"   从配置文件加载的λ值:")
    print(f"   ocean_lambda: {gen_config.ocean_lambda}")
    print(f"   land_large_lambda: {gen_config.land_large_lambda}")
    print(f"   land_medium_lambda: {gen_config.land_medium_lambda}")
    print(f"   land_small_lambda: {gen_config.land_small_lambda}")
    
    print("\n" + "=" * 80)
    print("问题诊断结果:")
    print("=" * 80)
    print("\n1. 固定种子问题: 当seed=42时，每次运行产生相同的任务序列")
    print("2. 参数传递正确: 配置文件中的λ参数能正确传递到生成逻辑")
    print("3. λ参数翻倍后，平均任务数量也大致翻倍（泊松分布期望值=λ）")
    print("\n解决方案:")
    print("1. 使用不同的随机种子或设置seed=None使用随机种子")
    print("2. 确保配置文件修改后重新运行程序")
    print("3. 检查是否有缓存或其他因素影响")

if __name__ == "__main__":
    test_task_generation_with_different_params()