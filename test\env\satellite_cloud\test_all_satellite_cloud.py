"""
Comprehensive test suite for satellite_cloud module
Runs all tests and provides detailed output for validation
"""

import unittest
import sys
import os
from datetime import datetime

# Add source path for absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

# Import all test modules
from test.env.satellite_cloud.test_compute_models import TestComputeModels
from test.env.satellite_cloud.test_satellite_compute import TestSatelliteCompute
from test.env.satellite_cloud.test_cloud_compute import TestCloudCompute
from test.env.satellite_cloud.test_compute_manager import TestComputeManager


def create_test_suite():
    """Create comprehensive test suite"""
    suite = unittest.TestSuite()
    
    # Add compute models tests
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestComputeModels))
    
    # Add satellite compute tests
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestSatelliteCompute))
    
    # Add cloud compute tests
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestCloudCompute))
    
    # Add compute manager tests
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestComputeManager))
    
    return suite


def run_integration_test():
    """Run integration test demonstrating full system functionality"""
    print("\n" + "=" * 80)
    print("执行系统集成测试")
    print("=" * 80)
    
    try:
        # Import required modules
        from src.env.satellite_cloud.compute_manager import ComputeManager, OffloadingStrategy
        from src.env.physics_layer.task_models import Task
        from datetime import datetime
        
        # Configuration for integration test
        config = {
            'system': {
                'num_leo_satellites': 3,
                'num_cloud_centers': 2,
                'timeslot_duration_ms': 5000
            },
            'queuing': {
                'w_priority': 1.0,
                'w_urgency': 2.0,
                'w_cost': 0.5,
                'epsilon_urgency': 1.0e-6,
                'max_queue_size': 50
            },
            'computation': {
                'f_leo_hz': 500e9,
                'f_cloud_hz': 100e9,
                'zeta_leo': 1.0e-10,
                'processing_overhead_ratio': 0.05,
                'leo_battery_capacity_j': 3600000,
                'leo_solar_power_w': 5000,
                'energy_threshold_ratio': 0.2,
                'default_drop_penalty': 100.0,
                'min_cpu_allocation': 10.0,
                'max_parallel_tasks': 100
            },
            'communication': {
                'b_us_hz': 1000e6,
                'mb_to_bits': 8e6
            },
            'offloading': {
                'strategy': OffloadingStrategy.DEADLINE_AWARE,
                'max_offload_ratio': 0.7,
                'energy_threshold': 0.3,
                'queue_threshold': 0.8
            }
        }
        
        # Create compute manager
        manager = ComputeManager(config=config)
        
        print(f"创建计算管理器:")
        print(f"  卫星数量: {len(manager.satellites)}")
        print(f"  云中心数量: {len(manager.clouds)}")
        print(f"  卸载策略: {manager.strategy}")
        
        # Create diverse workload
        tasks = []
        task_types = [
            # High priority, urgent tasks
            {"priority": 5.0, "data_size": 15.0, "complexity": 2000, "deadline": 8000},
            {"priority": 4.0, "data_size": 10.0, "complexity": 1500, "deadline": 10000},
            
            # Medium priority tasks
            {"priority": 3.0, "data_size": 8.0, "complexity": 1000, "deadline": 15000},
            {"priority": 3.0, "data_size": 12.0, "complexity": 1200, "deadline": 20000},
            
            # Low priority, large tasks
            {"priority": 1.0, "data_size": 25.0, "complexity": 3000, "deadline": 30000},
            {"priority": 2.0, "data_size": 20.0, "complexity": 2500, "deadline": 25000},
        ]
        
        for i, task_spec in enumerate(task_types):
            task = Task(
                task_id=i,
                user_id=100 + i,
                priority=task_spec["priority"],
                data_size_mb=task_spec["data_size"],
                complexity_cycles_per_bit=task_spec["complexity"],
                deadline_ms=task_spec["deadline"],
                creation_timestamp=datetime.now()
            )
            tasks.append(task)
        
        print(f"\n创建了 {len(tasks)} 个不同类型的任务:")
        for i, task in enumerate(tasks):
            print(f"  任务 {task.task_id}: 优先级={task.priority}, "
                  f"数据={task.data_size_mb}MB, "
                  f"复杂度={task.complexity_cycles_per_bit}, "
                  f"截止时间={task.deadline_ms}ms")
        
        # Simulate different illumination conditions
        illuminated_scenarios = [
            {0: True, 1: True, 2: False},    # Mixed illumination
            {0: False, 1: False, 2: True},   # Mostly eclipse
            {0: True, 1: True, 2: True},     # Full illumination
        ]
        
        # Test different offloading strategies
        strategies = [
            OffloadingStrategy.LOCAL_FIRST,
            OffloadingStrategy.CLOUD_FIRST,
            OffloadingStrategy.DEADLINE_AWARE,
            OffloadingStrategy.ENERGY_AWARE
        ]
        
        strategy_results = {}
        
        for strategy in strategies:
            print(f"\n{'='*50}")
            print(f"测试策略: {strategy}")
            print(f"{'='*50}")
            
            # Reset manager for each strategy
            manager.reset()
            manager.set_offloading_strategy(strategy)
            
            current_time = 1000.0
            strategy_stats = {
                'total_completed': 0,
                'total_energy': 0.0,
                'offload_ratio': 0.0
            }
            
            # Distribute tasks across multiple timeslots
            for timeslot, illuminated_status in enumerate(illuminated_scenarios):
                print(f"\n--- 时隙 {timeslot + 1} ---")
                print(f"光照状态: {illuminated_status}")
                
                # Distribute tasks for this timeslot
                for i, task in enumerate(tasks[timeslot * 2:(timeslot + 1) * 2]):
                    if i < len(tasks):
                        source_satellite = i % len(manager.satellites)
                        success = manager.distribute_task(
                            task, source_satellite, current_time, illuminated_status
                        )
                        print(f"  任务 {task.task_id} -> 卫星 {source_satellite}: {success}")
                
                # Process timeslot
                duration = 5.0
                results = manager.process_timeslot(
                    timeslot + 1, duration, illuminated_status
                )
                
                timeslot_completed = results['total_completed']
                timeslot_energy = results['total_energy_consumed']
                strategy_stats['total_completed'] += timeslot_completed
                strategy_stats['total_energy'] += timeslot_energy
                
                print(f"  完成任务数: {timeslot_completed}")
                print(f"  能量消耗: {timeslot_energy:.2e}J")
                
                current_time += duration
            
            # Calculate final statistics
            if manager.total_tasks_received > 0:
                strategy_stats['offload_ratio'] = manager.total_tasks_offloaded / manager.total_tasks_received
            
            strategy_results[strategy] = strategy_stats
            
            print(f"\n策略 {strategy} 最终结果:")
            print(f"  总接收任务数: {manager.total_tasks_received}")
            print(f"  总完成任务数: {strategy_stats['total_completed']}")
            print(f"  总卸载任务数: {manager.total_tasks_offloaded}")
            print(f"  卸载比例: {strategy_stats['offload_ratio']:.1%}")
            print(f"  卸载成功率: {manager.offload_success_rate:.1%}")
            print(f"  总能量消耗: {strategy_stats['total_energy']:.2e}J")
        
        # Summary comparison
        print(f"\n{'='*60}")
        print("策略对比总结")
        print(f"{'='*60}")
        print(f"{'策略':<20} {'完成任务':<10} {'卸载比例':<10} {'能量消耗':<15}")
        print("-" * 60)
        
        for strategy, stats in strategy_results.items():
            energy_str = f"{stats['total_energy']:.1e}"
            print(f"{strategy:<20} {stats['total_completed']:<10} "
                  f"{stats['offload_ratio']:.1%}      {energy_str:<15}")
        
        print(f"\n集成测试完成! 系统各模块协调工作正常。")
        return True
        
    except Exception as e:
        print(f"集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test runner"""
    print("SPACE2 卫星云计算模块综合测试")
    print("=" * 80)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Run unit tests
    print("\n执行单元测试...")
    suite = create_test_suite()
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    # Run integration test
    print("\n执行集成测试...")
    integration_success = run_integration_test()
    
    # Summary
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"单元测试运行: {result.testsRun}")
    print(f"单元测试失败: {len(result.failures)}")
    print(f"单元测试错误: {len(result.errors)}")
    print(f"集成测试: {'通过' if integration_success else '失败'}")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    overall_success = (len(result.failures) == 0 and 
                      len(result.errors) == 0 and 
                      integration_success)
    
    print(f"\n总体结果: {'所有测试通过' if overall_success else '存在测试失败'}")
    print(f"测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)