"""
Task Distributor Module for SPACE2 Satellite Simulation
Responsible for assigning tasks to satellites based on visibility and distance
Implements retry mechanism for failed assignments
"""

import numpy as np
import yaml
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging

# Absolute imports according to coding standard
from src.env.physics_layer.task_models import (
    Task, TaskAssignment, AssignmentStatus, DistributionMetrics
)
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.Foundation_Layer.logging_config import get_logger
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.Foundation_Layer.time_manager import TimeManager, create_time_manager_from_config


class TaskDistributor:
    """
    Task distributor that assigns tasks to satellites based on:
    1. Visibility between ground station and satellites
    2. Distance-based selection (nearest satellite)
    3. Retry mechanism for failed assignments
    """
    
    def __init__(self, orbital_updater: Optional[OrbitalUpdater] = None, 
                 config_file: str = None,
                 time_manager: Optional[TimeManager] = None):
        """
        Initialize task distributor
        
        Args:
            orbital_updater: OrbitalUpdater instance for satellite visibility
            config_file: Path to configuration file
            time_manager: TimeManager instance for time synchronization
        """
        # Setup paths
        self.config_file = config_file or str(Path(__file__).parent / "config.yaml")
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize components
        self.orbital_updater = orbital_updater or OrbitalUpdater(config_file=self.config_file)
        self.time_manager = time_manager or create_time_manager_from_config(self.config)
        
        # Initialize logger
        self.logger = get_logger(__name__)
        
        # System parameters from config
        system_config = self.config.get('system', {})
        self.max_retries = self.config.get('communication', {}).get('max_retries', 2)
        self.timeslot_duration = system_config.get('timeslot_duration_s', 3)
        
        # Task assignment tracking
        self.assignments: Dict[int, TaskAssignment] = {}
        self.retry_queue: Dict[int, int] = {}  # task_id -> next_retry_timeslot
        
        # Distribution metrics
        self.metrics = DistributionMetrics()
        
        # Build location ID to ground station index mapping
        self._build_location_mapping()
        
        self.logger.info(f"TaskDistributor initialized with max_retries={self.max_retries}")
    
    @handle_errors(module="task_distributor", function="load_config")
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from yaml file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            raise SpaceSimulationError(
                message=f"Failed to load config file: {self.config_file}",
                error_code="CONFIG_LOAD_ERROR"
            )
    
    def _build_location_mapping(self):
        """Build mapping from location IDs to ground station indices"""
        self.location_to_ground_idx = {}
        ground_stations = self.orbital_updater.ground_stations
        
        for idx, (station_id, station) in enumerate(ground_stations.items()):
            # Extract numeric ID from station_id (e.g., "1" or 1)
            try:
                location_id = int(station_id)
                self.location_to_ground_idx[location_id] = idx
            except (ValueError, TypeError):
                self.logger.warning(f"Could not parse station_id: {station_id}")
        
        self.logger.info(f"Built location mapping for {len(self.location_to_ground_idx)} locations")
    
    @handle_errors(module="task_distributor", function="find_nearest_satellite")
    def find_nearest_visible_satellite(self, task: Task, timeslot: int) -> Tuple[Optional[int], Optional[float]]:
        """
        Find the nearest visible satellite for a task
        
        Args:
            task: Task to be assigned
            timeslot: Current timeslot
            
        Returns:
            Tuple of (satellite_id, distance_km) or (None, None) if no satellite visible
        """
        # Get satellites at current timeslot
        satellites = self.orbital_updater.get_satellites_at_time(timeslot)
        
        if not satellites:
            self.logger.warning(f"No satellite data available for timeslot {timeslot}")
            return None, None
        
        # Get ground station index for task location
        if task.location_id not in self.location_to_ground_idx:
            self.logger.error(f"Unknown location_id: {task.location_id}")
            return None, None
        
        ground_idx = self.location_to_ground_idx[task.location_id]
        
        # Build visibility and distance matrices
        visibility_matrix, distance_matrix = self.orbital_updater.build_satellite_ground_visibility_matrix(
            satellites, timeslot
        )
        
        # Find visible satellites for this ground station
        visible_satellite_indices = np.where(visibility_matrix[:, ground_idx])[0]
        
        if len(visible_satellite_indices) == 0:
            self.logger.debug(f"No visible satellites for location {task.location_id} at timeslot {timeslot}")
            return None, None
        
        # Find nearest satellite among visible ones
        distances_to_visible = distance_matrix[visible_satellite_indices, ground_idx]
        nearest_idx = visible_satellite_indices[np.argmin(distances_to_visible)]
        min_distance = distance_matrix[nearest_idx, ground_idx]
        
        # Get satellite ID
        satellite_list = list(satellites.keys())
        selected_satellite_str = satellite_list[nearest_idx]
        
        # Parse satellite ID (handle formats like "sat_111" or "111")
        try:
            if isinstance(selected_satellite_str, str):
                if selected_satellite_str.startswith('sat_'):
                    satellite_id = int(selected_satellite_str.split('_')[1])
                else:
                    satellite_id = int(selected_satellite_str)
            else:
                satellite_id = int(selected_satellite_str)
            
            self.logger.debug(
                f"Task {task.task_id}: Found nearest satellite {satellite_id} "
                f"at distance {min_distance:.2f} km"
            )
            
            return satellite_id, min_distance
            
        except (ValueError, IndexError) as e:
            self.logger.error(f"Failed to parse satellite ID {selected_satellite_str}: {e}")
            return None, None
    
    @handle_errors(module="task_distributor", function="assign_task")
    def assign_task_to_satellite(self, task: Task, timeslot: int) -> TaskAssignment:
        """
        Assign a task to the nearest visible satellite
        
        Args:
            task: Task to be assigned
            timeslot: Current timeslot
            
        Returns:
            TaskAssignment object with assignment details
        """
        # Check if task already has an assignment
        if task.task_id in self.assignments:
            assignment = self.assignments[task.task_id]
            
            # If already assigned or failed, return existing assignment
            if assignment.status in [AssignmentStatus.ASSIGNED, AssignmentStatus.FAILED]:
                return assignment
        else:
            # Create new assignment
            assignment = TaskAssignment(
                task_id=task.task_id,
                task=task
            )
            self.assignments[task.task_id] = assignment
        
        # Find nearest visible satellite
        satellite_id, distance = self.find_nearest_visible_satellite(task, timeslot)
        
        current_time = timeslot * self.timeslot_duration
        
        if satellite_id is not None:
            # Successfully found a satellite
            assignment.mark_assigned(satellite_id, distance, current_time)
            self.logger.info(
                f"Task {task.task_id} assigned to satellite {satellite_id} "
                f"(distance: {distance:.2f} km)"
            )
            
            # Remove from retry queue if present
            if task.task_id in self.retry_queue:
                del self.retry_queue[task.task_id]
        else:
            # No visible satellite
            if assignment.can_retry(self.max_retries):
                # Schedule for retry
                assignment.mark_retry(current_time)
                next_retry_timeslot = timeslot + 1  # Retry in next timeslot
                self.retry_queue[task.task_id] = next_retry_timeslot
                
                self.logger.info(
                    f"Task {task.task_id}: No visible satellite, scheduled for retry "
                    f"(attempt {assignment.retry_count}/{self.max_retries})"
                )
            else:
                # Max retries exceeded
                assignment.mark_failed("MAX_RETRIES_EXCEEDED")
                self.logger.warning(
                    f"Task {task.task_id} failed after {assignment.retry_count} retries"
                )
                
                # Remove from retry queue
                if task.task_id in self.retry_queue:
                    del self.retry_queue[task.task_id]
        
        # Update metrics
        self.metrics.update_assignment(assignment)
        
        return assignment
    
    def process_retry_queue(self, current_timeslot: int) -> List[TaskAssignment]:
        """
        Process tasks in retry queue for the current timeslot
        
        Args:
            current_timeslot: Current timeslot number
            
        Returns:
            List of updated task assignments
        """
        updated_assignments = []
        
        # Find tasks that need retry in current timeslot
        tasks_to_retry = [
            task_id for task_id, retry_timeslot in self.retry_queue.items()
            if retry_timeslot <= current_timeslot
        ]
        
        for task_id in tasks_to_retry:
            if task_id not in self.assignments:
                self.logger.error(f"Task {task_id} in retry queue but not in assignments")
                del self.retry_queue[task_id]
                continue
            
            assignment = self.assignments[task_id]
            task = assignment.task
            
            # Check if task deadline has passed
            current_time = current_timeslot * self.timeslot_duration
            if current_time > task.deadline_timestamp:
                assignment.mark_failed("DEADLINE_EXCEEDED")
                del self.retry_queue[task_id]
                self.logger.warning(f"Task {task_id} failed: deadline exceeded")
                updated_assignments.append(assignment)
                continue
            
            # Retry assignment
            self.logger.info(f"Retrying task {task_id} (attempt {assignment.retry_count + 1})")
            updated_assignment = self.assign_task_to_satellite(task, current_timeslot)
            updated_assignments.append(updated_assignment)
        
        return updated_assignments
    
    def distribute_tasks(self, tasks: List[Task], timeslot: int) -> List[TaskAssignment]:
        """
        Distribute a batch of tasks to satellites
        
        Args:
            tasks: List of tasks to distribute
            timeslot: Current timeslot
            
        Returns:
            List of task assignments
        """
        assignments = []
        
        for task in tasks:
            assignment = self.assign_task_to_satellite(task, timeslot)
            assignments.append(assignment)
        
        # Update metrics
        self.metrics.tasks_per_timeslot[timeslot] = len(tasks)
        
        self.logger.info(
            f"Timeslot {timeslot}: Distributed {len(tasks)} tasks, "
            f"{sum(1 for a in assignments if a.status == AssignmentStatus.ASSIGNED)} assigned, "
            f"{sum(1 for a in assignments if a.status == AssignmentStatus.RETRYING)} retrying"
        )
        
        return assignments
    
    def simulate_timeslot(self, tasks: List[Task], timeslot: int) -> Dict[str, Any]:
        """
        Simulate task distribution for a complete timeslot
        
        Args:
            tasks: New tasks generated in this timeslot
            timeslot: Current timeslot number
            
        Returns:
            Dictionary containing timeslot results
        """
        # Process retry queue first
        retry_results = self.process_retry_queue(timeslot)
        
        # Distribute new tasks
        new_assignments = self.distribute_tasks(tasks, timeslot)
        
        # Combine results
        all_assignments = retry_results + new_assignments
        
        # Calculate statistics
        self.metrics.calculate_statistics()
        
        result = {
            "timeslot": timeslot,
            "new_tasks": len(tasks),
            "retry_tasks": len(retry_results),
            "total_processed": len(all_assignments),
            "assigned": sum(1 for a in all_assignments if a.status == AssignmentStatus.ASSIGNED),
            "retrying": sum(1 for a in all_assignments if a.status == AssignmentStatus.RETRYING),
            "failed": sum(1 for a in all_assignments if a.status == AssignmentStatus.FAILED),
            "retry_queue_size": len(self.retry_queue),
            "success_rate": self.metrics.assignment_success_rate
        }
        
        return result
    
    def get_assignment(self, task_id: int) -> Optional[TaskAssignment]:
        """
        Get assignment details for a specific task
        
        Args:
            task_id: Task ID
            
        Returns:
            TaskAssignment object or None if not found
        """
        return self.assignments.get(task_id)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get distribution statistics
        
        Returns:
            Dictionary containing statistics
        """
        self.metrics.calculate_statistics()
        
        stats = self.metrics.to_dict()
        stats["retry_queue_size"] = len(self.retry_queue)
        stats["active_assignments"] = len(self.assignments)
        
        # Calculate average metrics
        if self.metrics.assigned_tasks > 0:
            assigned_distances = [
                a.distance_km for a in self.assignments.values()
                if a.status == AssignmentStatus.ASSIGNED and a.distance_km is not None
            ]
            if assigned_distances:
                stats["avg_assignment_distance"] = np.mean(assigned_distances)
        
        if self.metrics.total_tasks > 0:
            retry_counts = [a.retry_count for a in self.assignments.values()]
            stats["avg_retry_count"] = np.mean(retry_counts)
        
        return stats
    
    def reset(self):
        """Reset the distributor state"""
        self.assignments.clear()
        self.retry_queue.clear()
        self.metrics = DistributionMetrics()
        self.logger.info("TaskDistributor reset")