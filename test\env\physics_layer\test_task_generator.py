"""
Test module for TaskGenerator
Tests task generation functionality based on timeslots
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_models import (
    Location, Task, TaskType, GeographyType, ScaleType, FunctionalType
)


class TestTaskGenerator:
    """Test class for TaskGenerator"""
    
    def __init__(self):
        """Initialize test environment"""
        self.generator = TaskGenerator(seed=42)
        self.test_timeslots = [1, 10, 100, 500, 1000, 1440]
        
    def setup_test_locations(self):
        """Setup test locations with different characteristics"""
        # Create test locations covering different types
        test_locations = [
            # Ocean locations
            Location(1, 10.0, 20.0, GeographyType.OCEAN, ScaleType.SMALL, FunctionalType.NORMAL),
            Location(2, -30.0, 40.0, GeographyType.OCEAN, ScaleType.MEDIUM, FunctionalType.INDUSTRIAL),
            
            # Land locations - Small
            Location(3, 40.5, -73.9, GeographyType.LAND, ScaleType.SMALL, FunctionalType.NORMAL),
            Location(4, 51.5, -0.1, GeographyType.LAND, ScaleType.SMALL, FunctionalType.DELAY_SENSITIVE),
            
            # Land locations - Medium
            Location(5, 35.6, 139.6, GeographyType.LAND, ScaleType.MEDIUM, FunctionalType.NORMAL),
            Location(6, 48.8, 2.3, GeographyType.LAND, ScaleType.MEDIUM, FunctionalType.INDUSTRIAL),
            
            # Land locations - Large
            Location(7, 31.2, 121.4, GeographyType.LAND, ScaleType.LARGE, FunctionalType.DELAY_SENSITIVE),
            Location(8, 39.9, 116.4, GeographyType.LAND, ScaleType.LARGE, FunctionalType.INDUSTRIAL),
        ]
        
        self.generator.locations = test_locations
        print(f"Setup {len(test_locations)} test locations")
        return test_locations
    
    def test_lambda_calculation(self):
        """Test lambda calculation for different location types"""
        print("\n=== Testing Lambda Calculation ===")
        
        test_cases = [
            (GeographyType.OCEAN, ScaleType.SMALL, 1.0),
            (GeographyType.LAND, ScaleType.SMALL, 3.0),
            (GeographyType.LAND, ScaleType.MEDIUM, 6.0),
            (GeographyType.LAND, ScaleType.LARGE, 10.0),
        ]
        
        for geography, scale, expected_lambda in test_cases:
            location = Location(
                999, 0.0, 0.0, geography, scale, FunctionalType.NORMAL
            )
            calculated_lambda = self.generator.calculate_lambda(location)
            print(f"  {geography.value}-{scale.value}: λ = {calculated_lambda} (expected: {expected_lambda})")
            assert calculated_lambda == expected_lambda, f"Lambda mismatch for {geography}-{scale}"
        
        print("  ✓ All lambda calculations correct")
    
    def test_task_type_sampling(self):
        """Test task type sampling based on functional type"""
        print("\n=== Testing Task Type Sampling ===")
        
        # Test sampling distribution over many samples
        samples = 1000
        functional_types = [
            FunctionalType.NORMAL,
            FunctionalType.INDUSTRIAL,
            FunctionalType.DELAY_SENSITIVE
        ]
        
        for func_type in functional_types:
            type_counts = {TaskType.REALTIME: 0, TaskType.NORMAL: 0, TaskType.COMPUTE_INTENSIVE: 0}
            
            for _ in range(samples):
                task_type = self.generator.sample_task_type(func_type)
                type_counts[task_type] += 1
            
            print(f"  {func_type.value} distribution ({samples} samples):")
            for task_type, count in type_counts.items():
                percentage = (count / samples) * 100
                print(f"    {task_type.name}: {percentage:.1f}%")
        
        print("  ✓ Task type sampling working correctly")
    
    def test_task_generation_per_timeslot(self):
        """Test task generation for specific timeslots"""
        print("\n=== Testing Task Generation per Timeslot ===")
        
        # Setup locations first
        locations = self.setup_test_locations()
        
        for timeslot in self.test_timeslots:
            print(f"\n  --- Timeslot {timeslot} ---")
            
            # Generate tasks for all locations
            tasks_by_location = self.generator.generate_tasks_for_timeslot(timeslot)
            
            total_tasks = sum(len(tasks) for tasks in tasks_by_location.values())
            print(f"  Total tasks generated: {total_tasks}")
            print(f"  Locations with tasks: {len(tasks_by_location)}")
            
            # Analyze task distribution
            if tasks_by_location:
                # Check task properties
                for location_id, tasks in tasks_by_location.items():
                    if tasks:
                        task = tasks[0]  # Check first task
                        print(f"    Location {location_id}: {len(tasks)} tasks")
                        print(f"      Sample task: ID={task.task_id}, Type={task.type_id.name}")
                        print(f"      Data size: {task.data_size_mb:.2f} MB")
                        print(f"      Complexity: {task.complexity_cycles_per_bit} cycles/bit")
                        print(f"      Priority: {task.priority}")
                        print(f"      Deadline: {task.deadline_timestamp:.1f}s")
                        
                        # Verify task properties
                        assert task.task_id > 0, "Invalid task ID"
                        assert task.data_size_mb > 0, "Invalid data size"
                        assert task.complexity_cycles_per_bit > 0, "Invalid complexity"
                        assert 1 <= task.priority <= 3, "Invalid priority"
                        assert task.deadline_timestamp > task.generation_time, "Invalid deadline"
                        break
            
            # Verify time consistency
            expected_time = timeslot * self.generator.timeslot_duration
            for tasks in tasks_by_location.values():
                for task in tasks:
                    assert task.generation_time == expected_time, f"Time mismatch for task {task.task_id}"
        
        print("\n  ✓ Task generation per timeslot validated")
    
    def test_poisson_distribution(self):
        """Test that task generation follows Poisson distribution"""
        print("\n=== Testing Poisson Distribution ===")
        
        # Setup a single location with known lambda
        location = Location(
            100, 40.0, -74.0, GeographyType.LAND, ScaleType.LARGE, FunctionalType.NORMAL
        )
        self.generator.locations = [location]
        
        expected_lambda = self.generator.calculate_lambda(location)
        print(f"  Testing with λ = {expected_lambda}")
        
        # Generate tasks for many timeslots to verify distribution
        num_samples = 1000
        task_counts = []
        
        for timeslot in range(num_samples):
            tasks = self.generator.generate_tasks_for_location(location, timeslot)
            task_counts.append(len(tasks))
        
        # Calculate statistics
        mean_tasks = np.mean(task_counts)
        variance_tasks = np.var(task_counts)
        
        print(f"  Generated {num_samples} samples")
        print(f"  Mean tasks per timeslot: {mean_tasks:.2f} (expected: {expected_lambda})")
        print(f"  Variance: {variance_tasks:.2f} (expected: {expected_lambda})")
        print(f"  Max tasks in a timeslot: {max(task_counts)}")
        print(f"  Min tasks in a timeslot: {min(task_counts)}")
        
        # For Poisson distribution, mean ≈ variance ≈ λ
        assert abs(mean_tasks - expected_lambda) < 0.5, "Mean deviates too much from lambda"
        assert abs(variance_tasks - expected_lambda) < 1.0, "Variance deviates too much from lambda"
        
        print("  ✓ Poisson distribution verified")
    
    def test_task_parameters_by_type(self):
        """Test that task parameters match their type specifications"""
        print("\n=== Testing Task Parameters by Type ===")
        
        location = Location(
            200, 35.0, 139.0, GeographyType.LAND, ScaleType.MEDIUM, FunctionalType.NORMAL
        )
        
        # Test each task type
        for task_type in [TaskType.REALTIME, TaskType.NORMAL, TaskType.COMPUTE_INTENSIVE]:
            print(f"\n  Testing {task_type.name}:")
            
            # Generate parameters
            params = self.generator.generate_task_parameters(task_type, 100.0)
            
            print(f"    Data size: {params['data_size_mb']:.2f} MB")
            print(f"    Complexity: {params['complexity_cycles_per_bit']} cycles/bit")
            print(f"    Deadline: {params['deadline_timestamp']:.1f}s")
            print(f"    Priority: {params['priority']}")
            
            # Verify ranges based on task type
            if task_type == TaskType.REALTIME:
                assert 10 <= params['data_size_mb'] <= 20, "REALTIME data size out of range"
                assert params['complexity_cycles_per_bit'] == 100, "REALTIME complexity incorrect"
                assert params['deadline_timestamp'] == 105.0, "REALTIME deadline incorrect"
            elif task_type == TaskType.NORMAL:
                assert 20 <= params['data_size_mb'] <= 50, "NORMAL data size out of range"
                assert params['complexity_cycles_per_bit'] == 200, "NORMAL complexity incorrect"
                assert params['deadline_timestamp'] == 115.0, "NORMAL deadline incorrect"
            elif task_type == TaskType.COMPUTE_INTENSIVE:
                assert 50 <= params['data_size_mb'] <= 150, "COMPUTE_INTENSIVE data size out of range"
                assert params['complexity_cycles_per_bit'] == 300, "COMPUTE_INTENSIVE complexity incorrect"
                assert params['deadline_timestamp'] == 130.0, "COMPUTE_INTENSIVE deadline incorrect"
            
            assert 1 <= params['priority'] <= 3, "Priority out of range"
        
        print("\n  ✓ Task parameters validated for all types")
    
    def test_statistics(self):
        """Test statistics collection"""
        print("\n=== Testing Statistics Collection ===")
        
        # Setup and generate some tasks
        self.setup_test_locations()
        
        # Generate tasks for multiple timeslots
        for timeslot in range(10):
            self.generator.generate_tasks_for_timeslot(timeslot)
        
        # Get statistics
        stats = self.generator.get_statistics()
        
        print("\n  Statistics:")
        print(f"    Total locations: {stats['total_locations']}")
        print(f"    Total tasks generated: {stats['total_tasks_generated']}")
        print(f"    Geography distribution: {stats['geography_distribution']}")
        print(f"    Scale distribution: {stats['scale_distribution']}")
        print(f"    Functional distribution: {stats['functional_distribution']}")
        
        assert stats['total_locations'] == 8, "Location count mismatch"
        assert stats['total_tasks_generated'] > 0, "No tasks generated"
        
        print("\n  ✓ Statistics collection working correctly")
    
    def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("TASK GENERATOR TEST SUITE")
        print("=" * 60)
        
        try:
            self.test_lambda_calculation()
            self.test_task_type_sampling()
            self.test_task_generation_per_timeslot()
            self.test_poisson_distribution()
            self.test_task_parameters_by_type()
            self.test_statistics()
            
            print("\n" + "=" * 60)
            print("ALL TESTS PASSED ✓")
            print("=" * 60)
            
        except AssertionError as e:
            print(f"\n❌ TEST FAILED: {e}")
            raise
        except Exception as e:
            print(f"\n❌ UNEXPECTED ERROR: {e}")
            raise


if __name__ == "__main__":
    tester = TestTaskGenerator()
    tester.run_all_tests()