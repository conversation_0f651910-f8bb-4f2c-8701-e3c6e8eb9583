import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.Foundation_Layer.time_manager import create_time_manager_from_config

def test_satellite_state_handling():
    """Test satellite state handling in orbital and communication modules"""
    
    print("=" * 80)
    print("Testing Satellite State Handling (state=TRUE/FALSE)")
    print("=" * 80)
    
    # Initialize modules
    config_file = "src/env/physics_layer/config.yaml"
    data_file = "src/env/env_data/satellite_data_72_0.csv"
    
    print("\n1. Loading original data...")
    orbital = OrbitalUpdater(data_file=data_file, config_file=config_file)
    comm_manager = CommunicationManager(orbital_updater=orbital)
    
    # Test with current data (all state=TRUE)
    time_step = 0
    satellites_all_true = orbital.get_satellites_at_time(time_step)
    print(f"   - With all state=TRUE: {len(satellites_all_true)} satellites loaded")
    
    # Get communication matrices
    isl_comm = comm_manager.get_isl_communication_matrix(time_step)
    sat_ground_comm = comm_manager.get_satellite_ground_communication_matrix(time_step)
    sat_cloud_comm = comm_manager.get_satellite_cloud_communication_matrix(time_step)
    
    print(f"   - ISL matrix shape: {isl_comm['visibility'].shape}")
    print(f"   - Satellite-Ground matrix shape: {sat_ground_comm['visibility'].shape}")
    print(f"   - Satellite-Cloud matrix shape: {sat_cloud_comm['visibility'].shape}")
    
    # Create test data with some satellites having state=FALSE
    print("\n2. Creating test data with some state=FALSE satellites...")
    
    # Read original data
    df = pd.read_csv(data_file)
    
    # Set some satellites to state=FALSE (satellites 115, 125, 135 for all timeslots)
    test_inactive_satellites = [115, 125, 135]  # Use integers
    df.loc[df['satellite_ID'].isin(test_inactive_satellites), 'state'] = 'FALSE'
    
    # Save test data
    test_data_file = "src/env/env_data/satellite_data72_test_state.csv"
    df.to_csv(test_data_file, index=False)
    print(f"   - Set satellites {test_inactive_satellites} to state=FALSE")
    print(f"   - Saved test data to {test_data_file}")
    
    # Test with modified data
    print("\n3. Testing with modified data (some state=FALSE)...")
    orbital_test = OrbitalUpdater(data_file=test_data_file, config_file=config_file)
    comm_manager_test = CommunicationManager(orbital_updater=orbital_test)
    
    satellites_with_false = orbital_test.get_satellites_at_time(time_step)
    print(f"   - With some state=FALSE: {len(satellites_with_false)} satellites loaded")
    print(f"   - Excluded satellites: {72 - len(satellites_with_false)} satellites")
    
    # Verify excluded satellites are not in the dictionary
    excluded_found = []
    for sat_id in test_inactive_satellites:
        if sat_id in satellites_with_false:  # Check as integer directly
            excluded_found.append(sat_id)
    
    if excluded_found:
        print(f"   X ERROR: Satellites {excluded_found} with state=FALSE were not excluded!")
    else:
        print(f"   + All satellites with state=FALSE were correctly excluded")
    
    # Check if active satellites are present
    active_satellites = [111, 112, 113, 114, 116]  # Use integers
    active_found = []
    for sat_id in active_satellites:
        if sat_id in satellites_with_false:  # Check as integer directly
            active_found.append(sat_id)
    
    print(f"   + Active satellites found: {len(active_found)} out of {len(active_satellites)} checked")
    
    # Test communication matrices with excluded satellites
    print("\n4. Testing communication matrices with excluded satellites...")
    isl_comm_test = comm_manager_test.get_isl_communication_matrix(time_step)
    sat_ground_comm_test = comm_manager_test.get_satellite_ground_communication_matrix(time_step)
    sat_cloud_comm_test = comm_manager_test.get_satellite_cloud_communication_matrix(time_step)
    
    print(f"   - ISL matrix shape: {isl_comm_test['visibility'].shape} (was {isl_comm['visibility'].shape})")
    print(f"   - Satellite-Ground matrix shape: {sat_ground_comm_test['visibility'].shape} (was {sat_ground_comm['visibility'].shape})")
    print(f"   - Satellite-Cloud matrix shape: {sat_cloud_comm_test['visibility'].shape} (was {sat_cloud_comm['visibility'].shape})")
    
    expected_satellites = 72 - len(test_inactive_satellites)
    if isl_comm_test['visibility'].shape[0] == expected_satellites:
        print(f"   + Matrix dimensions correctly adjusted for {expected_satellites} active satellites")
    else:
        print(f"   X ERROR: Expected {expected_satellites} satellites in matrices")
    
    # Test multiple timeslots
    print("\n5. Testing multiple timeslots...")
    test_timeslots = [0, 100, 500, 1000, 1999]
    
    for ts in test_timeslots:
        sats = orbital_test.get_satellites_at_time(ts)
        print(f"   - Timeslot {ts:4d}: {len(sats)} active satellites")
        
        # Verify consistency
        if len(sats) != expected_satellites:
            print(f"     X ERROR: Inconsistent satellite count at timeslot {ts}")
    
    # Clean up test file
    print("\n6. Cleaning up test data file...")
    os.remove(test_data_file)
    print(f"   - Removed {test_data_file}")
    
    print("\n" + "=" * 80)
    print("Test completed successfully!")
    print("=" * 80)

if __name__ == "__main__":
    test_satellite_state_handling()