# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is the SPACE2 satellite edge computing simulation environment - a LEO satellite constellation edge-cloud collaborative computing simulation platform based on PettingZoo framework supporting multi-agent reinforcement learning algorithms.

The system simulates 72 LEO satellites, 420 ground user terminals, and 5 cloud computing centers in complex interactions across 1441 timeslots (7205 seconds total simulation time).

## Project Structure
- `src/env/` - All environment-related source code
  - `Foundation_Layer/` - Core infrastructure (error handling, logging, time management)
  - `physics_layer/` - Physical simulation components (orbital dynamics, config)
  - `env_data/` - Simulation data files (satellite data, ground stations, cloud stations)
- `src/agent/` - Agent-related code (currently empty)
- `test/` - Test files matching source code structure
- `techincal_files/` - Technical documentation and supplementary files
- `env.md` - Comprehensive technical documentation (in Chinese)

## Configuration
Main configuration file: `src/env/physics_layer/config.yaml`
- System parameters (satellites: 72 LEO, users: 420, timeslots: 1441)
- Communication model parameters
- Computation model parameters
- Observation space normalization
- Hybrid simulation parameters

## Development Guidelines

### Code Organization Principles
- **Separation of Concerns**: Environment source code in `src/env/`, tests in `/test/`
- **Simplicity**: Source code focuses on core logic without test/output code
- **Decoupling**: Loose coupling between modules
- **Clear Interfaces**: Expose appropriate interfaces for integration

### Testing Approach
- Test by outputting calculation results per timeslot
- Test file structure mirrors source code structure for easy maintenance
- Tests validate computational accuracy through timeslot-based result verification

### Development Workflow
1. Requirements Analysis → Programming Planning → Test Method Design
2. Programming Implementation → Test Verification → Merge to Main Branch
3. Documentation + Test Documentation

### Coding Standards
- **Language**: Python 3.9+
- **Style**: PEP8 compliant with clear comments (classes, methods, key logic blocks)
- **Naming**: Consistent use of snake_case
- **Modules**: Each module should provide `__init__.py`

### Performance Optimization
- **Matrix Operations**: Prefer NumPy matrix/vectorized operations over for loops to reduce complexity
  - Example: Use `np.dot(A, B)` instead of nested loops
- **Sparse Representation**: Use `scipy.sparse` for large network/connection matrices to save memory
- **Batch Processing**: Avoid processing data individually, prioritize batch design
- **Caching**: Cache orbital data, visibility windows, etc., or pre-compute them

### Decoupling and Modularization
- Physical layer, communication layer, and agents should be independent, communicating through well-defined interfaces
- Data loading should be implemented through a unified DataLoader module
- Hard-coded paths or experimental parameters are not allowed in core modules

### Logging and Error Handling
- All modules should support logger with debug/info/error multi-level output
- Exceptions must be handled structurally, no `try: ... except: pass` allowed

## Key Components
Based on the codebase structure and documentation:

1. **Time Management**: Core time synchronization and context management
2. **Orbital Dynamics**: LEO satellite position and visibility calculations using ECEF coordinate system
   - Data source: `satellite_data72_1.csv` with 103,752 records (72 satellites × 1441 timeslots)
   - Supports three visibility matrices: satellite-satellite, satellite-ground, satellite-cloud
   - Vectorized distance calculations for performance optimization
3. **Error Handling**: Structured error management framework
4. **Logging**: Comprehensive logging configuration
5. **Data Processing**: Satellite, ground station, and cloud center data handling

## Architecture Notes
- This is a complex multi-agent simulation environment
- Uses layered architecture: Foundation → Physics → Environment layers
- Supports both traditional and hybrid simulation modes
- Integrates with PettingZoo for reinforcement learning compatibility
- **Scalable Satellite Count**: Current configuration uses 72 LEO satellites, but the system is designed to support variable satellite counts for testing different constellation scales

## Recent Implementation Updates

### Orbital Module (orbital.py) - Version 2.0
- **Data Source**: Updated to use `satellite_data72_1.csv` (103,752 satellite records)
- **Format**: `satillite_ID,time_slot,time,lat,lon,light,state`
- **Visibility Matrices**: Implements three types of visibility calculations:
  - Inter-satellite visibility (72×72 matrix)
  - Satellite-to-ground visibility (72×420 matrix) 
  - Satellite-to-cloud visibility (72×5 matrix)
- **Distance Thresholds**: 
  - Satellite-satellite: 2000km
  - Satellite-ground: 1000km
  - Satellite-cloud: 1200km
- **Coordinate System**: ECEF (Earth-Centered, Earth-Fixed) for accurate 3D distance calculations
- **Performance**: Vectorized NumPy operations for matrix computations
- **Testing**: Complete test coverage with per-timeslot result verification

## Development Standards (Extended Guidelines)

### Performance Optimization (Enhanced)
- **Vectorized Operations**: All distance calculations use NumPy vectorization
- **Memory Management**: Large matrices handled efficiently with appropriate data types
- **Caching Strategy**: Orbital positions and visibility results cached per timeslot
- **Testing Approach**: Output complete matrices for verification, not just summaries

### Scalability Requirements
- **Variable Satellite Count**: System supports different constellation sizes (18, 36, 72, etc.)
- **Modular Design**: Orbital calculations independent of other system components
- **Future Testing**: Different scales of satellite constellations planned for performance evaluation

````markdown path=编程规范.md mode=EDIT
# SPACE2 项目编程规范

## 1. 导入规范 (Import Standards)

### 1.1 坚持使用绝对导入 (Use Absolute Imports)
为了避免路径混乱和重构困难，项目中必须使用绝对路径导入。

**绝对导入**: 从项目的根目录（例如 src）开始的完整路径。

```python
# ✅ 正确示例
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.Foundation_Layer.time_manager import TimeManager
from src.env.physics_layer.communication_refactored import CommunicationManager

# ❌ 错误示例
from ..orbital import OrbitalUpdater  # 相对导入
from orbital import OrbitalUpdater    # 模糊导入
```

### 1.2 导入顺序规范
```python
# 1. 标准库导入
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional

# 2. 第三方库导入
import numpy as np
import pandas as pd
import yaml

# 3. 项目内部导入
from src.env.Foundation_Layer.time_manager import TimeManager
from src.env.physics_layer.orbital import OrbitalUpdater
```

## 2. 配置管理规范 (Configuration Management)

### 2.1 参数配置原则
所有参数都必须使用 `config.yaml` 中的参数，严禁在代码中配置默认参数。

```python
# ✅ 正确示例
def __init__(self, config_file: str = "src/env/physics_layer/config.yaml"):
    with open(config_file, 'r') as f:
        self.config = yaml.safe_load(f)
    self.num_satellites = self.config['system']['num_leo_satellites']

# ❌ 错误示例
def __init__(self, num_satellites: int = 72):  # 硬编码默认值
    self.num_satellites = num_satellites
```

### 2.2 配置文件结构
- 主配置文件: `src/env/physics_layer/config.yaml`
- 配置加载必须包含错误处理
- 支持配置验证和类型检查

## 3. 数据使用规范 (Data Usage Standards)

### 3.1 真实数据原则
所有数据均使用真实的，不要自己编造数据。

```python
# ✅ 正确示例
satellite_data = pd.read_csv("src/env/env_data/satellite_data72_1.csv")
ground_stations = pd.read_csv("src/env/env_data/global_ground_stations.csv")

# ❌ 错误示例
# 不要编造假数据
fake_positions = [(0, 0, 1200000), (10, 10, 1200000)]
```

### 3.2 数据文件管理
- 数据文件统一存放在 `src/env/env_data/` 目录
- 使用标准化的数据格式 (CSV, JSON)
- 数据文件必须包含完整的字段说明

## 4. 测试规范 (Testing Standards)

### 4.1 测试目录结构
测试文档的编写目录必须是在 `test/` 下面的与 `src/` 中相同的镜像目录。

```
项目结构:
src/
├── env/
│   ├── Foundation_Layer/
│   │   └── time_manager.py
│   └── physics_layer/
│       ├── orbital.py
│       └── communication_refactored.py

test/
├── env/
│   ├── Foundation_Layer/
│   │   └── test_time_manager.py
│   └── physics_layer/
│       ├── test_orbital.py
│       └── test_communication_refactored.py
```

### 4.2 测试文件命名规范
- 测试文件名: `test_[模块名].py`
- 测试类名: `Test[类名]`
- 测试方法名: `test_[功能描述]`

### 4.3 测试编写要求：使用时隙输出
所有测试必须基于时隙进行验证，输出详细的时隙信息。

```python
def test_orbital_timeslot_output(self):
    """测试轨道模块的时隙输出"""
    test_timeslots = [1, 100, 1000]
    
    for timeslot in test_timeslots:
        print(f"\n=== 时隙 {timeslot} 测试结果 ===")
        
        # 获取时间上下文
        context = self.time_manager.get_time_context(timeslot)
        print(f"仿真步数: {context.simulation_step}")
        print(f"仿真时间: {context.simulation_time}s")
        print(f"物理时间: {context.physical_time}")
        
        # 测试卫星位置
        satellites = self.orbital.get_satellites_at_time(timeslot)
        print(f"卫星数量: {len(satellites)}")
        
        # 验证结果
        assert len(satellites) == 72
        assert context.simulation_step == timeslot
```

## 5. 代码质量规范 (Code Quality Standards)

### 5.1 逻辑检查重点
着重检查以下逻辑问题：
- 时间步进的一致性
- 矩阵维度的正确性
- 数据类型的匹配
- 边界条件的处理
- 异常情况的处理

### 5.2 跨程序API使用规范
严格检查跨程序之间的API使用：

```python
# ✅ 正确的API调用
class CommunicationManager:
    def __init__(self, orbital_updater: OrbitalUpdater):
        self.orbital = orbital_updater
    
    def update_communication_matrices(self, timeslot: int):
        # 正确使用orbital模块的API
        satellites = self.orbital.get_satellites_at_time(timeslot)
        visibility_matrix = self.orbital.build_visibility_matrix(satellites)
```

### 5.3 接口设计原则
- 明确定义输入输出类型
- 提供详细的文档字符串
- 实现适当的错误处理
- 保持接口的向后兼容性

## 6. 文档规范 (Documentation Standards)

### 6.1 代码注释
```python
def calculate_distance_matrix(self, positions1: np.ndarray, positions2: np.ndarray) -> np.ndarray:
    """
    计算两组位置之间的距离矩阵
    
    Args:
        positions1: 第一组位置坐标 (N, 3)
        positions2: 第二组位置坐标 (M, 3)
    
    Returns:
        距离矩阵 (N, M)，单位：米
    
    Raises:
        ValueError: 当输入维度不正确时
    """
```

### 6.2 模块文档
每个模块必须包含 `__init__.py` 文件，说明模块功能和导出接口。

## 7. 性能规范 (Performance Standards)

### 7.1 矩阵运算优化
- 优先使用NumPy向量化操作
- 避免Python循环处理大型数组
- 合理使用内存，避免不必要的数据复制

### 7.2 缓存策略
- 对重复计算的结果进行缓存
- 合理设置缓存失效策略
- 监控内存使用情况

## 8. 错误处理规范 (Error Handling Standards)

### 8.1 统一异常处理
```python
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError

@handle_errors(module="orbital", function="update_positions")
def update_positions(self, time_context: TimeContext):
    try:
        # 核心逻辑
        pass
    except Exception as e:
        raise SpaceSimulationError(
            message=f"位置更新失败: {str(e)}",
            error_code="POSITION_UPDATE_FAILURE"
        )
``

## 9. 版本控制规范 (Version Control Standards)

### 9.1 提交信息格式
```
类型(范围): 简短描述

详细描述（可选）

类型: feat, fix, docs, style, refactor, test, chore
范围: 模块名称，如 orbital, communication, time_manager
```

### 9.2 分支管理
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/功能名`: 功能开发分支
- `hotfix/问题描述`: 紧急修复分支

## 10. 代码审查清单 (Code Review Checklist)

- [ ] 使用绝对导入路径
- [ ] 所有参数来自config.yaml
- [ ] 使用真实数据，无编造数据
- [ ] 测试文件在正确的镜像目录
- [ ] 测试包含时隙输出验证
- [ ] API调用正确，接口匹配
- [ ] 代码逻辑清晰，无明显bug
- [ ] 异常处理完善
- [ ] 文档和注释完整
- [ ] 性能考虑合理
````
