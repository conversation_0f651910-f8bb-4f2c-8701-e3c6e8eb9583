"""
测试实际的任务分配过程，找出为什么只有部分卫星接收到任务
"""

import sys
from pathlib import Path
import numpy as np
import yaml
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.task_generator import TaskGenerator

# 加载配置
config_path = Path(__file__).parent.parent.parent / 'src/env/physics_layer/config.yaml'
with open(config_path, 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 初始化
orbital = OrbitalUpdater(None, str(config_path))
generator = TaskGenerator()
csv_path = Path(__file__).parent.parent.parent / 'src/env/env_data/global_ground_stations.csv'
generator.load_locations_from_csv(str(csv_path))

# 获取卫星ID
actual_satellite_ids = sorted([int(sid) for sid in orbital.get_satellite_ids(1)])
print(f"卫星ID: {actual_satellite_ids}")
print(f"卫星数量: {len(actual_satellite_ids)}")

# 创建映射
satellite_id_mapping = {}  # idx -> actual_id
satellite_index_mapping = {}  # actual_id -> idx
for idx, actual_sat_id in enumerate(actual_satellite_ids):
    satellite_id_mapping[idx] = actual_sat_id
    satellite_index_mapping[actual_sat_id] = idx

# 测试多个时隙的任务分配
print("\n测试任务分配（模拟system_validation的逻辑）:")

# 统计每个卫星索引被分配的任务数
satellite_task_counts = [0] * 72

for timeslot in [1, 10, 50, 100]:
    print(f"\n时隙 {timeslot}:")
    
    # 获取可见性矩阵
    satellites_dict = orbital.get_satellites_at_time(timeslot)
    sg_visibility, sg_distances = orbital.build_satellite_ground_visibility_matrix(satellites_dict, timeslot)
    
    # 生成任务
    current_time = (timeslot - 1) * config['system']['timeslot_duration_s']
    total_tasks = 0
    assigned_tasks = 0
    
    # 只测试部分地面站
    test_locations = [loc for loc in generator.locations if loc.geography == 'Land'][:50]
    
    for location in test_locations:
        tasks = generator.generate_tasks_for_location(location, current_time)
        total_tasks += len(tasks)
        
        if tasks:
            # 模拟system_validation的任务分配逻辑
            ground_id = location.location_id - 1  # 转换为0-based索引
            
            if ground_id >= 0 and ground_id < sg_visibility.shape[1]:
                # 找可见卫星
                visible_satellites_with_distance = []
                
                # 这是system_validation.py第325行的循环
                for sat_id in range(len(actual_satellite_ids)):  # sat_id是0-71的索引
                    if sat_id < sg_visibility.shape[0] and sg_visibility[sat_id, ground_id]:
                        # 获取卫星位置
                        actual_sat_id = satellite_id_mapping[sat_id]
                        if actual_sat_id in satellites_dict:
                            sat_data = satellites_dict[actual_sat_id]
                            sat_lat = sat_data.latitude
                            sat_lon = sat_data.longitude
                            ground_lat = location.latitude
                            ground_lon = location.longitude
                            
                            # 计算距离
                            lat_diff = sat_lat - ground_lat
                            lon_diff = sat_lon - ground_lon
                            if lon_diff > 180:
                                lon_diff -= 360
                            elif lon_diff < -180:
                                lon_diff += 360
                            distance = np.sqrt(lat_diff**2 + lon_diff**2)
                            
                            visible_satellites_with_distance.append((sat_id, distance))
                
                if visible_satellites_with_distance:
                    # 选择最近的卫星
                    best_satellite = min(visible_satellites_with_distance, key=lambda x: x[1])[0]
                    
                    # 记录任务分配
                    satellite_task_counts[best_satellite] += len(tasks)
                    assigned_tasks += len(tasks)
    
    print(f"  生成任务: {total_tasks}")
    print(f"  分配任务: {assigned_tasks}")
    print(f"  分配率: {assigned_tasks/total_tasks*100:.1f}%")

print("\n\n任务分配到各卫星的统计:")
print(f"接收到任务的卫星数: {sum(1 for c in satellite_task_counts if c > 0)}")
print(f"总任务数: {sum(satellite_task_counts)}")

# 分析哪些卫星没有接收到任务
satellites_with_tasks = []
satellites_without_tasks = []

for idx, count in enumerate(satellite_task_counts):
    if count > 0:
        satellites_with_tasks.append((idx, satellite_id_mapping[idx], count))
    else:
        satellites_without_tasks.append((idx, satellite_id_mapping[idx]))

print(f"\n有任务的卫星 ({len(satellites_with_tasks)} 个):")
print(f"  索引范围: {min(s[0] for s in satellites_with_tasks)} - {max(s[0] for s in satellites_with_tasks)}")
print(f"  ID范围: {min(s[1] for s in satellites_with_tasks)} - {max(s[1] for s in satellites_with_tasks)}")
print(f"  前5个: {satellites_with_tasks[:5]}")

print(f"\n无任务的卫星 ({len(satellites_without_tasks)} 个):")
if satellites_without_tasks:
    print(f"  索引范围: {min(s[0] for s in satellites_without_tasks)} - {max(s[0] for s in satellites_without_tasks)}")
    print(f"  ID范围: {min(s[1] for s in satellites_without_tasks)} - {max(s[1] for s in satellites_without_tasks)}")
    print(f"  具体: {satellites_without_tasks}")

# 检查可见性矩阵
print(f"\n可见性矩阵检查:")
print(f"  形状: {sg_visibility.shape}")
print(f"  第0行（索引0卫星）可见地面站数: {np.sum(sg_visibility[0, :])}")
print(f"  第6行（索引6卫星）可见地面站数: {np.sum(sg_visibility[6, :])}")
print(f"  第70行（索引70卫星）可见地面站数: {np.sum(sg_visibility[70, :])}")