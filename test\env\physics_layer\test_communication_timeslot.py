"""
Communication Module Test - Complete 2000 Timeslots Communication Matrix Output
测试communication_refactored.py模块的通信链路计算功能
输出完整2000个时隙的通信数据矩阵和性能统计
"""
import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime
import time
import logging

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.physics_layer.orbital import OrbitalUpdater

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('communication_test_2000_timeslots.log'),
        logging.StreamHandler()
    ]
)


def format_matrix_stats(matrix, name, unit=""):
    """格式化输出矩阵统计信息"""
    non_zero = matrix[matrix > 0]
    if len(non_zero) > 0:
        return (f"  {name}: shape={matrix.shape}, "
                f"non-zero={len(non_zero)}, "
                f"mean={np.mean(non_zero):.2f}{unit}, "
                f"max={np.max(non_zero):.2f}{unit}, "
                f"min={np.min(non_zero):.2f}{unit}")
    else:
        return f"  {name}: shape={matrix.shape}, all zeros"


def test_communication_matrices_by_timeslot():
    """
    测试不同时隙的通信矩阵
    输出时隙1-5, 101-105的所有通信相关数据矩阵
    """
    print("=" * 100)
    print("COMMUNICATION MODULE TEST - TIMESLOT-BASED DATA MATRIX OUTPUT")
    print("Testing timeslots: 1-5, 101-105")
    print("=" * 100)
    
    # Initialize Communication Manager
    current_dir = Path(__file__).parent.parent.parent.parent
    config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
    data_file = current_dir / "src" / "env" / "env_data" / "satellite_data_72_0.csv"
    
    try:
        # First create orbital updater
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )
        
        # Then create communication manager
        comm_manager = CommunicationManager(
            orbital_updater=orbital,
            config_file=str(config_file)
        )
        
        print(f"[INIT] CommunicationManager initialized successfully")
        print(f"[INFO] Total timeslots: {orbital.get_total_timeslots()}")
        print(f"[INFO] Number of satellites: {len(orbital.get_satellites_at_time(0))}")
        print(f"[INFO] Number of ground stations: {orbital.get_ground_station_count()}")
        print(f"[INFO] Number of cloud centers: {orbital.get_cloud_station_count()}")
        print()
        
        # Print communication parameters
        print("[COMM PARAMETERS]")
        print(f"  ISL data rate: {comm_manager.isl_data_rate_bps/1e9:.1f} Gbps")
        print(f"  RF carrier frequency: {comm_manager.rf_carrier_freq_hz/1e9:.1f} GHz")
        print(f"  User-Satellite uplink bandwidth: {comm_manager.b_us_hz/1e6:.0f} MHz")
        print(f"  Satellite-User downlink bandwidth: {comm_manager.b_su_hz/1e6:.0f} MHz")
        print(f"  Satellite-Cloud downlink bandwidth: {comm_manager.b_sc_hz/1e6:.0f} MHz")
        print(f"  Cloud-Satellite uplink bandwidth: {comm_manager.b_cs_hz/1e6:.0f} MHz")
        print()
        
    except Exception as e:
        print(f"[ERROR] Failed to initialize: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test timeslots
    test_timeslots = list(range(1, 6)) + list(range(101, 106))
    
    for timeslot in test_timeslots:
        print("=" * 100)
        print(f"TIMESLOT {timeslot}")
        print("=" * 100)
        
        try:
            # Get time context
            time_context = orbital.time_manager.get_time_context(timeslot)
            print(f"[TIME] Physical time: {time_context.physical_time}")
            print(f"[TIME] Simulation time: {time_context.simulation_time:.2f}s")
            print()
            
            # 1. INTER-SATELLITE LINK (ISL) COMMUNICATION
            print("-" * 50)
            print("1. INTER-SATELLITE LINK (ISL) COMMUNICATION")
            print("-" * 50)
            
            isl_data = comm_manager.get_isl_communication_matrix(timeslot)
            
            print("[ISL Data Matrices]")
            print(format_matrix_stats(isl_data['data_rate_bps'], "Data Rate", " bps"))
            print(format_matrix_stats(isl_data['distance_km'], "Distance", " km"))
            print(format_matrix_stats(isl_data['propagation_delay_ms'], "Propagation Delay", " ms"))
            
            vis_count = np.sum(isl_data['visibility'])
            total_possible = len(isl_data['satellite_ids']) * (len(isl_data['satellite_ids']) - 1)
            print(f"  Visibility: {vis_count}/{total_possible} links visible ({vis_count/total_possible*100:.1f}%)")
            print(f"  Link type: {isl_data['link_type']}")
            
            # Show sample of data rate matrix (first 5x5)
            print("\n  Sample Data Rate Matrix (first 5x5, Gbps):")
            sample_matrix = isl_data['data_rate_bps'][:5, :5] / 1e9
            for row in sample_matrix:
                print("    " + " ".join([f"{val:6.1f}" if val > 0 else "   0.0" for val in row]))
            
            # 2. SATELLITE-GROUND COMMUNICATION
            print("\n" + "-" * 50)
            print("2. SATELLITE-GROUND COMMUNICATION")
            print("-" * 50)
            
            sg_data = comm_manager.get_satellite_ground_communication_matrix(timeslot)
            
            print("[Satellite-Ground Data Matrices]")
            print("Uplink (Ground -> Satellite):")
            print(format_matrix_stats(sg_data['uplink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sg_data['uplink_snr_db'], "  SNR", " dB"))
            
            print("Downlink (Satellite -> Ground):")
            print(format_matrix_stats(sg_data['downlink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sg_data['downlink_snr_db'], "  SNR", " dB"))
            
            print("Common:")
            print(format_matrix_stats(sg_data['distance_km'], "  Distance", " km"))
            print(format_matrix_stats(sg_data['propagation_delay_ms'], "  Propagation Delay", " ms"))
            
            vis_count = np.sum(sg_data['visibility'])
            total_possible = len(sg_data['satellite_ids']) * len(sg_data['ground_station_ids'])
            print(f"  Visibility: {vis_count}/{total_possible} links visible ({vis_count/total_possible*100:.1f}%)")
            print(f"  Link type: {sg_data['link_type']}")
            
            # Statistics per satellite
            vis_per_sat = np.sum(sg_data['visibility'], axis=1)
            if len(vis_per_sat) > 0:
                print(f"\n  Ground stations visible per satellite:")
                print(f"    Mean: {np.mean(vis_per_sat):.1f}, Max: {np.max(vis_per_sat)}, Min: {np.min(vis_per_sat)}")
            
            # Statistics per ground station
            vis_per_ground = np.sum(sg_data['visibility'], axis=0)
            if len(vis_per_ground) > 0:
                print(f"  Satellites visible per ground station:")
                print(f"    Mean: {np.mean(vis_per_ground):.1f}, Max: {np.max(vis_per_ground)}, Min: {np.min(vis_per_ground)}")
            
            # 3. SATELLITE-CLOUD COMMUNICATION
            print("\n" + "-" * 50)
            print("3. SATELLITE-CLOUD COMMUNICATION")
            print("-" * 50)
            
            sc_data = comm_manager.get_satellite_cloud_communication_matrix(timeslot)
            
            print("[Satellite-Cloud Data Matrices]")
            print("Uplink (Cloud -> Satellite):")
            print(format_matrix_stats(sc_data['uplink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sc_data['uplink_snr_db'], "  SNR", " dB"))
            
            print("Downlink (Satellite -> Cloud):")
            print(format_matrix_stats(sc_data['downlink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sc_data['downlink_snr_db'], "  SNR", " dB"))
            
            print("Common:")
            print(format_matrix_stats(sc_data['distance_km'], "  Distance", " km"))
            print(format_matrix_stats(sc_data['propagation_delay_ms'], "  Propagation Delay", " ms"))
            
            vis_count = np.sum(sc_data['visibility'])
            total_possible = len(sc_data['satellite_ids']) * len(sc_data['cloud_station_ids'])
            print(f"  Visibility: {vis_count}/{total_possible} links visible ({vis_count/total_possible*100:.1f}%)")
            print(f"  Link type: {sc_data['link_type']}")
            
            # Show full visibility matrix (since it's small - 72x5)
            print("\n  Full Visibility Matrix (72 satellites x 5 cloud centers):")
            print("  Cloud Centers: ", end="")
            for i in range(5):
                print(f"C{i+1:2d} ", end="")    
            print()
            
            # Show first 10 satellites
            for i in range(min(10, sc_data['visibility'].shape[0])):
                print(f"  Sat {i+1:2d}: ", end="")
                for j in range(sc_data['visibility'].shape[1]):
                    print(f"  {int(sc_data['visibility'][i, j])} ", end="")
                print()
            if sc_data['visibility'].shape[0] > 10:
                print("  ... (showing first 10 satellites only)")
            
            # Statistics
            vis_per_sat = np.sum(sc_data['visibility'], axis=1)
            vis_per_cloud = np.sum(sc_data['visibility'], axis=0)
            print(f"\n  Cloud centers visible per satellite:")
            print(f"    Mean: {np.mean(vis_per_sat):.2f}, Max: {np.max(vis_per_sat)}, Min: {np.min(vis_per_sat)}")
            print(f"  Satellites visible per cloud center:")
            for i, count in enumerate(vis_per_cloud):
                print(f"    Cloud {i+1}: {count} satellites")
            
            # 4. LINK QUALITY METRICS SUMMARY
            print("\n" + "-" * 50)
            print("4. LINK QUALITY METRICS SUMMARY")
            print("-" * 50)
            
            metrics = comm_manager.get_link_quality_metrics(timeslot)
            
            print("[ISL Metrics]")
            print(f"  Average data rate: {metrics['isl']['avg_data_rate_gbps']:.2f} Gbps")
            print(f"  Total visible links: {metrics['isl']['total_links']}")
            print(f"  Average propagation delay: {metrics['isl']['avg_delay_ms']:.2f} ms")
            
            print("\n[Satellite-Ground Metrics]")
            print(f"  Average uplink rate: {metrics['satellite_ground']['avg_uplink_rate_mbps']:.2f} Mbps")
            print(f"  Average downlink rate: {metrics['satellite_ground']['avg_downlink_rate_mbps']:.2f} Mbps")
            print(f"  Total visible links: {metrics['satellite_ground']['total_links']}")
            print(f"  Average propagation delay: {metrics['satellite_ground']['avg_delay_ms']:.2f} ms")
            
            print("\n[Satellite-Cloud Metrics]")
            print(f"  Average uplink rate: {metrics['satellite_cloud']['avg_uplink_rate_mbps']:.2f} Mbps")
            print(f"  Average downlink rate: {metrics['satellite_cloud']['avg_downlink_rate_mbps']:.2f} Mbps")
            print(f"  Total visible links: {metrics['satellite_cloud']['total_links']}")
            print(f"  Average propagation delay: {metrics['satellite_cloud']['avg_delay_ms']:.2f} ms")
            
        except Exception as e:
            print(f"[ERROR] Failed to process timeslot {timeslot}: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 100)
    print("TEST COMPLETED")
    print("=" * 100)


def test_complete_2000_timeslots_communication():
    """
    测试完整2000个时隙的通信矩阵
    输出所有时隙的通信性能数据和统计信息
    """
    print("=" * 100)
    print("COMPLETE 2000 TIMESLOTS COMMUNICATION MATRIX TEST")
    print("=" * 100)

    # Initialize Communication Manager
    current_dir = Path(__file__).parent.parent.parent.parent
    config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
    data_file = current_dir / "src" / "env" / "env_data" / "satellite_data_72_0.csv"

    try:
        # Initialize orbital updater and communication manager
        start_init = time.time()
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )

        comm_manager = CommunicationManager(
            orbital_updater=orbital,
            config_file=str(config_file)
        )
        init_time = time.time() - start_init

        print(f"[INIT] Communication system initialized in {init_time:.3f}s")
        print(f"[INFO] Total timeslots: {orbital.get_total_timeslots()}")
        print(f"[INFO] Number of satellites: {len(orbital.get_satellites_at_time(0))}")
        print(f"[INFO] Number of ground stations: {orbital.get_ground_station_count()}")
        print(f"[INFO] Number of cloud centers: {orbital.get_cloud_station_count()}")
        print()

    except Exception as e:
        print(f"[ERROR] Failed to initialize: {e}")
        import traceback
        traceback.print_exc()
        return

    # 数据存储结构
    results = {
        'timeslot': [],
        'satellite_count': [],
        'computation_time': [],

        # ISL 数据
        'isl_visible_links': [],
        'isl_avg_data_rate_gbps': [],
        'isl_total_capacity_gbps': [],
        'isl_avg_delay_ms': [],

        # 卫星-地面站数据
        'sg_visible_links': [],
        'sg_avg_uplink_mbps': [],
        'sg_avg_downlink_mbps': [],
        'sg_total_uplink_capacity_gbps': [],
        'sg_total_downlink_capacity_gbps': [],
        'sg_avg_delay_ms': [],

        # 卫星-云中心数据
        'sc_visible_links': [],
        'sc_avg_uplink_mbps': [],
        'sc_avg_downlink_mbps': [],
        'sc_total_uplink_capacity_gbps': [],
        'sc_total_downlink_capacity_gbps': [],
        'sc_avg_delay_ms': [],
    }

    print("开始完整2000时隙通信矩阵计算...")
    print("=" * 100)

    total_start_time = time.time()

    # 批量处理，每100个时隙报告一次进度
    batch_size = 100
    total_timeslots = orbital.get_total_timeslots()

    for batch_start in range(0, total_timeslots, batch_size):
        batch_end = min(batch_start + batch_size, total_timeslots)
        batch_start_time = time.time()

        for timeslot in range(batch_start, batch_end):
            timeslot_start = time.time()

            try:
                # 获取当前时隙的卫星数量
                satellites = orbital.get_satellites_at_time(timeslot)
                satellite_count = len(satellites)

                if satellite_count == 0:
                    print(f"[WARNING] 时隙 {timeslot} 无卫星数据，跳过")
                    continue

                # 获取通信质量指标
                metrics = comm_manager.get_link_quality_metrics(timeslot)

                # 获取详细的通信矩阵数据
                isl_data = comm_manager.get_isl_communication_matrix(timeslot)
                sg_data = comm_manager.get_satellite_ground_communication_matrix(timeslot)
                sc_data = comm_manager.get_satellite_cloud_communication_matrix(timeslot)

                computation_time = time.time() - timeslot_start

                # 记录结果
                results['timeslot'].append(timeslot)
                results['satellite_count'].append(satellite_count)
                results['computation_time'].append(computation_time)

                # ISL 数据
                results['isl_visible_links'].append(metrics['isl']['total_links'])
                results['isl_avg_data_rate_gbps'].append(metrics['isl']['avg_data_rate_gbps'])
                results['isl_total_capacity_gbps'].append(np.sum(isl_data['data_rate_bps']) / 1e9)
                results['isl_avg_delay_ms'].append(metrics['isl']['avg_delay_ms'])

                # 卫星-地面站数据
                results['sg_visible_links'].append(metrics['satellite_ground']['total_links'])
                results['sg_avg_uplink_mbps'].append(metrics['satellite_ground']['avg_uplink_rate_mbps'])
                results['sg_avg_downlink_mbps'].append(metrics['satellite_ground']['avg_downlink_rate_mbps'])
                results['sg_total_uplink_capacity_gbps'].append(np.sum(sg_data['uplink_data_rate_bps']) / 1e9)
                results['sg_total_downlink_capacity_gbps'].append(np.sum(sg_data['downlink_data_rate_bps']) / 1e9)
                results['sg_avg_delay_ms'].append(metrics['satellite_ground']['avg_delay_ms'])

                # 卫星-云中心数据
                results['sc_visible_links'].append(metrics['satellite_cloud']['total_links'])
                results['sc_avg_uplink_mbps'].append(metrics['satellite_cloud']['avg_uplink_rate_mbps'])
                results['sc_avg_downlink_mbps'].append(metrics['satellite_cloud']['avg_downlink_rate_mbps'])
                results['sc_total_uplink_capacity_gbps'].append(np.sum(sc_data['uplink_data_rate_bps']) / 1e9)
                results['sc_total_downlink_capacity_gbps'].append(np.sum(sc_data['downlink_data_rate_bps']) / 1e9)
                results['sc_avg_delay_ms'].append(metrics['satellite_cloud']['avg_delay_ms'])

            except Exception as e:
                print(f"[ERROR] 时隙 {timeslot} 计算失败: {e}")
                continue

        batch_time = time.time() - batch_start_time
        progress = (batch_end / total_timeslots) * 100
        avg_time_per_slot = batch_time / (batch_end - batch_start)

        print(f"[PROGRESS] 时隙 {batch_start}-{batch_end-1} 完成 "
              f"({progress:.1f}%) - 批次耗时: {batch_time:.2f}s, "
              f"平均每时隙: {avg_time_per_slot:.3f}s")

    total_test_time = time.time() - total_start_time

    print("\n" + "=" * 100)
    print("通信矩阵计算完成 - 性能统计")
    print("=" * 100)

    # 转换为DataFrame进行分析
    df_results = pd.DataFrame(results)

    print(f"[SUMMARY] 总测试时间: {total_test_time:.2f}s")
    print(f"[SUMMARY] 测试时隙数: {len(df_results)}")
    print(f"[SUMMARY] 平均每时隙计算时间: {df_results['computation_time'].mean():.4f}s")
    print(f"[SUMMARY] 最大每时隙计算时间: {df_results['computation_time'].max():.4f}s")
    print(f"[SUMMARY] 最小每时隙计算时间: {df_results['computation_time'].min():.4f}s")

    # ISL 统计
    print(f"\n[ISL] 星间链路统计:")
    print(f"  - 平均可见链路数: {df_results['isl_visible_links'].mean():.1f}")
    print(f"  - 最大可见链路数: {df_results['isl_visible_links'].max()}")
    print(f"  - 最小可见链路数: {df_results['isl_visible_links'].min()}")
    print(f"  - 平均数据速率: {df_results['isl_avg_data_rate_gbps'].mean():.2f} Gbps")
    print(f"  - 平均总容量: {df_results['isl_total_capacity_gbps'].mean():.2f} Gbps")
    print(f"  - 平均传播延迟: {df_results['isl_avg_delay_ms'].mean():.2f} ms")

    # 卫星-地面站统计
    print(f"\n[SAT-GROUND] 卫星-地面站链路统计:")
    print(f"  - 平均可见链路数: {df_results['sg_visible_links'].mean():.1f}")
    print(f"  - 最大可见链路数: {df_results['sg_visible_links'].max()}")
    print(f"  - 最小可见链路数: {df_results['sg_visible_links'].min()}")
    print(f"  - 平均上行速率: {df_results['sg_avg_uplink_mbps'].mean():.2f} Mbps")
    print(f"  - 平均下行速率: {df_results['sg_avg_downlink_mbps'].mean():.2f} Mbps")
    print(f"  - 平均上行总容量: {df_results['sg_total_uplink_capacity_gbps'].mean():.2f} Gbps")
    print(f"  - 平均下行总容量: {df_results['sg_total_downlink_capacity_gbps'].mean():.2f} Gbps")
    print(f"  - 平均传播延迟: {df_results['sg_avg_delay_ms'].mean():.2f} ms")

    # 卫星-云中心统计
    print(f"\n[SAT-CLOUD] 卫星-云中心链路统计:")
    print(f"  - 平均可见链路数: {df_results['sc_visible_links'].mean():.1f}")
    print(f"  - 最大可见链路数: {df_results['sc_visible_links'].max()}")
    print(f"  - 最小可见链路数: {df_results['sc_visible_links'].min()}")
    print(f"  - 平均上行速率: {df_results['sc_avg_uplink_mbps'].mean():.2f} Mbps")
    print(f"  - 平均下行速率: {df_results['sc_avg_downlink_mbps'].mean():.2f} Mbps")
    print(f"  - 平均上行总容量: {df_results['sc_total_uplink_capacity_gbps'].mean():.2f} Gbps")
    print(f"  - 平均下行总容量: {df_results['sc_total_downlink_capacity_gbps'].mean():.2f} Gbps")
    print(f"  - 平均传播延迟: {df_results['sc_avg_delay_ms'].mean():.2f} ms")

    # 保存详细结果
    results_file = 'communication_2000_timeslots_results.csv'
    df_results.to_csv(results_file, index=False)
    print(f"\n[SUCCESS] 详细结果已保存到: {results_file}")

    # 保存几个时隙的完整矩阵样本
    save_communication_matrix_samples(comm_manager, [0, 500, 1000, 1500, 1999])

    # 生成通信性能报告
    generate_communication_report(df_results, total_test_time)

    print("\n" + "=" * 100)
    print("完整2000时隙通信测试成功完成！")
    print("=" * 100)

    return True

def save_communication_matrix_samples(comm_manager, sample_timeslots):
    """保存通信矩阵样本用于验证"""
    print(f"\n[INFO] 保存通信矩阵样本...")

    for timeslot in sample_timeslots:
        try:
            # 获取通信矩阵
            isl_data = comm_manager.get_isl_communication_matrix(timeslot)
            sg_data = comm_manager.get_satellite_ground_communication_matrix(timeslot)
            sc_data = comm_manager.get_satellite_cloud_communication_matrix(timeslot)

            # 保存为numpy文件
            np.savez(f'communication_matrices_timeslot_{timeslot}.npz',
                    # ISL 数据
                    isl_data_rate_bps=isl_data['data_rate_bps'],
                    isl_distance_km=isl_data['distance_km'],
                    isl_propagation_delay_ms=isl_data['propagation_delay_ms'],
                    isl_visibility=isl_data['visibility'],

                    # 卫星-地面站数据
                    sg_uplink_data_rate_bps=sg_data['uplink_data_rate_bps'],
                    sg_downlink_data_rate_bps=sg_data['downlink_data_rate_bps'],
                    sg_uplink_snr_db=sg_data['uplink_snr_db'],
                    sg_downlink_snr_db=sg_data['downlink_snr_db'],
                    sg_distance_km=sg_data['distance_km'],
                    sg_propagation_delay_ms=sg_data['propagation_delay_ms'],
                    sg_visibility=sg_data['visibility'],

                    # 卫星-云中心数据
                    sc_uplink_data_rate_bps=sc_data['uplink_data_rate_bps'],
                    sc_downlink_data_rate_bps=sc_data['downlink_data_rate_bps'],
                    sc_uplink_snr_db=sc_data['uplink_snr_db'],
                    sc_downlink_snr_db=sc_data['downlink_snr_db'],
                    sc_distance_km=sc_data['distance_km'],
                    sc_propagation_delay_ms=sc_data['propagation_delay_ms'],
                    sc_visibility=sc_data['visibility'],

                    # 元数据
                    satellite_ids=isl_data['satellite_ids'],
                    ground_station_ids=sg_data['ground_station_ids'],
                    cloud_station_ids=sc_data['cloud_station_ids'])

            print(f"  - 时隙 {timeslot}: 通信矩阵已保存")

        except Exception as e:
            print(f"  - 时隙 {timeslot}: 保存失败 - {e}")

def generate_communication_report(df_results, total_time):
    """生成通信性能报告"""
    report_file = 'communication_2000_timeslots_report.txt'

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("通信模块完整2000时隙性能测试报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"测试时间: {datetime.now()}\n")
        f.write(f"总测试时长: {total_time:.2f}秒\n")
        f.write(f"测试时隙数: {len(df_results)}\n\n")

        f.write("计算性能统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"平均每时隙计算时间: {df_results['computation_time'].mean():.4f}s\n")
        f.write(f"最大每时隙计算时间: {df_results['computation_time'].max():.4f}s\n")
        f.write(f"最小每时隙计算时间: {df_results['computation_time'].min():.4f}s\n")
        f.write(f"标准差: {df_results['computation_time'].std():.4f}s\n\n")

        f.write("星间链路(ISL)统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"平均可见链路数: {df_results['isl_visible_links'].mean():.1f}\n")
        f.write(f"最大可见链路数: {df_results['isl_visible_links'].max()}\n")
        f.write(f"最小可见链路数: {df_results['isl_visible_links'].min()}\n")
        f.write(f"平均数据速率: {df_results['isl_avg_data_rate_gbps'].mean():.2f} Gbps\n")
        f.write(f"平均总容量: {df_results['isl_total_capacity_gbps'].mean():.2f} Gbps\n")
        f.write(f"平均传播延迟: {df_results['isl_avg_delay_ms'].mean():.2f} ms\n\n")

        f.write("卫星-地面站链路统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"平均可见链路数: {df_results['sg_visible_links'].mean():.1f}\n")
        f.write(f"最大可见链路数: {df_results['sg_visible_links'].max()}\n")
        f.write(f"最小可见链路数: {df_results['sg_visible_links'].min()}\n")
        f.write(f"平均上行速率: {df_results['sg_avg_uplink_mbps'].mean():.2f} Mbps\n")
        f.write(f"平均下行速率: {df_results['sg_avg_downlink_mbps'].mean():.2f} Mbps\n")
        f.write(f"平均上行总容量: {df_results['sg_total_uplink_capacity_gbps'].mean():.2f} Gbps\n")
        f.write(f"平均下行总容量: {df_results['sg_total_downlink_capacity_gbps'].mean():.2f} Gbps\n")
        f.write(f"平均传播延迟: {df_results['sg_avg_delay_ms'].mean():.2f} ms\n\n")

        f.write("卫星-云中心链路统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"平均可见链路数: {df_results['sc_visible_links'].mean():.1f}\n")
        f.write(f"最大可见链路数: {df_results['sc_visible_links'].max()}\n")
        f.write(f"最小可见链路数: {df_results['sc_visible_links'].min()}\n")
        f.write(f"平均上行速率: {df_results['sc_avg_uplink_mbps'].mean():.2f} Mbps\n")
        f.write(f"平均下行速率: {df_results['sc_avg_downlink_mbps'].mean():.2f} Mbps\n")
        f.write(f"平均上行总容量: {df_results['sc_total_uplink_capacity_gbps'].mean():.2f} Gbps\n")
        f.write(f"平均下行总容量: {df_results['sc_total_downlink_capacity_gbps'].mean():.2f} Gbps\n")
        f.write(f"平均传播延迟: {df_results['sc_avg_delay_ms'].mean():.2f} ms\n")

    print(f"[SUCCESS] 通信性能报告已保存到: {report_file}")

def compare_communication_across_timeslots():
    """
    比较不同时隙之间的通信性能变化
    """
    print("\n" + "=" * 100)
    print("COMMUNICATION PERFORMANCE COMPARISON ACROSS TIMESLOTS")
    print("=" * 100)
    
    # Initialize
    current_dir = Path(__file__).parent.parent.parent.parent
    config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
    data_file = current_dir / "src" / "env" / "env_data" / "satellite_data_72_0.csv"
    
    try:
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )
        comm_manager = CommunicationManager(
            orbital_updater=orbital,
            config_file=str(config_file)
        )
    except Exception as e:
        print(f"[ERROR] Failed to initialize: {e}")
        return
    
    # Collect statistics
    early_slots = list(range(1, 6))
    later_slots = list(range(101, 106))
    
    stats = {
        'early': {'isl_links': [], 'sg_links': [], 'sc_links': [], 
                  'sg_uplink': [], 'sg_downlink': [], 'sc_uplink': [], 'sc_downlink': []},
        'later': {'isl_links': [], 'sg_links': [], 'sc_links': [],
                  'sg_uplink': [], 'sg_downlink': [], 'sc_uplink': [], 'sc_downlink': []}
    }
    
    print("Collecting statistics for early timeslots (1-5)...")
    for timeslot in early_slots:
        metrics = comm_manager.get_link_quality_metrics(timeslot)
        stats['early']['isl_links'].append(metrics['isl']['total_links'])
        stats['early']['sg_links'].append(metrics['satellite_ground']['total_links'])
        stats['early']['sc_links'].append(metrics['satellite_cloud']['total_links'])
        stats['early']['sg_uplink'].append(metrics['satellite_ground']['avg_uplink_rate_mbps'])
        stats['early']['sg_downlink'].append(metrics['satellite_ground']['avg_downlink_rate_mbps'])
        stats['early']['sc_uplink'].append(metrics['satellite_cloud']['avg_uplink_rate_mbps'])
        stats['early']['sc_downlink'].append(metrics['satellite_cloud']['avg_downlink_rate_mbps'])
    
    print("Collecting statistics for later timeslots (101-105)...")
    for timeslot in later_slots:
        metrics = comm_manager.get_link_quality_metrics(timeslot)
        stats['later']['isl_links'].append(metrics['isl']['total_links'])
        stats['later']['sg_links'].append(metrics['satellite_ground']['total_links'])
        stats['later']['sc_links'].append(metrics['satellite_cloud']['total_links'])
        stats['later']['sg_uplink'].append(metrics['satellite_ground']['avg_uplink_rate_mbps'])
        stats['later']['sg_downlink'].append(metrics['satellite_ground']['avg_downlink_rate_mbps'])
        stats['later']['sc_uplink'].append(metrics['satellite_cloud']['avg_uplink_rate_mbps'])
        stats['later']['sc_downlink'].append(metrics['satellite_cloud']['avg_downlink_rate_mbps'])
    
    print("\n[COMPARISON RESULTS]")
    print("-" * 50)
    
    print("\nVisible Links Count:")
    print(f"  ISL Links:")
    print(f"    Early (1-5): avg={np.mean(stats['early']['isl_links']):.1f}, std={np.std(stats['early']['isl_links']):.1f}")
    print(f"    Later (101-105): avg={np.mean(stats['later']['isl_links']):.1f}, std={np.std(stats['later']['isl_links']):.1f}")
    print(f"    Change: {np.mean(stats['later']['isl_links']) - np.mean(stats['early']['isl_links']):.1f}")
    
    print(f"\n  Satellite-Ground Links:")
    print(f"    Early (1-5): avg={np.mean(stats['early']['sg_links']):.1f}, std={np.std(stats['early']['sg_links']):.1f}")
    print(f"    Later (101-105): avg={np.mean(stats['later']['sg_links']):.1f}, std={np.std(stats['later']['sg_links']):.1f}")
    print(f"    Change: {np.mean(stats['later']['sg_links']) - np.mean(stats['early']['sg_links']):.1f}")
    
    print(f"\n  Satellite-Cloud Links:")
    print(f"    Early (1-5): avg={np.mean(stats['early']['sc_links']):.1f}, std={np.std(stats['early']['sc_links']):.1f}")
    print(f"    Later (101-105): avg={np.mean(stats['later']['sc_links']):.1f}, std={np.std(stats['later']['sc_links']):.1f}")
    print(f"    Change: {np.mean(stats['later']['sc_links']) - np.mean(stats['early']['sc_links']):.1f}")
    
    print("\nAverage Data Rates (Mbps):")
    print(f"  Satellite-Ground Uplink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sg_uplink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sg_uplink']):.2f} Mbps")
    
    print(f"\n  Satellite-Ground Downlink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sg_downlink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sg_downlink']):.2f} Mbps")
    
    print(f"\n  Satellite-Cloud Uplink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sc_uplink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sc_uplink']):.2f} Mbps")
    
    print(f"\n  Satellite-Cloud Downlink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sc_downlink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sc_downlink']):.2f} Mbps")


if __name__ == "__main__":
    print("通信模块完整2000时隙测试")
    print(f"开始时间: {datetime.now()}")

    # 运行完整2000时隙测试
    success = test_complete_2000_timeslots_communication()

    print(f"\n结束时间: {datetime.now()}")

    if success:
        print("完整2000时隙通信测试成功完成！")

        # 可选：运行原有的详细测试（少量时隙）
        print("\n" + "=" * 100)
        print("运行详细测试（少量时隙样本）...")
        test_communication_matrices_by_timeslot()

        # 可选：运行比较测试
        print("\n" + "=" * 100)
        print("运行时隙间比较测试...")
        compare_communication_across_timeslots()
    else:
        print("测试失败！")