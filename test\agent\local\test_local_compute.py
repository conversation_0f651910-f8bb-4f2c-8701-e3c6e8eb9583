"""
Test suite for local computation simulator
Tests individual components and end-to-end simulation
"""

import sys
import os
from pathlib import Path
import unittest
import numpy as np
import yaml

# Add project root to path
project_root = str(Path(__file__).parent.parent.parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Absolute imports
from src.agent.local.local_compute_simulator import LocalComputeSimulator
from src.agent.local.simulation_result import SimulationResult, PerformanceMetrics, TimeslotResult
from src.env.physics_layer.task_models import Task, TaskType
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus


class TestLocalComputeSimulator(unittest.TestCase):
    """Test cases for LocalComputeSimulator"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Use default config for testing
        config_path = str(Path(project_root) / 'src' / 'env' / 'physics_layer' / 'config.yaml')
        self.simulator = LocalComputeSimulator(config_path=config_path)
        
    def test_initialization(self):
        """Test simulator initialization"""
        self.assertIsNotNone(self.simulator)
        self.assertEqual(self.simulator.num_satellites, 72)
        self.assertEqual(self.simulator.num_users, 420)
        self.assertEqual(self.simulator.timeslot_duration, 3)
        
    def test_config_loading(self):
        """Test configuration loading"""
        config = self.simulator._load_config()
        self.assertIsNotNone(config)
        self.assertIn('system', config)
        self.assertIn('computation', config)
        self.assertIn('communication', config)
        self.assertIn('queuing', config)
        
    def test_component_initialization(self):
        """Test component initialization"""
        self.simulator._initialize_components()
        
        # Check all components are initialized
        self.assertIsNotNone(self.simulator.time_manager)
        self.assertIsNotNone(self.simulator.orbital_updater)
        self.assertIsNotNone(self.simulator.task_generator)
        self.assertIsNotNone(self.simulator.task_distributor)
        self.assertEqual(len(self.simulator.satellites), 72)
        
        # Check task generator has locations loaded
        self.assertEqual(len(self.simulator.task_generator.locations), 420)
        
    def test_satellite_illumination_check(self):
        """Test satellite illumination checking"""
        self.simulator._initialize_components()
        
        # Test for first satellite at first timeslot
        illuminated = self.simulator._check_satellite_illumination(0, 0)
        self.assertIsInstance(illuminated, bool)
        
    def test_single_timeslot_processing(self):
        """Test processing a single timeslot"""
        self.simulator._initialize_components()
        
        # Process first timeslot
        result = self.simulator._process_single_timeslot(0)
        
        # Check result structure
        self.assertIsInstance(result, TimeslotResult)
        self.assertEqual(result.timeslot, 0)
        self.assertGreaterEqual(result.tasks_generated, 0)
        self.assertGreaterEqual(result.tasks_assigned, 0)
        self.assertIsInstance(result.tasks_completed, list)
        self.assertIsInstance(result.tasks_dropped, list)
        
    def test_performance_metrics_calculation(self):
        """Test performance metrics calculation"""
        self.simulator._initialize_components()
        
        # Run a few timeslots to generate data
        for ts in range(5):
            self.simulator._process_single_timeslot(ts)
        
        # Calculate metrics
        metrics = self.simulator._calculate_performance_metrics()
        
        # Check metrics structure
        self.assertIsInstance(metrics, PerformanceMetrics)
        self.assertGreaterEqual(metrics.mean_delay, 0)
        self.assertGreaterEqual(metrics.total_energy_consumed, 0)
        self.assertTrue(0 <= metrics.type1_completion_rate <= 1)
        self.assertTrue(0 <= metrics.type2_completion_rate <= 1)
        self.assertTrue(0 <= metrics.type3_completion_rate <= 1)
        
    def test_short_simulation(self):
        """Test running a short simulation"""
        # Run simulation for 10 timeslots
        result = self.simulator.run_simulation(total_timeslots=10)
        
        # Check result structure
        self.assertIsInstance(result, SimulationResult)
        self.assertGreaterEqual(result.total_tasks_generated, 0)
        self.assertGreaterEqual(result.total_tasks_completed, 0)
        self.assertTrue(0 <= result.overall_completion_rate <= 1)
        self.assertGreaterEqual(result.avg_delay_per_task, 0)
        self.assertGreaterEqual(result.avg_energy_per_task, 0)
        
        # Check satellite statistics
        self.assertEqual(len(result.satellite_statistics), 72)
        
        # Check timeslot results
        self.assertEqual(len(result.timeslot_results), 10)
        
    def test_task_statistics_tracking(self):
        """Test task statistics tracking by type and priority"""
        self.simulator._initialize_components()
        
        # Run a few timeslots
        for ts in range(5):
            self.simulator._process_single_timeslot(ts)
        
        # Check task statistics
        for task_type in TaskType:
            stats = self.simulator.task_stats_by_type[task_type]
            self.assertIn('generated', stats)
            self.assertIn('completed', stats)
            self.assertGreaterEqual(stats['generated'], 0)
            self.assertGreaterEqual(stats['completed'], 0)
            self.assertLessEqual(stats['completed'], stats['generated'])
        
        # Check priority statistics
        for priority in range(1, 11):
            stats = self.simulator.task_stats_by_priority[priority]
            self.assertIn('generated', stats)
            self.assertIn('completed', stats)
            self.assertGreaterEqual(stats['generated'], 0)
            self.assertGreaterEqual(stats['completed'], 0)
            self.assertLessEqual(stats['completed'], stats['generated'])
    
    def test_result_export(self):
        """Test exporting results to JSON"""
        import tempfile
        import json
        
        # Run short simulation
        result = self.simulator.run_simulation(total_timeslots=5)
        
        # Export to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            self.simulator.export_results(result, temp_path)
            
            # Load and verify exported data
            with open(temp_path, 'r') as f:
                data = json.load(f)
            
            self.assertIn('summary', data)
            self.assertIn('completion_by_priority', data)
            self.assertIn('performance_metrics', data)
            self.assertIn('satellite_stats', data)
            
        finally:
            # Clean up
            if os.path.exists(temp_path):
                os.remove(temp_path)


class TestSimulationResult(unittest.TestCase):
    """Test cases for SimulationResult data model"""
    
    def test_result_initialization(self):
        """Test SimulationResult initialization"""
        result = SimulationResult()
        self.assertEqual(result.total_tasks_generated, 0)
        self.assertEqual(result.total_tasks_completed, 0)
        self.assertEqual(result.total_tasks_dropped, 0)
        self.assertEqual(result.overall_completion_rate, 0.0)
        
    def test_result_to_dict(self):
        """Test converting result to dictionary"""
        result = SimulationResult(
            total_tasks_generated=100,
            total_tasks_completed=60,
            total_tasks_dropped=40,
            overall_completion_rate=0.6,
            avg_delay_per_task=10.5,
            avg_energy_per_task=1500.0
        )
        
        data = result.to_dict()
        self.assertIsInstance(data, dict)
        self.assertIn('summary', data)
        self.assertEqual(data['summary']['total_generated'], 100)
        self.assertEqual(data['summary']['total_completed'], 60)
        self.assertEqual(data['summary']['overall_completion_rate'], 0.6)
        
    def test_performance_metrics(self):
        """Test PerformanceMetrics data model"""
        metrics = PerformanceMetrics(
            mean_delay=10.0,
            median_delay=8.0,
            p90_delay=15.0,
            p99_delay=20.0,
            total_energy_consumed=100000.0,
            type1_completion_rate=0.8,
            type2_completion_rate=0.6,
            type3_completion_rate=0.4
        )
        
        data = metrics.to_dict()
        self.assertIsInstance(data, dict)
        self.assertIn('delay_metrics', data)
        self.assertIn('energy_metrics', data)
        self.assertIn('completion_by_type', data)
        self.assertEqual(data['delay_metrics']['mean'], 10.0)
        self.assertEqual(data['completion_by_type']['REALTIME'], 0.8)


def run_performance_test():
    """Run a performance test to measure simulation speed"""
    import time
    
    print("\n" + "="*60)
    print(" Performance Test")
    print("="*60)
    
    config_path = str(Path(project_root) / 'src' / 'env' / 'physics_layer' / 'config.yaml')
    simulator = LocalComputeSimulator(config_path=config_path)
    
    # Test different timeslot counts
    test_cases = [10, 50, 100]
    
    for num_timeslots in test_cases:
        print(f"\nTesting {num_timeslots} timeslots...")
        start_time = time.time()
        
        result = simulator.run_simulation(total_timeslots=num_timeslots)
        
        elapsed = time.time() - start_time
        tasks_per_sec = result.total_tasks_generated / elapsed if elapsed > 0 else 0
        
        print(f"  Time: {elapsed:.2f}s")
        print(f"  Tasks generated: {result.total_tasks_generated}")
        print(f"  Tasks completed: {result.total_tasks_completed}")
        print(f"  Completion rate: {result.overall_completion_rate:.1%}")
        print(f"  Processing speed: {tasks_per_sec:.0f} tasks/sec")
    
    print("\n" + "="*60)


if __name__ == '__main__':
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_performance_test()