"""
测试任务生成器并生成任务量热图
运行前100个时隙，统计每个地理位置的任务生成量，并生成热图
"""

import sys
import os
from pathlib import Path
import numpy as np
import json
import folium
from folium.plugins import HeatMap
import pandas as pd
from typing import Dict, List, Tuple

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.env.physics_layer.task_generator import TaskGenerator, Location

def run_task_generation(num_timeslots: int = 100) -> Dict:
    """
    运行任务生成器指定时隙数
    
    Args:
        num_timeslots: 要运行的时隙数量
    
    Returns:
        包含任务生成统计的字典
    """
    # 设置随机种子以保证结果可重现
    np.random.seed(42)
    
    # 创建任务生成器
    generator = TaskGenerator()
    
    # 加载地理位置数据
    csv_path = Path(__file__).parent.parent.parent / 'src' / 'env' / 'env_data' / 'global_ground_stations.csv'
    generator.load_locations_from_csv(str(csv_path))
    
    # 统计每个位置的任务生成量
    location_task_counts = {}  # location_id -> total_tasks
    location_info = {}  # location_id -> (lat, lon, scale, type)
    
    # 记录每个时隙的总任务数
    timeslot_totals = []
    
    # 初始化位置信息
    for location in generator.locations:
        location_id = location.location_id
        location_task_counts[location_id] = 0
        location_info[location_id] = {
            'latitude': location.latitude,
            'longitude': location.longitude,
            'scale': location.scale,
            'functional_type': location.functional_type,
            'geography': location.geography,
            'lambda': generator.calculate_lambda(location)
        }
    
    print(f"开始运行任务生成模拟（前{num_timeslots}个时隙）...")
    
    # 运行指定数量的时隙
    for timeslot in range(num_timeslots):
        current_time = timeslot * 5  # 每个时隙5秒
        timeslot_total = 0
        
        # 为每个位置生成任务
        for location in generator.locations:
            tasks = generator.generate_tasks_for_location(location, current_time)
            num_tasks = len(tasks)
            location_task_counts[location.location_id] += num_tasks
            timeslot_total += num_tasks
        
        timeslot_totals.append(timeslot_total)
        
        if (timeslot + 1) % 10 == 0:
            print(f"  已处理时隙 {timeslot + 1}/{num_timeslots}, 本时隙生成 {timeslot_total} 个任务")
    
    # 计算统计信息
    total_tasks = sum(location_task_counts.values())
    avg_per_location = total_tasks / len(location_task_counts)
    avg_per_timeslot = total_tasks / num_timeslots
    
    print(f"\n=== 任务生成统计 ===")
    print(f"总任务数: {total_tasks}")
    print(f"平均每位置: {avg_per_location:.2f}")
    print(f"平均每时隙: {avg_per_timeslot:.2f}")
    print(f"最大单位置生成量: {max(location_task_counts.values())}")
    print(f"最小单位置生成量: {min(location_task_counts.values())}")
    
    return {
        'location_task_counts': location_task_counts,
        'location_info': location_info,
        'timeslot_totals': timeslot_totals,
        'total_tasks': total_tasks,
        'num_timeslots': num_timeslots
    }

def generate_heatmap(stats: Dict, output_file: str = 'task_generation_heatmap.html'):
    """
    生成任务量热图
    
    Args:
        stats: 任务生成统计数据
        output_file: 输出HTML文件名
    """
    print("\n生成任务量热图...")
    
    # 创建基础地图
    m = folium.Map(location=[0, 0], zoom_start=2, tiles='CartoDB dark_matter')
    
    # 准备热图数据
    heat_data = []
    location_task_counts = stats['location_task_counts']
    location_info = stats['location_info']
    
    # 归一化任务数量用于热图权重
    max_count = max(location_task_counts.values())
    min_count = min(location_task_counts.values())
    
    # 为每个位置添加标记和热图点
    for loc_id, task_count in location_task_counts.items():
        info = location_info[loc_id]
        lat = info['latitude']
        lon = info['longitude']
        
        # 归一化权重（0-1之间）
        if max_count > min_count:
            weight = (task_count - min_count) / (max_count - min_count)
        else:
            weight = 0.5
        
        # 添加到热图数据
        heat_data.append([lat, lon, weight])
        
        # 根据任务量确定颜色
        if task_count > 800:
            color = 'red'
        elif task_count > 600:
            color = 'orange'
        elif task_count > 400:
            color = 'yellow'
        elif task_count > 200:
            color = 'lightgreen'
        else:
            color = 'green'
        
        # 添加圆形标记
        folium.CircleMarker(
            location=[lat, lon],
            radius=3 + (weight * 7),  # 根据任务量调整大小
            popup=f"""
            Location ID: {loc_id}<br>
            Tasks: {task_count}<br>
            Scale: {info['scale']}<br>
            Type: {info['functional_type']}<br>
            Geography: {info['geography']}<br>
            Lambda: {info['lambda']}<br>
            Avg per timeslot: {task_count/stats['num_timeslots']:.2f}
            """,
            color=color,
            fill=True,
            fillOpacity=0.7
        ).add_to(m)
    
    # 添加热力图层
    HeatMap(
        heat_data,
        min_opacity=0.3,
        max_zoom=18,
        radius=25,
        blur=15,
        gradient={
            0.0: 'blue',
            0.2: 'lime',
            0.4: 'yellow',
            0.6: 'orange',
            0.8: 'red',
            1.0: 'darkred'
        }
    ).add_to(m)
    
    # 添加图例
    legend_html = '''
    <div style="position: fixed; 
                bottom: 50px; right: 50px; width: 200px; height: 180px; 
                background-color: white; border:2px solid grey; z-index:9999; 
                font-size:14px; padding: 10px">
    <p style="margin: 0;"><b>任务生成量热图</b></p>
    <p style="margin: 5px 0;">前100个时隙统计</p>
    <p style="margin: 5px 0;">总任务数: {}</p>
    <p style="margin: 5px 0;"><span style="color:red;">●</span> > 800 tasks</p>
    <p style="margin: 5px 0;"><span style="color:orange;">●</span> 600-800 tasks</p>
    <p style="margin: 5px 0;"><span style="color:yellow;">●</span> 400-600 tasks</p>
    <p style="margin: 5px 0;"><span style="color:lightgreen;">●</span> 200-400 tasks</p>
    <p style="margin: 5px 0;"><span style="color:green;">●</span> < 200 tasks</p>
    </div>
    '''.format(stats['total_tasks'])
    
    m.get_root().html.add_child(folium.Element(legend_html))
    
    # 保存地图
    output_path = Path(__file__).parent / output_file
    m.save(str(output_path))
    print(f"热图已保存到: {output_path}")
    
    return m

def analyze_distribution(stats: Dict):
    """
    分析任务分布情况
    
    Args:
        stats: 任务生成统计数据
    """
    print("\n=== 任务分布分析 ===")
    
    location_info = stats['location_info']
    location_task_counts = stats['location_task_counts']
    
    # 按地理类型分组
    land_tasks = 0
    ocean_tasks = 0
    
    # 按规模分组
    scale_tasks = {'Small': 0, 'Medium': 0, 'Large': 0}
    scale_counts = {'Small': 0, 'Medium': 0, 'Large': 0}
    
    # 按功能类型分组
    func_tasks = {'Normal': 0, 'Industrial': 0, 'DelaySensitive': 0}
    func_counts = {'Normal': 0, 'Industrial': 0, 'DelaySensitive': 0}
    
    for loc_id, task_count in location_task_counts.items():
        info = location_info[loc_id]
        
        # 地理类型
        if info['geography'] == 'Land':
            land_tasks += task_count
        else:
            ocean_tasks += task_count
        
        # 规模
        if info['scale'] in scale_tasks:
            scale_tasks[info['scale']] += task_count
            scale_counts[info['scale']] += 1
        
        # 功能类型
        if info['functional_type'] in func_tasks:
            func_tasks[info['functional_type']] += task_count
            func_counts[info['functional_type']] += 1
    
    print(f"\n地理类型分布:")
    print(f"  陆地任务: {land_tasks} ({land_tasks/stats['total_tasks']*100:.1f}%)")
    print(f"  海洋任务: {ocean_tasks} ({ocean_tasks/stats['total_tasks']*100:.1f}%)")
    
    print(f"\n规模分布:")
    for scale, count in scale_tasks.items():
        if scale_counts[scale] > 0:
            avg = count / scale_counts[scale] / stats['num_timeslots']
            print(f"  {scale}: {count} tasks, {scale_counts[scale]} locations, avg {avg:.2f} tasks/location/timeslot")
    
    print(f"\n功能类型分布:")
    for func, count in func_tasks.items():
        if func_counts[func] > 0:
            avg = count / func_counts[func] / stats['num_timeslots']
            print(f"  {func}: {count} tasks, {func_counts[func]} locations, avg {avg:.2f} tasks/location/timeslot")
    
    # 找出热点位置（任务生成最多的前10个位置）
    sorted_locations = sorted(location_task_counts.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n热点位置（前10个）:")
    for i, (loc_id, count) in enumerate(sorted_locations[:10], 1):
        info = location_info[loc_id]
        avg = count / stats['num_timeslots']
        print(f"  {i}. Location {loc_id}: {count} tasks (avg {avg:.2f}/timeslot)")
        print(f"     位置: ({info['latitude']:.1f}, {info['longitude']:.1f}), "
              f"规模: {info['scale']}, 类型: {info['functional_type']}")
    
    # 时隙分析
    timeslot_totals = stats['timeslot_totals']
    print(f"\n时隙任务生成统计:")
    print(f"  平均每时隙: {np.mean(timeslot_totals):.2f}")
    print(f"  标准差: {np.std(timeslot_totals):.2f}")
    print(f"  最大值: {max(timeslot_totals)}")
    print(f"  最小值: {min(timeslot_totals)}")

def main():
    """主函数"""
    print("="*60)
    print("任务生成器测试与热图生成")
    print("="*60)
    
    # 运行任务生成（前100个时隙）
    stats = run_task_generation(num_timeslots=100)
    
    # 生成热图
    generate_heatmap(stats)
    
    # 分析分布
    analyze_distribution(stats)
    
    # 保存统计数据到JSON
    output_stats = {
        'total_tasks': stats['total_tasks'],
        'num_timeslots': stats['num_timeslots'],
        'num_locations': len(stats['location_task_counts']),
        'avg_per_location': stats['total_tasks'] / len(stats['location_task_counts']),
        'avg_per_timeslot': stats['total_tasks'] / stats['num_timeslots'],
        'max_location_tasks': max(stats['location_task_counts'].values()),
        'min_location_tasks': min(stats['location_task_counts'].values())
    }
    
    output_path = Path(__file__).parent / 'task_generation_stats.json'
    with open(output_path, 'w') as f:
        json.dump(output_stats, f, indent=2)
    print(f"\n统计数据已保存到: {output_path}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()