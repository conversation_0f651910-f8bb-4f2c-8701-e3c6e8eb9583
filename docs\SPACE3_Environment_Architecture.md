# SPACE3 环境架构文档

## 目录
- [1. 总体架构概览](#1-总体架构概览)
- [2. 环境层次结构](#2-环境层次结构)
- [3. 核心组件详解](#3-核心组件详解)
- [4. 接口定义与调用关系](#4-接口定义与调用关系)
- [5. 数据流与交互机制](#5-数据流与交互机制)
- [6. 配置管理](#6-配置管理)
- [7. 性能与优化](#7-性能与优化)

---

## 1. 总体架构概览

### 1.1 系统定位
SPACE3是一个专为Transformer-MAPPO强化学习算法设计的LEO卫星星座边缘计算仿真环境，基于PettingZoo框架实现多智能体并行环境。

### 1.2 环境规模
- **卫星数量**: 72颗LEO卫星
- **地面用户**: 420个用户终端
- **云计算中心**: 5个区域云中心
- **仿真时长**: 1441个时隙（7205秒）
- **智能体**: 72个卫星智能体并行决策

### 1.3 架构设计原则
```
┌─────────────────────────────────────────────────────────┐
│                    PettingZoo Interface                 │
│                (Parallel Multi-Agent Env)               │
├─────────────────────────────────────────────────────────┤
│                  SPACE3 Environment Layer               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ Observation │ │   Action    │ │     Reward          │ │
│  │  Builder    │ │  Handler    │ │   Calculator        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    Computing Layer                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ Satellite   │ │    Cloud    │ │      Task           │ │
│  │  Compute    │ │   Compute   │ │   Generator         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    Physics Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │  Orbital    │ │Communication│ │      Time           │ │
│  │ Dynamics    │ │  Manager    │ │    Manager          │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                   Foundation Layer                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │    Error    │ │   Logging   │ │      Data           │ │
│  │  Handling   │ │   System    │ │    Models           │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

---

## 2. 环境层次结构

### 2.1 层次职责

#### Foundation Layer (基础层)
**位置**: `src/env/Foundation_Layer/`  
**职责**: 提供系统基础服务和数据结构  
**核心模块**:
- `time_manager.py`: 统一时间管理和同步
- `error_handling.py`: 结构化异常处理
- `logging_config.py`: 分级日志配置

#### Physics Layer (物理层) 
**位置**: `src/env/physics_layer/`  
**职责**: 模拟卫星星座的物理特性和通信特性  
**核心模块**:
- `orbital.py`: LEO卫星轨道动力学计算
- `communication_refactored.py`: 激光/RF链路通信建模
- `task_generator.py`: 基于地理位置的任务生成
- `task_models.py`: 任务和位置数据结构定义

#### Computing Layer (计算层)
**位置**: `src/env/satellite_cloud/`  
**职责**: 实现分布式边缘计算和云计算处理  
**核心模块**:
- `satellite_compute.py`: 卫星边缘计算节点（DPSQ调度）
- `cloud_compute.py`: 云计算中心（高性能并行处理）
- `compute_models.py`: 计算任务和资源模型

#### Environment Layer (环境层)
**位置**: `src/env/space_env/`  
**职责**: 强化学习环境接口和智能体交互逻辑  
**核心模块**:
- `space3_pettingzoo_env.py`: 主环境类，PettingZoo接口实现
- `observation_builder.py`: 多模态观测空间构建
- `sequence_action_handler.py`: 序列动作验证和执行
- `reward_calculator.py`: 多维度奖励计算

### 2.2 依赖关系图

```mermaid
graph TD
    subgraph "Foundation Layer"
        TM[TimeManager]
        EH[ErrorHandling]
        LOG[LoggingConfig]
    end
    
    subgraph "Physics Layer"
        ORB[OrbitalUpdater]
        COMM[CommunicationManager]
        TG[TaskGenerator]
        TM_P[TaskModels]
    end
    
    subgraph "Computing Layer"
        SC[SatelliteCompute]
        CC[CloudCompute]
        CM[ComputeModels]
    end
    
    subgraph "Environment Layer"
        ENV[Space3PettingZooEnv]
        OB[ObservationBuilder]
        AH[ActionHandler]
        RC[RewardCalculator]
    end
    
    %% 基础依赖
    ORB --> TM
    COMM --> TM
    COMM --> ORB
    SC --> TM
    SC --> ORB
    SC --> COMM
    TG --> TM_P
    
    %% 环境层依赖
    ENV --> ORB
    ENV --> COMM
    ENV --> TG
    ENV --> SC
    ENV --> CC
    ENV --> OB
    ENV --> AH
    ENV --> RC
    
    %% 数据模型依赖
    SC --> CM
    CC --> CM
    TG --> TM_P
```

---

## 3. 核心组件详解

### 3.1 主环境类 - Space3PettingZooEnv

**文件位置**: `src/env/space_env/space3_pettingzoo_env.py`

#### 3.1.1 类继承关系
```python
class Space3PettingZooEnv(ParallelEnv):  # PettingZoo并行环境
```

#### 3.1.2 初始化流程
```python
def __init__(self, config_path: str = None):
    # 1. 配置加载和验证
    self._load_and_validate_config(config_path)
    
    # 2. 初始化依赖模块
    self._initialize_modules()
    
    # 3. 设置环境参数
    self._setup_environment_params()
    
    # 4. 定义动作和观测空间
    self._setup_spaces()
    
    # 5. 初始化环境组件
    self._initialize_components()
```

#### 3.1.3 关键属性
- **智能体管理**: `self.agents`, `self.possible_agents`
- **模块实例**: `self.orbital_updater`, `self.communication_manager`, `self.task_generator`
- **计算节点**: `self.satellites`, `self.cloud_nodes`
- **状态变量**: `self.current_timeslot`, `self.task_queues`, `self.current_visibility_matrices`

#### 3.1.4 PettingZoo接口实现
```python
def reset(self, seed=None, options=None) -> Tuple[Dict, Dict]:
    """环境重置，返回初始观测和信息"""
    
def step(self, actions: Dict[str, np.ndarray]) -> Tuple[Dict, Dict, Dict, Dict, Dict]:
    """执行一步，返回观测、奖励、终止、截断、信息"""
    
def render(self):
    """环境渲染（可选）"""
    
def close(self):
    """资源清理"""
```

### 3.2 观测构建器 - ObservationBuilder

**文件位置**: `src/env/space_env/observation_builder.py`

#### 3.2.1 观测空间结构
```python
observation_space = spaces.Dict({
    'own_state': spaces.Box(shape=(12,)),           # 卫星自身状态
    'task_queue': spaces.Box(shape=(20, 8)),        # 任务队列（padding到20）
    'task_mask': spaces.Box(shape=(20,)),           # 任务掩码（真实/padding）
    'action_mask': spaces.Box(shape=(20, 16)),      # 动作可行性掩码
    'neighbor_states': spaces.Box(shape=(10, 5)),   # 邻居卫星状态
    'comm_quality': spaces.Box(shape=(10,)),        # 通信链路质量
    'time_info': spaces.Box(shape=(3,))             # 时间进度信息
})
```

#### 3.2.2 特征编码方法
```python
def _encode_task(self, task, norm_params: Dict) -> np.ndarray:
    """任务特征编码 [8维]"""
    # [任务类型, 优先级, 数据大小, 复杂度, 剩余时间, 纬度, 经度, 紧急程度]
    
def _build_own_state(self, satellite, current_timeslot, max_timeslots) -> np.ndarray:
    """卫星状态编码 [12维]"""  
    # [CPU利用率, 内存利用率, 电池电量, 队列长度, 等待任务数, 完成任务数, 
    #  丢弃任务数, 能量消耗, 光照状态, 时间进度, 剩余时间, 负载指标]
```

#### 3.2.3 归一化策略
所有观测值归一化到 [0, 1] 范围，归一化参数可配置：
```python
normalization_params = {
    'task': {'data_size_max': 100.0, 'complexity_max': 1000.0, ...},
    'state': {'cpu_max': 100.0, 'battery_max': 100.0, ...},
    'neighbor': {'distance_max': 5500.0, 'comm_rate_max': 1000.0, ...}
}
```

### 3.3 序列动作处理器 - SequenceActionHandler

**文件位置**: `src/env/space_env/sequence_action_handler.py`

#### 3.3.1 动作空间设计
```python
action_space = spaces.MultiDiscrete([16] * 20)  # 20个任务位置，每个位置16种选择
```

#### 3.3.2 动作目标映射
```python
action_map = {
    0: ActionTarget(target_type='local'),                    # 本地处理
    1-10: ActionTarget(target_type='neighbor', index=i),     # 邻居卫星 
    11-15: ActionTarget(target_type='cloud', index=j)        # 云中心
}
```

#### 3.3.3 动作验证流程
```python
def validate_batch_actions(self, all_actions, all_task_queues, global_visibility):
    """批量验证所有智能体的动作序列"""
    # 1. 解析动作序列
    # 2. 检查可见性约束
    # 3. 验证资源可用性
    # 4. 负载均衡检查
    # 5. 返回验证结果
```

### 3.4 奖励计算器 - RewardCalculator

**文件位置**: `src/env/space_env/reward_calculator.py`

#### 3.4.1 多维度奖励组成
```python
@dataclass
class RewardComponents:
    task_completion: float         # 任务完成奖励 (+)
    task_drop_penalty: float       # 任务丢弃惩罚 (-)
    delay_penalty: float           # 延迟惩罚 (-)
    energy_penalty: float          # 能耗惩罚 (-)
    load_balance_bonus: float      # 负载均衡奖励 (+/-)
    invalid_action_penalty: float  # 无效动作惩罚 (-)
    cooperation_bonus: float       # 协作奖励 (+)
    total: float                   # 总奖励
```

#### 3.4.2 奖励计算公式
```python
# 任务完成奖励
completion_reward = completed_tasks * weight_completion * priority_bonus

# 延迟惩罚（非线性）
delay_penalty = weight_delay * (normalized_delay²) * 100

# 负载均衡奖励
if load_variance < target_variance:
    balance_bonus = weight_balance * (1 - load_variance/target_variance)
else:
    balance_bonus = -weight_balance * (load_variance - target_variance) * 2
```

---

## 4. 接口定义与调用关系

### 4.1 时间管理接口 (TimeManager)

#### 4.1.1 核心接口
```python
class TimeManager:
    def get_time_context(self, simulation_step: int) -> TimeContext:
        """获取完整时间上下文"""
        return TimeContext(
            simulation_step=simulation_step,
            simulation_time=simulation_step * self.timeslot_duration,
            physical_time=self.start_time + timedelta(seconds=simulation_time),
            timeslot_duration=self.timeslot_duration
        )
    
    def is_valid_step(self, simulation_step: int) -> bool:
        """验证时间步有效性"""
        return 0 <= simulation_step < self.total_timeslots
```

#### 4.1.2 工厂函数
```python
def create_time_manager_from_config(config: dict) -> TimeManager:
    """从配置创建时间管理器"""
    return TimeManager(
        start_time=datetime.fromisoformat(config['system']['start_time']),
        timeslot_duration=config['system']['timeslot_duration_s'],
        total_timeslots=config['system']['num_timeslots']
    )
```

#### 4.1.3 调用关系
```python
# 在环境主循环中的调用
time_context = self.time_manager.get_time_context(self.current_timeslot)

# 在轨道更新器中的调用  
satellites = self.orbital_updater.get_satellites_at_time(timeslot)

# 在通信管理器中的调用
comm_matrix = self.communication_manager.get_isl_communication_matrix(timeslot)
```

### 4.2 轨道动力学接口 (OrbitalUpdater)

#### 4.2.1 卫星状态获取
```python
def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]:
    """获取指定时间步的所有卫星状态"""
    # 返回: {'sat_111': Satellite(...), 'sat_112': Satellite(...), ...}
```

#### 4.2.2 可见性矩阵计算
```python
def build_visibility_matrices(self, satellites: Dict[str, Satellite], time_step: int) -> Dict:
    """构建三种可见性矩阵"""
    return {
        'satellite_to_satellite': sat_to_sat_matrix,    # (72, 72)
        'satellite_to_ground': sat_to_ground_matrix,    # (72, 420)  
        'satellite_to_cloud': sat_to_cloud_matrix       # (72, 5)
    }
```

#### 4.2.3 距离计算
```python
def calculate_distance_matrix(self, positions1: np.ndarray, positions2: np.ndarray) -> np.ndarray:
    """向量化距离计算"""
    # 使用ECEF坐标系，支持大规模矩阵运算
    # positions1: (N, 3), positions2: (M, 3) -> distance_matrix: (N, M)
```

#### 4.2.4 在环境中的调用
```python
# 环境初始化时
self.orbital_updater = OrbitalUpdater()

# 每个时隙更新
visibility_matrices = self._get_real_visibility_matrices(self.current_timeslot)

# 内部实现
def _get_real_visibility_matrices(self, timeslot: int) -> Dict:
    satellites = self.orbital_updater.get_satellites_at_time(timeslot)
    return self.orbital_updater.build_visibility_matrices(satellites, timeslot)
```

### 4.3 通信管理接口 (CommunicationManager)

#### 4.3.1 激光链路通信矩阵
```python
def get_isl_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]:
    """获取卫星间激光通信矩阵"""
    return {
        'data_rate_bps': np.ndarray,         # (72, 72) - 数据传输速率
        'snr_db': np.ndarray,                # (72, 72) - 信噪比
        'distance_km': np.ndarray,           # (72, 72) - 距离
        'visibility': np.ndarray,            # (72, 72) - 可见性
        'propagation_delay_ms': np.ndarray,  # (72, 72) - 传播延迟
        'satellite_ids': List[str]           # 卫星ID列表
    }
```

#### 4.3.2 RF链路通信矩阵  
```python
def get_satellite_ground_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]:
    """获取卫星-地面RF通信矩阵"""
    return {
        'uplink_data_rate_bps': np.ndarray,    # (72, 420) - 上行速率
        'uplink_snr_db': np.ndarray,           # (72, 420) - 上行SNR
        'downlink_data_rate_bps': np.ndarray,  # (72, 420) - 下行速率  
        'downlink_snr_db': np.ndarray,         # (72, 420) - 下行SNR
        'distance_km': np.ndarray,             # (72, 420) - 距离
        'visibility': np.ndarray               # (72, 420) - 可见性
    }
```

#### 4.3.3 通信质量计算
```python
def calculate_data_rate(self, snr_db: np.ndarray, bandwidth_hz: float) -> np.ndarray:
    """Shannon容量公式计算数据速率"""
    # C = B * log2(1 + SNR) * coding_efficiency
    snr_linear = 10**(snr_db / 10)
    return bandwidth_hz * np.log2(1 + snr_linear) * self.coding_efficiency
```

#### 4.3.4 在环境中的调用
```python
# 环境初始化
self.communication_manager = CommunicationManager(self.config)

# 获取通信矩阵
self.current_comm_matrix = self.communication_manager.get_communication_matrix(
    self.current_visibility_matrices
)
```

### 4.4 任务生成接口 (TaskGenerator)

#### 4.4.1 位置数据加载
```python
def load_locations_from_csv(self, csv_file_path: str) -> None:
    """从CSV加载地理位置数据"""
    # 解析格式: location_id, latitude, longitude, geographical_type, scale_type, functional_type
```

#### 4.4.2 任务生成
```python
def generate_tasks_for_timeslot(self, timeslot: int) -> Dict[int, List[Task]]:
    """为所有位置生成指定时隙的任务"""
    tasks_by_location = {}
    for location in self.locations:
        lambda_rate = self.calculate_lambda(location)
        num_tasks = np.random.poisson(lambda_rate)
        tasks = [self._create_task(location, timeslot) for _ in range(num_tasks)]
        tasks_by_location[location.location_id] = tasks
    return tasks_by_location
```

#### 4.4.3 任务类型配置
```python
TASK_TYPE_CONFIG = {
    TaskType.REALTIME: {
        'data_size_mb_range': (10, 20),
        'complexity_cycles_per_bit': 100,
        'deadline_seconds': 5,
        'priority_range': (4, 5)
    },
    TaskType.NORMAL: {
        'data_size_mb_range': (20, 50), 
        'complexity_cycles_per_bit': 200,
        'deadline_seconds': 15,
        'priority_range': (2, 4)
    },
    TaskType.COMPUTE_INTENSIVE: {
        'data_size_mb_range': (50, 100),
        'complexity_cycles_per_bit': 500, 
        'deadline_seconds': 30,
        'priority_range': (1, 3)
    }
}
```

#### 4.4.4 在环境中的调用
```python
# 环境初始化时
self.task_generator = TaskGenerator(None)
ground_stations_path = self._get_config_path('ground_stations')
self.task_generator.load_locations_from_csv(ground_stations_path)

# 每个时隙生成新任务
new_tasks_by_location = self.task_generator.generate_tasks_for_timeslot(self.current_timeslot)
all_new_tasks = []
for location_id, tasks in new_tasks_by_location.items():
    all_new_tasks.extend(tasks)
self._distribute_new_tasks(all_new_tasks)
```

### 4.5 卫星计算接口 (SatelliteCompute)

#### 4.5.1 任务管理
```python
def add_task(self, task: Task) -> bool:
    """添加任务到处理队列"""
    # 1. 检查队列容量
    # 2. 验证能量可行性  
    # 3. 转换为ComputeTask
    # 4. 添加到队列
    # 返回: 是否成功添加

def process_timeslot(self, duration: float, illuminated: bool) -> ProcessingResult:
    """处理一个时隙的任务"""
    # 1. 更新电池状态（考虑太阳能充电）
    # 2. DPSQ动态优先级调度
    # 3. 并行任务处理（最多200个）
    # 4. 能量消耗计算
    # 5. 返回完成的任务和性能指标
```

#### 4.5.2 状态查询
```python
def get_queue_status(self) -> Dict[str, Any]:
    """获取队列状态"""
    return {
        'queue_length': len(self.task_queue),
        'num_processing': len(self.processing_tasks),
        'num_waiting': len([t for t in self.task_queue if t.status == TaskStatus.WAITING]),
        'battery_level': self.battery.get_charge_percentage(),
        'cpu_utilization': self.get_cpu_utilization(),
        'can_accept_tasks': self.can_accept_new_task()
    }

def get_energy_status(self) -> Dict[str, Any]:
    """获取能量状态"""
    return {
        'battery_capacity_j': self.battery.capacity,
        'current_charge_j': self.battery.current_charge,
        'battery_level': self.battery.get_charge_percentage(),
        'total_consumed': self.energy_consumed,
        'in_sunlight': self.in_sunlight,
        'can_accept_tasks': self.can_accept_new_task()
    }

def get_statistics(self) -> Dict[str, Any]:
    """获取综合统计"""
    return {
        'total_completed': self.total_completed,
        'total_dropped': self.total_dropped,
        'total_energy_consumed': self.energy_consumed,
        'avg_utilization': self.get_cpu_utilization(),
        'avg_processing_time': self.calculate_avg_processing_time(),
        'current_load': len(self.task_queue) + len(self.processing_tasks)
    }
```

#### 4.5.3 DPSQ调度算法
```python
def calculate_dynamic_priority_score(self, task: ComputeTask, current_time: float) -> float:
    """动态优先级评分"""
    # 优先级因子 f_p(P_i)
    priority_factor = task.priority / 5.0
    
    # 截止时间因子 f_d(D_i, t)  
    remaining_time = max(0, task.deadline - current_time)
    deadline_factor = 1.0 / (1.0 + remaining_time/60.0)
    
    # 复杂度因子 f_c(S_i, C_i)
    complexity_factor = task.complexity / (task.data_size_mb * 1e9)
    
    # 综合评分
    score = (self.w_p * priority_factor + 
             self.w_d * deadline_factor - 
             self.w_c * complexity_factor)
    return score
```

#### 4.5.4 在环境中的调用
```python
# 环境初始化 - 创建72个卫星计算节点
for i in range(self.num_satellites):
    sat_id = f"sat_{111 + i}"
    self.satellites[sat_id] = SatelliteCompute(
        satellite_id=sat_id,
        config=self.config
    )

# 动作执行 - 任务分配
success = self.satellites[agent_id].add_task(task)

# 时隙处理 - 计算执行
for satellite in self.satellites.values():
    duration = self.timeslot_duration
    illuminated = True  # 简化假设
    satellite.process_timeslot(duration, illuminated)

# 状态查询 - 观测构建
queue_status = self.satellites[agent_id].get_queue_status()
energy_status = self.satellites[agent_id].get_energy_status() 
statistics = self.satellites[agent_id].get_statistics()
```

### 4.6 云计算接口 (CloudCompute)

#### 4.6.1 任务处理
```python
def add_task(self, task: Task, arrival_time: float = 0.0) -> bool:
    """添加任务到云处理队列"""
    # 云计算容量远大于卫星，很少拒绝任务
    
def process_batch(self, duration: float) -> List[ComputeTask]:
    """批量并行处理任务"""
    # 1. 高并行度处理（无能量约束）
    # 2. 使用配置的调度策略（FIFO/PRIORITY/SJF）
    # 3. 返回完成的任务列表
```

#### 4.6.2 容量管理
```python
def get_processing_capacity(self) -> Dict[str, Any]:
    """获取处理容量信息"""
    return {
        'total_capacity': self.max_parallel_tasks,
        'available_capacity': self.max_parallel_tasks - len(self.processing_tasks),
        'utilization_rate': len(self.processing_tasks) / self.max_parallel_tasks,
        'queue_utilization': len(self.task_queue) / self.queue_capacity
    }
```

#### 4.6.3 在环境中的调用
```python
# 环境初始化 - 创建5个云计算中心
for i in range(self.num_clouds):
    cloud_id = f"cloud_{i}"
    self.cloud_nodes[cloud_id] = CloudCompute(
        cloud_id=cloud_id,
        config=self.config
    )

# 动作执行 - 任务卸载到云
self.cloud_nodes[cloud_id].add_task(task)

# 时隙处理 - 云计算执行
for cloud in self.cloud_nodes.values():
    cloud.process_batch(self.timeslot_duration)
```

---

## 5. 数据流与交互机制

### 5.1 环境主循环数据流

```mermaid
sequenceDiagram
    participant Agent as RL Agent
    participant Env as Space3Env
    participant Obs as ObservationBuilder
    participant Act as ActionHandler
    participant Sat as SatelliteCompute
    participant Cloud as CloudCompute
    participant Orb as OrbitalUpdater
    participant Comm as CommunicationManager
    participant Task as TaskGenerator
    participant Reward as RewardCalculator

    Note over Agent, Reward: Environment Reset Phase
    Agent->>Env: reset()
    Env->>Orb: get_satellites_at_time(0)
    Orb-->>Env: satellite_positions
    Env->>Comm: get_communication_matrix(visibility)
    Comm-->>Env: comm_matrix
    Env->>Task: generate_tasks_for_timeslot(0)
    Task-->>Env: initial_tasks
    Env->>Obs: build_all_observations()
    Obs-->>Env: observations
    Env-->>Agent: observations, infos

    loop Every Timeslot
        Note over Agent, Reward: Decision Phase
        Agent->>Env: step(actions)
        
        Note over Env, Act: Action Processing
        Env->>Act: validate_batch_actions(actions, queues, visibility)
        Act-->>Env: validated_actions
        
        Note over Env, Cloud: Task Execution
        par Satellite Processing
            Env->>Sat: add_task(task) for each assignment
            Env->>Sat: process_timeslot(duration, illuminated)
            Sat-->>Env: processing_results
        and Cloud Processing  
            Env->>Cloud: add_task(task) for cloud assignments
            Env->>Cloud: process_batch(duration)
            Cloud-->>Env: cloud_results
        end
        
        Note over Env, Task: State Update
        Env->>Orb: get_satellites_at_time(current_timeslot)
        Orb-->>Env: updated_positions
        Env->>Comm: get_communication_matrix(new_visibility)
        Comm-->>Env: updated_comm_matrix
        Env->>Task: generate_tasks_for_timeslot(current_timeslot)
        Task-->>Env: new_tasks
        
        Note over Env, Reward: Reward & Observation
        Env->>Reward: calculate_rewards(results, states, statistics)
        Reward-->>Env: rewards
        Env->>Obs: build_all_observations()
        Obs-->>Env: new_observations
        
        Env-->>Agent: observations, rewards, dones, truncations, infos
    end
```

### 5.2 任务分配与执行流程

```mermaid
flowchart TD
    A[智能体动作序列] --> B[ActionHandler验证]
    B --> C{动作有效?}
    C -->|是| D[解析目标类型]
    C -->|否| E[记录无效动作惩罚]
    
    D --> F{目标类型}
    F -->|local| G[本地处理]
    F -->|neighbor| H[邻居卫星]
    F -->|cloud| I[云中心]
    
    G --> J[SatelliteCompute.add_task]
    H --> K[目标卫星.add_task]
    I --> L[CloudCompute.add_task]
    
    J --> M{队列容量?}
    K --> N{邻居可见?}
    L --> O{云连接?}
    
    M -->|足够| P[添加到队列]
    M -->|不足| Q[拒绝任务]
    N -->|可见| P
    N -->|不可见| Q
    O -->|连接| R[添加到云队列]
    O -->|断开| Q
    
    P --> S[DPSQ调度处理]
    R --> T[云并行处理]
    Q --> U[任务丢弃]
    
    S --> V[能量消耗]
    T --> W[高性能处理]
    U --> X[惩罚计算]
    
    V --> Y[更新统计]
    W --> Y
    X --> Y
    
    Y --> Z[返回处理结果]
```

### 5.3 观测空间构建流程

```mermaid
flowchart LR
    subgraph "数据源"
        A[卫星状态]
        B[任务队列]
        C[可见性矩阵]
        D[通信矩阵]
        E[时间信息]
    end
    
    subgraph "特征编码"
        F[_build_own_state]
        G[_build_task_queue]
        H[_build_action_mask]
        I[_build_neighbor_states]
        J[_build_comm_quality]
        K[_build_time_info]
    end
    
    subgraph "归一化处理"
        L[状态归一化 [0,1]]
        M[任务特征归一化]
        N[掩码处理]
        O[邻居状态归一化]
        P[通信质量归一化]
        Q[时间进度归一化]
    end
    
    subgraph "观测输出"
        R[own_state: 12维]
        S[task_queue: 20×8矩阵]
        T[task_mask: 20维]
        U[action_mask: 20×16矩阵]
        V[neighbor_states: 10×5矩阵]
        W[comm_quality: 10维]
        X[time_info: 3维]
    end
    
    A --> F --> L --> R
    B --> G --> M --> S
    B --> G --> N --> T
    C --> H --> N --> U
    C --> I --> O --> V
    D --> J --> P --> W
    E --> K --> Q --> X
```

### 5.4 奖励计算机制

```mermaid
flowchart TD
    A[动作执行结果] --> B[RewardCalculator]
    C[卫星状态] --> B
    D[任务统计] --> B
    
    B --> E[任务完成奖励]
    B --> F[任务丢弃惩罚]
    B --> G[延迟惩罚]
    B --> H[能耗惩罚]
    B --> I[负载均衡奖励]
    B --> J[无效动作惩罚]
    B --> K[协作奖励]
    
    E --> L{优先级加成}
    L -->|高优先级| M[+额外奖励]
    L -->|普通优先级| N[基础奖励]
    
    F --> O{优先级惩罚}
    O -->|高优先级| P[-额外惩罚]
    O -->|普通优先级| Q[基础惩罚]
    
    G --> R{延迟程度}
    R -->|轻微| S[线性惩罚]
    R -->|严重| T[二次惩罚]
    
    H --> U{电池状态}
    U -->|>80%| V[能耗奖励]
    U -->|20-80%| W[正常能耗]
    U -->|<20%| X[低电量惩罚]
    
    I --> Y{负载方差}
    Y -->|低方差| Z[均衡奖励]
    Y -->|高方差| AA[不均衡惩罚]
    
    J --> BB[固定惩罚值]
    
    K --> CC{协作类型}
    CC -->|邻居转发| DD[邻居奖励]
    CC -->|云卸载| EE[云奖励]
    CC -->|负载帮助| FF[负载奖励]
    
    M --> GG[汇总总奖励]
    N --> GG
    P --> GG
    Q --> GG
    S --> GG
    T --> GG
    V --> GG
    W --> GG
    X --> GG
    Z --> GG
    AA --> GG
    BB --> GG
    DD --> GG
    EE --> GG
    FF --> GG
    
    GG --> HH[更新累积奖励]
    GG --> II[返回智能体奖励]
```

---

## 6. 配置管理

### 6.1 配置文件层次结构

```
├── src/env/physics_layer/config.yaml          # 系统基础配置
└── src/env/space_env/env_config.yaml          # 环境专用配置
```

### 6.2 系统基础配置 (config.yaml)

```yaml
system:
  start_time: "2024-01-01T00:00:00"
  timeslot_duration_s: 5
  num_timeslots: 1441
  num_leo_satellites: 72
  num_ground_stations: 420  
  num_cloud_centers: 5

orbital:
  satellite_altitude_km: 550
  inclination_deg: 53
  visibility_threshold_km: 2000
  
communication:
  isl_frequency_hz: 1.94e14      # 激光链路
  rf_uplink_frequency_hz: 30e9   # Ka波段上行
  rf_downlink_frequency_hz: 20e9 # Ka波段下行
  
satellite_compute:
  cpu_frequency_ghz: 50
  battery_capacity_mj: 3.6
  solar_power_kw: 5
  energy_threshold_percent: 20
  
cloud_compute:
  cpu_frequency_ghz: 100
  queue_capacity_multiplier: 10
```

### 6.3 环境专用配置 (env_config.yaml)

```yaml
# 观测空间配置
max_queue_size: 20
task_feature_dim: 8  
state_feature_dim: 12
neighbor_feature_dim: 5
max_visible_neighbors: 10
num_cloud_targets: 5

# 奖励权重
reward_weights:
  completion: 10.0
  drop: -5.0
  delay: -0.01
  energy: -0.001
  load_balance: 1.0
  invalid_action: -1.0
  cooperation: 2.0

# 数据路径配置  
data_paths:
  ground_stations: "src/env/env_data/global_ground_stations.csv"
  satellite_data: "src/env/env_data/satellite_data72_1.csv"
  cloud_centers: "src/env/env_data/cloud_centers.csv"
  base_config: "src/env/physics_layer/config.yaml"

# 训练配置
training:
  batch_size: 32
  learning_rate: 3e-4
  gamma: 0.99
  gae_lambda: 0.95
```

### 6.4 配置加载与验证

```python
class Space3PettingZooEnv:
    def _load_and_validate_config(self, config_path: str):
        """配置加载和验证流程"""
        # 1. 加载环境配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.env_config = yaml.safe_load(f)
            
        # 2. 验证配置完整性
        self._validate_env_config(self.env_config)
        
        # 3. 加载系统配置
        system_config_path = self._get_config_path('base_config')
        with open(system_config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
            
        # 4. 合并配置
        self.config['rl_env'] = self.env_config
        
    def _validate_env_config(self, config: Dict):
        """配置验证规则"""
        required_keys = [
            'max_queue_size', 'task_feature_dim', 'state_feature_dim',
            'reward_weights', 'data_paths'
        ]
        
        # 检查必需字段
        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            raise ValueError(f"Missing required config keys: {missing_keys}")
            
        # 检查数值范围
        if config['max_queue_size'] <= 0:
            raise ValueError("max_queue_size must be positive")
            
        # 检查路径配置
        required_paths = ['ground_stations', 'base_config']
        data_paths = config.get('data_paths', {})
        missing_paths = [path for path in required_paths if path not in data_paths]
        if missing_paths:
            raise ValueError(f"Missing required data path keys: {missing_paths}")
```

---

## 7. 性能与优化

### 7.1 计算复杂度分析

#### 7.1.1 主要计算瓶颈
```python
# 1. 轨道动力学计算 - O(N²) 复杂度
def build_visibility_matrix(self, satellites: Dict[str, Satellite]):
    # 72×72 卫星间距离计算: O(72²) = O(5184)
    # 72×420 卫星-地面距离: O(72×420) = O(30240) 
    # 72×5 卫星-云距离: O(72×5) = O(360)

# 2. 通信矩阵计算 - O(N²) 复杂度  
def get_isl_communication_matrix(self, time_step: int):
    # 路径损耗计算: O(N²)
    # SNR计算: O(N²) 
    # 数据速率计算: O(N²)

# 3. 动作验证 - O(N×M) 复杂度
def validate_batch_actions(self, all_actions, all_task_queues, global_visibility):
    # N个智能体 × M个任务 = O(72×20) = O(1440)
```

#### 7.1.2 优化策略

**向量化计算**:
```python
# 使用NumPy向量化替代Python循环
distance_matrix = np.linalg.norm(
    positions1[:, np.newaxis, :] - positions2[np.newaxis, :, :], 
    axis=2
)  # 替代双重循环

# 批量SNR计算
snr_db = rx_power_dbm - noise_power_dbm  # 向量运算
data_rate = bandwidth * np.log2(1 + 10**(snr_db/10))  # 向量化Shannon公式
```

**稀疏矩阵优化**:
```python
from scipy.sparse import csr_matrix

# 可见性矩阵稀疏化（大部分元素为0）
visibility_sparse = csr_matrix(visibility_matrix)
comm_matrix_sparse = csr_matrix(comm_matrix * visibility_matrix)
```

**缓存机制**:
```python
class OrbitalUpdater:
    def __init__(self):
        self._satellite_cache = {}  # 缓存卫星位置
        self._visibility_cache = {}  # 缓存可见性矩阵
        
    def get_satellites_at_time(self, time_step: int):
        if time_step not in self._satellite_cache:
            self._satellite_cache[time_step] = self._calculate_positions(time_step)
        return self._satellite_cache[time_step]
```

### 7.2 内存优化

#### 7.2.1 数据类型优化
```python
# 使用合适的数据类型减少内存占用
visibility_matrix = np.zeros((72, 72), dtype=np.bool_)  # 1位布尔型
distance_matrix = np.zeros((72, 72), dtype=np.float32)  # 32位浮点型
action_mask = np.zeros((20, 16), dtype=np.int8)  # 8位整型
```

#### 7.2.2 批处理策略
```python
# 批量处理任务而不是逐个处理
def process_batch_tasks(self, tasks: List[ComputeTask], duration: float):
    """批量并行处理，减少函数调用开销"""
    completed_tasks = []
    for batch in self._create_batches(tasks, batch_size=50):
        batch_results = self._process_task_batch(batch, duration)
        completed_tasks.extend(batch_results)
    return completed_tasks
```

### 7.3 并行化优化

#### 7.3.1 卫星并行处理
```python
# 卫星计算节点间无依赖，可并行处理
from concurrent.futures import ThreadPoolExecutor

def process_all_satellites_parallel(self, duration: float, illuminated: bool):
    """并行处理所有卫星"""
    with ThreadPoolExecutor(max_workers=8) as executor:
        futures = []
        for satellite in self.satellites.values():
            future = executor.submit(satellite.process_timeslot, duration, illuminated)
            futures.append(future)
        
        results = [future.result() for future in futures]
    return results
```

#### 7.3.2 观测构建并行化
```python
def build_all_observations_parallel(self) -> Dict:
    """并行构建所有智能体观测"""
    with ThreadPoolExecutor(max_workers=4) as executor:
        observation_futures = {}
        for agent_id in self.agents:
            future = executor.submit(
                self.observation_builder.build_observation,
                agent_id, self.satellites[agent_id], self.task_queues[agent_id],
                self.current_visibility_matrices, self.current_comm_matrix,
                self.current_timeslot, self.max_timeslots,
                self._get_satellite_index(agent_id), self.satellites
            )
            observation_futures[agent_id] = future
            
        observations = {
            agent_id: future.result() 
            for agent_id, future in observation_futures.items()
        }
    return observations
```

### 7.4 性能监控

```python
class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.timings = {}
        self.memory_usage = {}
        
    def time_function(self, func_name: str):
        """函数计时装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                
                if func_name not in self.timings:
                    self.timings[func_name] = []
                self.timings[func_name].append(end_time - start_time)
                return result
            return wrapper
        return decorator
        
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        report = {}
        for func_name, times in self.timings.items():
            report[func_name] = {
                'avg_time': np.mean(times),
                'max_time': np.max(times),
                'min_time': np.min(times),
                'call_count': len(times)
            }
        return report
```

### 7.5 扩展性设计

#### 7.5.1 可配置卫星数量
```python
class Space3PettingZooEnv:
    def _setup_scalable_satellites(self):
        """支持可变卫星数量"""
        # 从配置读取卫星数量
        self.num_satellites = self.config['system']['num_leo_satellites']
        
        # 动态创建智能体列表
        self.possible_agents = [f"sat_{111 + i}" for i in range(self.num_satellites)]
        
        # 动态调整观测空间维度
        self.max_visible_neighbors = min(10, self.num_satellites - 1)
```

#### 7.5.2 模块化接口设计
```python
# 抽象基类定义统一接口
class ComputeNodeInterface(ABC):
    @abstractmethod
    def add_task(self, task: Task) -> bool:
        pass
        
    @abstractmethod
    def process_timeslot(self, duration: float) -> ProcessingResult:
        pass
        
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        pass

# 具体实现可以替换而不影响环境主逻辑
class SatelliteCompute(ComputeNodeInterface):
    # 卫星计算实现
    
class EdgeCompute(ComputeNodeInterface):
    # 边缘计算实现（未来扩展）
```

通过这种架构设计，SPACE3环境具有良好的可扩展性、可维护性和性能表现，能够支持大规模多智能体强化学习训练。

---

**文档版本**: v1.0  
**最后更新**: 2024年  
**维护团队**: SPACE3开发团队