"""
负载均衡模块 (Load Balancing Module)

实现区域负载均衡计算，基于24个全球区域对LEO卫星的负载分布进行分析。
每个时隙计算各区域内卫星负载的方差，用于评估负载均衡程度。

核心公式:
- 单星负载: L_s(t) = w_cpu * U_cpu,s(t) + w_q * N_queue,s(t)/N_max
- 区域负载方差: B_k(t) = (1/|S_k(t)|) * Σ(L_s(t) - L̄_k(t))²

Author: SPACE2 Team
Version: 1.0
"""

import json
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

from src.env.Foundation_Layer.time_manager import TimeContext
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.Foundation_Layer.logging_config import get_logger


@dataclass
class RegionalLoadResult:
    """区域负载计算结果"""
    timeslot: int
    regional_load_variances: Dict[int, float]  # 区域ID -> 负载方差
    regional_mean_loads: Dict[int, float]      # 区域ID -> 平均负载
    regional_satellite_counts: Dict[int, int]  # 区域ID -> 卫星数量
    total_regions_active: int                   # 活跃区域数量


@dataclass  
class SatelliteLoadInfo:
    """卫星负载信息"""
    satellite_id: int
    region_id: int
    cpu_utilization: float      # CPU利用率 (0-1)
    queue_length: int          # 任务队列长度
    load_score: float          # 综合负载评分
    latitude: float            # 纬度
    longitude: float           # 经度


class LoadBalancingCalculator:
    """负载均衡计算器"""
    
    def __init__(self, config: Dict[str, Any], regions_file: str = "src/env/env_data/regions.json"):
        """
        初始化负载均衡计算器
        
        Args:
            config: 系统配置字典
            regions_file: 区域定义文件路径
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 负载计算参数
        self.w_cpu = config.get('load_balancing', {}).get('w_cpu', 0.7)
        self.w_queue = config.get('load_balancing', {}).get('w_queue', 0.3)
        self.max_queue_size = config.get('queuing', {}).get('max_queue_size', 100)
        
        # 加载区域定义
        self.regions = self._load_regions(regions_file)
        self.logger.info(f"负载均衡计算器初始化完成，加载{len(self.regions)}个区域定义")
    
    def _load_regions(self, regions_file: str) -> List[Dict]:
        """加载区域定义文件"""
        try:
            with open(regions_file, 'r', encoding='utf-8') as f:
                regions = json.load(f)
            self.logger.debug(f"成功加载{len(regions)}个区域定义")
            return regions
        except Exception as e:
            error_msg = f"加载区域定义文件失败: {regions_file}, 错误: {str(e)}"
            self.logger.error(error_msg)
            raise SpaceSimulationError(
                message=error_msg,
                error_code="REGIONS_LOAD_FAILURE"
            )
    
    @handle_errors(module="load_balancing", function="determine_satellite_region")
    def _determine_satellite_region(self, latitude: float, longitude: float) -> Optional[int]:
        """
        根据卫星位置确定其所属区域
        
        Args:
            latitude: 纬度 (-90 to 90)
            longitude: 经度 (-180 to 180)
            
        Returns:
            区域ID，如果不在任何区域内则返回None
        """
        for region in self.regions:
            lat_range = region['latitude_range']
            lon_range = region['longitude_range']
            
            if (lat_range['min'] <= latitude <= lat_range['max'] and
                lon_range['min'] <= longitude <= lon_range['max']):
                return region['region_id']
        
        # 如果没有找到匹配的区域，记录警告
        self.logger.warning(f"卫星位置({latitude:.3f}, {longitude:.3f})不在任何定义区域内")
        return None
    
    def _group_satellites_by_region(self, satellites_data: List[Dict]) -> Dict[int, List[SatelliteLoadInfo]]:
        """
        按区域对卫星进行分组
        
        Args:
            satellites_data: 卫星数据列表，包含位置和负载信息
            
        Returns:
            区域ID -> 卫星负载信息列表的字典
        """
        regional_satellites = {}
        
        for sat_data in satellites_data:
            # 确定卫星所属区域
            region_id = self._determine_satellite_region(
                sat_data['latitude'], 
                sat_data['longitude']
            )
            
            if region_id is None:
                continue
                
            # 计算卫星负载评分
            load_score = (self.w_cpu * sat_data['cpu_utilization'] + 
                         self.w_queue * sat_data['queue_length'] / self.max_queue_size)
            
            # 创建卫星负载信息对象
            sat_load_info = SatelliteLoadInfo(
                satellite_id=sat_data['satellite_id'],
                region_id=region_id,
                cpu_utilization=sat_data['cpu_utilization'],
                queue_length=sat_data['queue_length'],
                load_score=load_score,
                latitude=sat_data['latitude'],
                longitude=sat_data['longitude']
            )
            
            # 添加到对应区域
            if region_id not in regional_satellites:
                regional_satellites[region_id] = []
            regional_satellites[region_id].append(sat_load_info)
        
        return regional_satellites
    
    @handle_errors(module="load_balancing", function="calculate_regional_load_variance")
    def _calculate_regional_load_variance(self, satellites_in_region: List[SatelliteLoadInfo]) -> Tuple[float, float]:
        """
        计算单个区域内的负载方差
        
        Args:
            satellites_in_region: 区域内卫星负载信息列表
            
        Returns:
            (负载方差, 平均负载)
        """
        if not satellites_in_region:
            return 0.0, 0.0
        
        # 提取所有卫星的负载评分
        loads = [sat.load_score for sat in satellites_in_region]
        
        # 计算平均负载
        mean_load = np.mean(loads)
        
        # 计算负载方差 B_k(t)
        if len(loads) == 1:
            variance = 0.0  # 只有一颗卫星时方差为0
        else:
            variance = np.var(loads, ddof=0)  # 使用总体方差公式
        
        return variance, mean_load
    
    @handle_errors(module="load_balancing", function="calculate_load_balance")
    def calculate_load_balance(self, timeslot: int, satellites_data: List[Dict]) -> RegionalLoadResult:
        """
        计算指定时隙的区域负载均衡结果
        
        Args:
            timeslot: 时隙编号
            satellites_data: 卫星数据列表，每个元素包含:
                - satellite_id: 卫星ID
                - latitude: 纬度
                - longitude: 经度  
                - cpu_utilization: CPU利用率 (0-1)
                - queue_length: 任务队列长度
                
        Returns:
            RegionalLoadResult: 区域负载计算结果
        """
        self.logger.debug(f"开始计算时隙{timeslot}的区域负载均衡")
        
        # 按区域分组卫星
        regional_satellites = self._group_satellites_by_region(satellites_data)
        
        # 计算各区域的负载方差
        regional_variances = {}
        regional_means = {}
        regional_counts = {}
        
        for region_id, satellites_in_region in regional_satellites.items():
            variance, mean_load = self._calculate_regional_load_variance(satellites_in_region)
            
            regional_variances[region_id] = variance
            regional_means[region_id] = mean_load
            regional_counts[region_id] = len(satellites_in_region)
            
            self.logger.debug(
                f"区域{region_id}: {len(satellites_in_region)}颗卫星, "
                f"平均负载={mean_load:.4f}, 方差={variance:.6f}"
            )
        
        # 创建结果对象
        result = RegionalLoadResult(
            timeslot=timeslot,
            regional_load_variances=regional_variances,
            regional_mean_loads=regional_means,
            regional_satellite_counts=regional_counts,
            total_regions_active=len(regional_variances)
        )
        
        self.logger.info(
            f"时隙{timeslot}负载均衡计算完成: "
            f"{result.total_regions_active}个活跃区域, "
            f"平均方差={np.mean(list(regional_variances.values())):.6f}"
        )
        
        return result
    
    def get_load_balance_summary(self, result: RegionalLoadResult) -> Dict[str, Any]:
        """
        获取负载均衡结果摘要
        
        Args:
            result: 区域负载计算结果
            
        Returns:
            包含统计信息的字典
        """
        if not result.regional_load_variances:
            return {
                "timeslot": result.timeslot,
                "total_regions": 0,
                "average_variance": 0.0,
                "max_variance": 0.0,
                "min_variance": 0.0,
                "total_satellites": 0
            }
        
        variances = list(result.regional_load_variances.values())
        total_satellites = sum(result.regional_satellite_counts.values())
        
        return {
            "timeslot": result.timeslot,
            "total_regions": result.total_regions_active,
            "average_variance": np.mean(variances),
            "max_variance": np.max(variances),
            "min_variance": np.min(variances),
            "variance_std": np.std(variances),
            "total_satellites": total_satellites,
            "most_unbalanced_region": max(result.regional_load_variances.items(), key=lambda x: x[1])[0],
            "most_balanced_region": min(result.regional_load_variances.items(), key=lambda x: x[1])[0]
        }